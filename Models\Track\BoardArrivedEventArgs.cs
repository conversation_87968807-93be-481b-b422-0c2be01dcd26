using System;

namespace Nickel_Inspect.Models.Track
{
    /// <summary>
    /// 产品就位事件参数
    /// </summary>
    public class BoardArrivedEventArgs : EventArgs
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        public string BoardId { get; set; }

        /// <summary>
        /// 到达时间
        /// </summary>
        public DateTime ArrivedTime { get; set; }

        /// <summary>
        /// 产品类型
        /// </summary>
        public string BoardType { get; set; }

        /// <summary>
        /// 是否需要检查
        /// </summary>
        public bool NeedInspection { get; set; } = true;

        /// <summary>
        /// 构造函数
        /// </summary>
        public BoardArrivedEventArgs()
        {
            ArrivedTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="boardId">产品ID</param>
        /// <param name="boardType">产品类型</param>
        /// <param name="needInspection">是否需要检查</param>
        public BoardArrivedEventArgs(string boardId, string boardType, bool needInspection = true)
        {
            BoardId = boardId;
            BoardType = boardType;
            NeedInspection = needInspection;
            ArrivedTime = DateTime.Now;
        }
    }
}
