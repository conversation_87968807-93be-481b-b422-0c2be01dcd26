using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.LightControl;
using Nickel_Inspect.Services.Track;
using Nickel_Inspect.ViewModels;
using Nickel_Inspect.Views;
using Nickel_Inspect.Views.Dialogs;
using Prism.DryIoc;
using Prism.Ioc;
using Prism.Modularity;

namespace Nickel_Inspect
{
    public partial class App : PrismApplication
    {
        /// <summary>
        /// 启动界面对话框
        /// </summary>
        private StartupSplashDialog _splashDialog;

        //声明一个静态 Mutex 变量
        private static Mutex _mutex = null;
        // 定义唯一的 Mutex 名称。使用 GUID 可以确保唯一性。
        // 建议结合你的应用程序名称或一个固定的 GUID。
        private const string MutexName = "Nickel_Inspect_SingleInstanceMutex"; 

        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // 注册基础服务
            containerRegistry.RegisterSingleton<BkMotionCard>();
            containerRegistry.RegisterSingleton<DatabaseService>();
            containerRegistry.RegisterSingleton<ILogService, LogService>();

            // 注册配置帮助类 - 改为先初始化再注册
            try
            {
                MachineConfigurationHelper.Initialize("DeviceConfiguration.json");
                containerRegistry.RegisterInstance(
                    typeof(MachineConfigurationHelper),
                    MachineConfigurationHelper.Instance
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载机器配置失败：{ex.Message}，程序可能无法正常运行",
                    "配置错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                // 创建一个空配置以防止空引用异常
                containerRegistry.RegisterSingleton<MachineConfigurationHelper>();
            }

            // 注册状态管理器 - 负责管理所有设备状态和指示灯
            containerRegistry.RegisterSingleton<StatusManager>();

            //注册光源控制服务
            containerRegistry.RegisterSingleton<ILightControlService, LightControlService>();

            // 注册检测服务
            containerRegistry.RegisterSingleton<IInspectionService, InspectionService>();

            // 注册日志查询视图模型
            containerRegistry.RegisterSingleton<LogQueryViewModel>();

            // 注册轨道控制服务
            containerRegistry.RegisterSingleton<ITrackService, TrackService>();
            containerRegistry.RegisterSingleton<ITrackConfigService, TrackConfigService>();

            containerRegistry.RegisterDialog<AddInspectPoint, AddInspectPointViewModel>();
            containerRegistry.RegisterDialog<EditInspectPoint, EditInspectPointViewModel>();
            containerRegistry.RegisterDialog<TrackControlView, TrackControlViewModel>();
            containerRegistry.RegisterDialog<NewProductWindow, NewProductViewModel>();
            containerRegistry.RegisterSingleton<InspectionViewModel>();

            // 注册VisionPro服务
            containerRegistry.RegisterSingleton<IVisionProService, VisionProService>();

            // 注册VisionPro测试视图模型
            containerRegistry.RegisterSingleton<VisionProTestViewModel>();

            // 注册检查点配置视图模型
            containerRegistry.RegisterSingleton<ConfigPointViewModel>();

            // 注册ModbusConfig类
            containerRegistry.RegisterInstance<ModbusConfig>(LoadModbusConfig());

            // 注册主窗口视图模型
            containerRegistry.RegisterSingleton<MainWindowViewModel>();
        }

        /// <summary>
        /// 重写Application启动方法，显示启动画面
        /// </summary>
        protected override void OnStartup(StartupEventArgs e)
        {
            // 检查是否已有实例运行
            bool createdNew;
            _mutex = new Mutex(true, MutexName, out createdNew);

            if (!createdNew)
            {
                MessageBox.Show(
                    "程序已在运行中，不能重复启动",
                    "提示",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning
                );
                Current.Shutdown();
                return;
            }

            // 显示启动界面
            ShowSplashScreen();

            base.OnStartup(e);
        }

        /// <summary>
        /// 显示启动画面
        /// </summary>
        private void ShowSplashScreen()
        {
            _splashDialog = new StartupSplashDialog();
            _splashDialog.Show();
        }

        protected override void OnInitialized()
        {
            base.OnInitialized();

            // 初始化报警系统
            InitializeAlarmSystem();

            // 注册应用程序退出事件
            Application.Current.Exit += Current_Exit;

            // 获取主窗口并添加加载事件，用于关闭启动界面
            if (MainWindow is MainWindow mainWindow)
            {
                // 先移除可能已存在的事件处理器，避免重复订阅
                mainWindow.Loaded -= MainWindow_Loaded;
                mainWindow.Loaded += MainWindow_Loaded;

                // 如果窗口已经加载完成，直接调用事件处理方法
                if (mainWindow.IsLoaded)
                {
                    MainWindow_Loaded(mainWindow, new RoutedEventArgs());
                }
                // 添加备用计时器，确保启动画面会被关闭
                else
                {
                    System.Windows.Threading.DispatcherTimer timer =
                        new System.Windows.Threading.DispatcherTimer();
                    timer.Interval = TimeSpan.FromSeconds(10);
                    timer.Tick += (s, e) =>
                    {
                        timer.Stop();
                        CloseSplashScreen();
                    };
                    timer.Start();
                }
            }
        }

        /// <summary>
        /// 关闭启动画面
        /// </summary>
        private void CloseSplashScreen()
        {
            if (_splashDialog != null)
            {
                _splashDialog.CloseSplash();
                _splashDialog = null;
            }
        }

        /// <summary>
        /// 主窗口加载完成事件处理
        /// </summary>
        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 主窗口加载完成后，等待1秒再关闭启动界面，让用户有足够时间看到"准备就绪"的信息
            await Task.Delay(1000);

            // 关闭启动界面
            CloseSplashScreen();
        }

        private void Current_Exit(object sender, ExitEventArgs e)
        {
            // 应用程序退出前清理资源
            try
            {
                if (Container.IsRegistered<StatusManager>())
                {
                    var statusManager = Container.Resolve<StatusManager>();
                    Task.Run(() =>
                        {
                            try
                            {
                                statusManager.Dispose();
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"退出时清理资源失败: {ex.Message}");
                            }
                        })
                        .Wait(2000); // 最多等待2秒
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"退出事件处理异常: {ex.Message}");
            }
        }

        private ModbusConfig LoadModbusConfig()
        {
            try
            {
                string configPath = "ModbusConfig.json";
                string jsonContent = System.IO.File.ReadAllText(configPath);
                return Newtonsoft.Json.JsonConvert.DeserializeObject<ModbusConfig>(jsonContent);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载ModbusConfig配置文件失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                return new ModbusConfig();
            }
        }

        private MachineConfigurationHelper InitializeMachineConfiguration()
        {
            try
            {
                MachineConfigurationHelper.Initialize("DeviceConfiguration.json");
                return MachineConfigurationHelper.Instance;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载机器配置失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                throw;
            }
        }

        private void InitializeAlarmSystem()
        {
            try
            {
                // 获取必要的服务
                var motionCard = Container.Resolve<BkMotionCard>();
                var logService = Container.Resolve<ILogService>();
                var configHelper = Container.Resolve<MachineConfigurationHelper>();
                var statusManager = Container.Resolve<StatusManager>();

                // 检查并更新配置文件中的报警配置
                Nickel_Inspect.Services.Alarms.AlarmSystemHelper.UpdateAlarmsConfiguration();

                // 先检查是否已经初始化过报警系统
                var existingAlarmManager = Nickel_Inspect.Services.Alarms.AlarmSystemHelper.Current;
                if (existingAlarmManager != null)
                {
                    // 如果已经初始化过，直接注册到容器中
                    ((IContainerRegistry)Container).RegisterInstance(
                        typeof(Nickel_Inspect.Services.Alarms.AlarmManager),
                        existingAlarmManager
                    );
                    // 确保状态管理器也与报警管理器集成
                    statusManager.SetAlarmManager(existingAlarmManager);
                    logService.LogInformation("使用已初始化的报警系统实例", "应用程序");
                    return;
                }

                // 获取InspectionViewModel，后面用于接收报警系统日志
                InspectionViewModel inspectionViewModel = null;
                try
                {
                    inspectionViewModel = Container.Resolve<InspectionViewModel>();
                }
                catch
                {
                    // 如果无法解析InspectionViewModel，忽略错误
                    logService.LogWarning(
                        "无法解析InspectionViewModel，报警系统日志将不会显示在界面上",
                        "应用程序"
                    );
                }

                // 创建日志接收处理方法
                EventHandler<Nickel_Inspect.Services.Alarms.LogMessageEventArgs> logReceiver = null;
                if (inspectionViewModel != null)
                {
                    logReceiver = (sender, e) =>
                    {
                        inspectionViewModel.AddLogMessage(e.Type, e.Message, "报警系统");
                    };
                }

                // 从ConfigHelper初始化报警系统，并传入状态管理器
                var alarmManager =
                    Nickel_Inspect.Services.Alarms.AlarmSystemHelper.InitializeAlarmSystemFromConfigHelper(
                        configHelper,
                        motionCard,
                        logService,
                        statusManager,
                        logReceiver,
                        (IContainerRegistry)Container // 传递容器注册表，让Helper自动注册
                    );

                // 由于状态管理器已经集成了报警管理器，不再需要在这里订阅报警事件
                logService.LogInformation("报警系统与状态管理器集成完成", "应用程序");
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"初始化报警系统时发生错误: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }
    }
}
