<UserControl
    x:Class="Nickel_Inspect.Views.EditInspectPoint"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    Width="800"
    Height="700"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="{DynamicResource RegionBrush}"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="{DynamicResource RegionBrush}" CornerRadius="5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题  -->
            <TextBlock
                Margin="0,0,0,20"
                Foreground="{DynamicResource PrimaryTextBrush}"
                Style="{StaticResource TextBlockLargeBold}"
                Text="编辑检查点" />

            <!--  表单内容  -->
            <StackPanel Grid.Row="1" Margin="0,0,0,20">
                <!--  机种选择  -->
                <DockPanel Margin="0,5">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Width="120"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="机种名称：" />
                        <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="{Binding SelectedModel.ModelName}" />
                    </StackPanel>
                </DockPanel>

                <!--  点位名称（只读）  -->
                <DockPanel Margin="0,5">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock
                            Width="120"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="点位名称：" />
                        <TextBlock
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Style="{StaticResource TextBlockBaseStyle}"
                            Text="{Binding PointName}" />
                    </StackPanel>
                </DockPanel>

                <!--  坐标编辑  -->
                <GroupBox
                    Margin="0,10"
                    Header="坐标编辑"
                    Style="{StaticResource GroupBoxBaseStyle}">
                    <Grid Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            Margin="0,0,5,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="X:" />
                        <TextBox
                            Grid.Column="1"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding CurrentX}" />

                        <TextBlock
                            Grid.Column="2"
                            Margin="10,0,5,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="Y:" />
                        <TextBox
                            Grid.Column="3"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding CurrentY}" />

                        <TextBlock
                            Grid.Column="4"
                            Margin="10,0,5,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="Z:" />
                        <TextBox
                            Grid.Column="5"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding CurrentZ}" />
                    </Grid>
                </GroupBox>

                <!--  检查文件路径  -->
                <DockPanel Margin="0,5">
                    <TextBlock
                        Width="120"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="检查文件路径：" />
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBox
                            Grid.Column="0"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding InspectionFilePath}" />
                        <Button
                            Grid.Column="1"
                            Margin="5,0,0,0"
                            Command="{Binding BrowseFileCommand}"
                            Content="浏览..."
                            Style="{StaticResource ButtonDefault}" />
                    </Grid>
                </DockPanel>

                <!--  是否激活  -->
                <DockPanel Margin="0,10">
                    <TextBlock
                        Width="120"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="是否激活：" />
                    <CheckBox VerticalAlignment="Center" IsChecked="{Binding IsActive}" />
                </DockPanel>

                <!--  光源亮度控制  -->
                <GroupBox
                    Margin="0,10"
                    Header="光源亮度控制"
                    Style="{StaticResource GroupBoxBaseStyle}">
                    <StackPanel>
                        <!--  光源控制器选择  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="120"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="光源控制器：" />
                            <ComboBox
                                MinWidth="150"
                                DisplayMemberPath="Name"
                                ItemsSource="{Binding LightControllers}"
                                SelectedValue="{Binding LightControllerId}"
                                SelectedValuePath="ControllerId"
                                Style="{StaticResource ComboBoxBaseStyle}" />
                        </DockPanel>

                        <!--  红色通道亮度  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="120"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="红色通道亮度：" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="50" />
                                </Grid.ColumnDefinitions>
                                <Slider
                                    Grid.Column="0"
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding RedChannelBrightness}" />

                                <TextBlock
                                    Grid.Column="1"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    Text="{Binding RedChannelBrightness}" />
                            </Grid>
                        </DockPanel>

                        <!--  绿色通道亮度  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="120"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="绿色通道亮度：" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="50" />
                                </Grid.ColumnDefinitions>
                                <Slider
                                    Grid.Column="0"
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding GreenChannelBrightness}" />

                                <TextBlock
                                    Grid.Column="1"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    Text="{Binding GreenChannelBrightness}" />
                            </Grid>
                        </DockPanel>

                        <!--  蓝色通道亮度  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="120"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="蓝色通道亮度：" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="50" />
                                </Grid.ColumnDefinitions>
                                <Slider
                                    Grid.Column="0"
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding BlueChannelBrightness}" />

                                <TextBlock
                                    Grid.Column="1"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    Text="{Binding BlueChannelBrightness}" />
                            </Grid>
                        </DockPanel>

                        <!--  白光通道亮度  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="120"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="白光通道亮度：" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="50" />
                                </Grid.ColumnDefinitions>
                                <Slider
                                    Grid.Column="0"
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding WhiteChannelBrightness}" />

                                <TextBlock
                                    Grid.Column="1"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    Text="{Binding WhiteChannelBrightness}" />
                            </Grid>
                        </DockPanel>

                        <!--  自定义通道设置  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="120"
                                VerticalAlignment="Center"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                Text="自定义通道设置：" />
                            <TextBox
                                Style="{StaticResource TextBoxBaseStyle}"
                                Text="{Binding CustomChannelSettings}"
                                ToolTip="格式：通道号:亮度值，例如 1:120,2:200,3:50" />
                        </DockPanel>
                    </StackPanel>
                </GroupBox>
            </StackPanel>

            <!--  按钮区域  -->
            <StackPanel
                Grid.Row="2"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    Width="80"
                    Height="35"
                    Margin="0,0,10,0"
                    Command="{Binding SaveCommand}"
                    Content="保存"
                    Style="{StaticResource ButtonPrimary}" />
                <Button
                    Width="80"
                    Height="35"
                    Command="{Binding CancelCommand}"
                    Content="取消"
                    Style="{StaticResource ButtonDefault}" />
            </StackPanel>
        </Grid>
    </Border>
</UserControl> 