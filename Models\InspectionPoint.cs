﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Nickel_Inspect.Models
{
    public class InspectionPoint
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int PointId { get; set; }

        /// <summary>
        /// 机种ID
        /// </summary>
        public int ModelId { get; set; }

        /// <summary>
        /// 检查点名称
        /// </summary>
        public string PointName { get; set; }

        /// <summary>
        /// 检查点编码
        /// </summary>
        public string PointCode { get; set; }

        /// <summary>
        /// 检查点序号
        /// </summary>
        public int SequenceNo { get; set; }

        /// <summary>
        /// 检查点X坐标
        /// </summary>
        public decimal XPosition { get; set; }

        /// <summary>
        /// 检查点Y坐标
        /// </summary>
        public decimal YPosition { get; set; }

        // <summary>
        /// 检查点Z坐标
        /// </summary>
        public decimal ZPosition { get; set; }

        /// <summary>
        /// 触发IO端口
        /// </summary>
        public int TriggerIOPort { get; set; }

        /// <summary>
        /// 超时时间
        /// </summary>
        public int TimeoutSeconds { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 检查文件路径
        /// </summary>
        public string InspectionFilePath { get; set; }

        /// <summary>
        /// 光源控制器ID
        /// </summary>
        public int LightControllerId { get; set; }

        /// <summary>
        /// 红色通道亮度值 (0-255)
        /// </summary>
        public int RedChannelBrightness { get; set; }

        /// <summary>
        /// 绿色通道亮度值 (0-255)
        /// </summary>
        public int GreenChannelBrightness { get; set; }

        /// <summary>
        /// 蓝色通道亮度值 (0-255)
        /// </summary>
        public int BlueChannelBrightness { get; set; }

        /// <summary>
        /// 白光通道亮度值 (0-255)，某些光源只有单通道
        /// </summary>
        public int WhiteChannelBrightness { get; set; }

        /// <summary>
        /// 自定义通道亮度设置，用于支持多通道或特殊光源
        /// 格式：通道号:亮度值，例如 "1:120,2:200,3:50"
        /// </summary>
        public string CustomChannelSettings { get; set; }
    }
}
