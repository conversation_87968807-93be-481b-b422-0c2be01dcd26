using System;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Prism.Ioc;

namespace Nickel_Inspect.Services.Alarms
{
    /// <summary>
    /// 报警系统帮助类，提供初始化报警系统的静态方法
    /// </summary>
    public static class AlarmSystemHelper
    {
        // 静态单例引用
        private static AlarmManager _instance;
        private static readonly object _syncLock = new object();
        private static ILogService _logger;

        /// <summary>
        /// 获取当前的报警管理器实例，如果尚未初始化则返回null
        /// </summary>
        public static AlarmManager Current => _instance;

        /// <summary>
        /// 从配置文件初始化报警系统
        /// </summary>
        /// <param name="configFilePath">配置文件路径</param>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        /// <param name="statusManager">状态管理器，可选</param>
        /// <param name="logReceiver">可选的日志接收器</param>
        /// <param name="containerRegistry">可选的容器注册表，用于注册单例</param>
        /// <returns>报警管理器</returns>
        public static AlarmManager InitializeAlarmSystemFromFile(
            string configFilePath,
            BkMotionCard motionCard,
            ILogService logService,
            StatusManager statusManager = null,
            EventHandler<LogMessageEventArgs> logReceiver = null,
            IContainerRegistry containerRegistry = null
        )
        {
            // 检查是否已经初始化过
            if (_instance != null)
            {
                logService.LogInformation("报警系统已经初始化，将返回现有实例", "报警系统");
                return _instance;
            }

            if (string.IsNullOrEmpty(configFilePath))
                throw new ArgumentNullException(nameof(configFilePath));

            if (motionCard == null)
                throw new ArgumentNullException(nameof(motionCard));

            if (logService == null)
                throw new ArgumentNullException(nameof(logService));

            try
            {
                if (!File.Exists(configFilePath))
                {
                    logService.LogError($"配置文件不存在: {configFilePath}", "报警系统");
                    throw new FileNotFoundException($"配置文件不存在: {configFilePath}");
                }

                string jsonContent = File.ReadAllText(configFilePath);
                JObject config = JObject.Parse(jsonContent);

                return InitializeAlarmSystem(
                    config,
                    motionCard,
                    logService,
                    statusManager,
                    logReceiver,
                    containerRegistry
                );
            }
            catch (Exception ex)
            {
                logService.LogError(
                    ex,
                    $"从配置文件初始化报警系统失败: {configFilePath}",
                    "报警系统"
                );
                throw;
            }
        }

        /// <summary>
        /// 从配置对象初始化报警系统
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        /// <param name="statusManager">状态管理器，可选</param>
        /// <param name="logReceiver">可选的日志接收器</param>
        /// <param name="containerRegistry">可选的容器注册表，用于注册单例</param>
        /// <returns>报警管理器</returns>
        public static AlarmManager InitializeAlarmSystem(
            JObject config,
            BkMotionCard motionCard,
            ILogService logService,
            StatusManager statusManager = null,
            EventHandler<LogMessageEventArgs> logReceiver = null,
            IContainerRegistry containerRegistry = null
        )
        {
            // 使用双重检查锁定模式确保线程安全
            if (_instance != null)
            {
                logService.LogInformation("报警系统已经初始化，将返回现有实例", "报警系统");
                return _instance;
            }

            lock (_syncLock)
            {
                if (_instance != null)
                {
                    return _instance;
                }

                if (config == null)
                    throw new ArgumentNullException(nameof(config));

                if (motionCard == null)
                    throw new ArgumentNullException(nameof(motionCard));

                if (logService == null)
                    throw new ArgumentNullException(nameof(logService));

                try
                {
                    // 配置预处理 - 如果是从ConfigHelper获取的配置，可能需要特殊处理
                    PreprocessConfig(config);

                    var initializer = new AlarmSystemInitializer(config, motionCard, logService);

                    // 如果提供了日志接收器，订阅日志事件
                    if (logReceiver != null)
                    {
                        initializer.LogMessageReceived += logReceiver;
                    }

                    initializer.Initialize();
                    _instance = initializer.GetAlarmManager();

                    // 如果提供了状态管理器，设置报警管理器
                    if (statusManager != null)
                    {
                        statusManager.SetAlarmManager(_instance);
                        logService.LogInformation("已将报警管理器与状态管理器集成", "报警系统");
                    }

                    // 如果提供了容器注册表，注册为单例
                    if (containerRegistry != null)
                    {
                        containerRegistry.RegisterInstance(typeof(AlarmManager), _instance);
                        logService.LogInformation("已将报警管理器注册为容器单例", "报警系统");
                    }

                    return _instance;
                }
                catch (Exception ex)
                {
                    logService.LogError(ex, "初始化报警系统失败", "报警系统");
                    throw;
                }
            }
        }

        /// <summary>
        /// 从MachineConfigurationHelper初始化报警系统
        /// </summary>
        /// <param name="configHelper">配置帮助器</param>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        /// <param name="statusManager">状态管理器，可选</param>
        /// <param name="logReceiver">可选的日志接收器</param>
        /// <param name="containerRegistry">可选的容器注册表，用于注册单例</param>
        /// <returns>报警管理器</returns>
        public static AlarmManager InitializeAlarmSystemFromConfigHelper(
            MachineConfigurationHelper configHelper,
            BkMotionCard motionCard,
            ILogService logService,
            StatusManager statusManager = null,
            EventHandler<LogMessageEventArgs> logReceiver = null,
            IContainerRegistry containerRegistry = null
        )
        {
            _logger = logService;

            try
            {
                // 获取配置对象
                JObject config = configHelper.GetConfigAsJObject();

                // 预处理配置
                PreprocessConfig(config);

                return InitializeAlarmSystem(
                    config,
                    motionCard,
                    logService,
                    statusManager,
                    logReceiver,
                    containerRegistry
                );
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "从ConfigHelper初始化报警系统失败", "报警系统");
                throw;
            }
        }

        /// <summary>
        /// 预处理配置对象
        /// </summary>
        private static void PreprocessConfig(JObject config)
        {
            try
            {
                if (config == null)
                {
                    _logger?.LogError("无法预处理空的配置对象");
                    return;
                }

                // 检查是否已存在alarms节点
                if (config["Alarms"] == null)
                {
                    _logger?.LogInformation("配置中不存在Alarms节点，创建新节点");
                    config["Alarms"] = new JObject();
                }

                JObject alarms = (JObject)config["Alarms"];

                // 检查是否已存在ServoAlarms节点
                if (alarms["ServoAlarms"] == null)
                {
                    _logger?.LogInformation("配置中不存在ServoAlarms节点，创建新节点");
                    alarms["ServoAlarms"] = new JObject();

                    // 添加伺服报警配置
                    var servoAlarms = (JObject)alarms["ServoAlarms"];

                    // X轴伺服报警
                    var xAxisServoAlarm = new JObject();
                    xAxisServoAlarm["BitIndex"] = 0;
                    xAxisServoAlarm["Name"] = "X轴伺服报警";
                    xAxisServoAlarm["Type"] = "IO";
                    xAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示X轴伺服报警
                    xAxisServoAlarm["Description"] = "X轴伺服驱动器异常报警";
                    servoAlarms["xAxisServoAlarm"] = xAxisServoAlarm;

                    // Y轴伺服报警
                    var yAxisServoAlarm = new JObject();
                    yAxisServoAlarm["BitIndex"] = 1;
                    yAxisServoAlarm["Name"] = "Y轴伺服报警";
                    yAxisServoAlarm["Type"] = "IO";
                    yAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示Y轴伺服报警
                    yAxisServoAlarm["Description"] = "Y轴伺服驱动器异常报警";
                    servoAlarms["yAxisServoAlarm"] = yAxisServoAlarm;

                    // Z轴伺服报警
                    var zAxisServoAlarm = new JObject();
                    zAxisServoAlarm["BitIndex"] = 2;
                    zAxisServoAlarm["Name"] = "Z轴伺服报警";
                    zAxisServoAlarm["Type"] = "IO";
                    zAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示Z轴伺服报警
                    zAxisServoAlarm["Description"] = "Z轴伺服驱动器异常报警";
                    servoAlarms["zAxisServoAlarm"] = zAxisServoAlarm;

                    _logger?.LogInformation("成功添加默认伺服报警配置");
                }

                // 检查是否已存在MotionAlarms节点
                if (alarms["MotionAlarms"] == null)
                {
                    _logger?.LogInformation("配置中不存在MotionAlarms节点，创建新节点");
                    alarms["MotionAlarms"] = new JObject();

                    // 添加运动报警配置
                    var motionAlarms = (JObject)alarms["MotionAlarms"];

                    // 运动报警复位配置
                    var resetMotionAlarm = new JObject();
                    resetMotionAlarm["BitIndex"] = 0;
                    resetMotionAlarm["Name"] = "运动报警复位";
                    resetMotionAlarm["Type"] = "Register";
                    resetMotionAlarm["IoAddress"] = 16; // 使用D16寄存器作为运动报警复位
                    resetMotionAlarm["Description"] = "写入1到D16寄存器复位所有运动报警";
                    motionAlarms["resetMotionAlarm"] = resetMotionAlarm;

                    // 控制卡总异常
                    var cardTotalError = new JObject();
                    cardTotalError["BitIndex"] = 0;
                    cardTotalError["Name"] = "控制卡总异常";
                    cardTotalError["Type"] = "Register";
                    cardTotalError["IoAddress"] = 17; // 使用D17寄存器检测总异常
                    cardTotalError["Description"] = "控制卡总异常，1代表有异常";
                    motionAlarms["cardTotalError"] = cardTotalError;

                    // X轴复位异常
                    var xAxisHomeError = new JObject();
                    xAxisHomeError["BitIndex"] = 0;
                    xAxisHomeError["Name"] = "X轴复位异常";
                    xAxisHomeError["Type"] = "Register";
                    xAxisHomeError["IoAddress"] = 21; // 使用D21寄存器
                    xAxisHomeError["Description"] = "X轴复位过程中发生异常";
                    motionAlarms["xAxisHomeError"] = xAxisHomeError;

                    // X轴移动异常
                    var xAxisMoveError = new JObject();
                    xAxisMoveError["BitIndex"] = 0;
                    xAxisMoveError["Name"] = "X轴移动异常";
                    xAxisMoveError["Type"] = "Register";
                    xAxisMoveError["IoAddress"] = 22; // 使用D22寄存器
                    xAxisMoveError["Description"] = "X轴移动过程中发生异常";
                    motionAlarms["xAxisMoveError"] = xAxisMoveError;

                    // Y轴复位异常
                    var yAxisHomeError = new JObject();
                    yAxisHomeError["BitIndex"] = 0;
                    yAxisHomeError["Name"] = "Y轴复位异常";
                    yAxisHomeError["Type"] = "Register";
                    yAxisHomeError["IoAddress"] = 37; // 使用D37寄存器
                    yAxisHomeError["Description"] = "Y轴复位过程中发生异常";
                    motionAlarms["yAxisHomeError"] = yAxisHomeError;

                    // Y轴移动异常
                    var yAxisMoveError = new JObject();
                    yAxisMoveError["BitIndex"] = 0;
                    yAxisMoveError["Name"] = "Y轴移动异常";
                    yAxisMoveError["Type"] = "Register";
                    yAxisMoveError["IoAddress"] = 38; // 使用D38寄存器
                    yAxisMoveError["Description"] = "Y轴移动过程中发生异常";
                    motionAlarms["yAxisMoveError"] = yAxisMoveError;

                    // Z轴复位异常
                    var zAxisHomeError = new JObject();
                    zAxisHomeError["BitIndex"] = 0;
                    zAxisHomeError["Name"] = "Z轴复位异常";
                    zAxisHomeError["Type"] = "Register";
                    zAxisHomeError["IoAddress"] = 53; // 使用D53寄存器
                    zAxisHomeError["Description"] = "Z轴复位过程中发生异常";
                    motionAlarms["zAxisHomeError"] = zAxisHomeError;

                    // Z轴移动异常
                    var zAxisMoveError = new JObject();
                    zAxisMoveError["BitIndex"] = 0;
                    zAxisMoveError["Name"] = "Z轴移动异常";
                    zAxisMoveError["Type"] = "Register";
                    zAxisMoveError["IoAddress"] = 54; // 使用D54寄存器
                    zAxisMoveError["Description"] = "Z轴移动过程中发生异常";
                    motionAlarms["zAxisMoveError"] = zAxisMoveError;

                    _logger?.LogInformation("成功添加默认运动报警配置");
                }

                // 保存预处理后的配置
                SaveConfig(config);
                _logger?.LogInformation("预处理配置成功完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"预处理配置时发生异常: {ex.Message}", "[AlarmSystemHelper]");
            }
        }

        /// <summary>
        /// 从MachineConfig中创建报警系统配置
        /// </summary>
        /// <param name="machineConfig">机器配置对象</param>
        /// <returns>报警系统配置对象</returns>
        private static JObject CreateConfigFromMachineConfig(JObject machineConfig)
        {
            try
            {
                _logger?.LogInformation("开始从机器配置创建报警系统配置");
                var config = new JObject();

                // 创建alarms对象
                var alarms = new JObject();

                // 创建servoAlarms对象 - 仅包含伺服报警
                var servoAlarms = new JObject();

                // X轴伺服报警
                var xAxisServoAlarm = new JObject();
                xAxisServoAlarm["BitIndex"] = 0;
                xAxisServoAlarm["Name"] = "X轴伺服报警";
                xAxisServoAlarm["Type"] = "IO";
                xAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示X轴伺服报警
                xAxisServoAlarm["Description"] = "X轴伺服驱动器异常报警";
                servoAlarms["xAxisServoAlarm"] = xAxisServoAlarm;

                // Y轴伺服报警
                var yAxisServoAlarm = new JObject();
                yAxisServoAlarm["BitIndex"] = 1;
                yAxisServoAlarm["Name"] = "Y轴伺服报警";
                yAxisServoAlarm["Type"] = "IO";
                yAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示Y轴伺服报警
                yAxisServoAlarm["Description"] = "Y轴伺服驱动器异常报警";
                servoAlarms["yAxisServoAlarm"] = yAxisServoAlarm;

                // Z轴伺服报警
                var zAxisServoAlarm = new JObject();
                zAxisServoAlarm["BitIndex"] = 2;
                zAxisServoAlarm["Name"] = "Z轴伺服报警";
                zAxisServoAlarm["Type"] = "IO";
                zAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示Z轴伺服报警
                zAxisServoAlarm["Description"] = "Z轴伺服驱动器异常报警";
                servoAlarms["zAxisServoAlarm"] = zAxisServoAlarm;

                // 创建motionAlarms对象 - 包含运动报警和复位配置
                var motionAlarms = new JObject();

                // 运动报警复位配置
                var resetMotionAlarm = new JObject();
                resetMotionAlarm["BitIndex"] = 0;
                resetMotionAlarm["Name"] = "运动报警复位";
                resetMotionAlarm["Type"] = "Register";
                resetMotionAlarm["IoAddress"] = 16; // 使用D16寄存器作为运动报警复位
                resetMotionAlarm["Description"] = "写入1到D16寄存器复位所有运动报警";
                motionAlarms["resetMotionAlarm"] = resetMotionAlarm;

                // 控制卡总异常
                var cardTotalError = new JObject();
                cardTotalError["BitIndex"] = 0;
                cardTotalError["Name"] = "控制卡总异常";
                cardTotalError["Type"] = "Register";
                cardTotalError["IoAddress"] = 17; // 使用D17寄存器检测总异常
                cardTotalError["Description"] = "控制卡总异常，1代表有异常";
                motionAlarms["cardTotalError"] = cardTotalError;

                // X轴复位异常
                var xAxisHomeError = new JObject();
                xAxisHomeError["BitIndex"] = 0;
                xAxisHomeError["Name"] = "X轴复位异常";
                xAxisHomeError["Type"] = "Register";
                xAxisHomeError["IoAddress"] = 21; // 使用D21寄存器
                xAxisHomeError["Description"] = "X轴复位过程中发生异常";
                motionAlarms["xAxisHomeError"] = xAxisHomeError;

                // X轴移动异常
                var xAxisMoveError = new JObject();
                xAxisMoveError["BitIndex"] = 0;
                xAxisMoveError["Name"] = "X轴移动异常";
                xAxisMoveError["Type"] = "Register";
                xAxisMoveError["IoAddress"] = 22; // 使用D22寄存器
                xAxisMoveError["Description"] = "X轴移动过程中发生异常";
                motionAlarms["xAxisMoveError"] = xAxisMoveError;

                // Y轴复位异常
                var yAxisHomeError = new JObject();
                yAxisHomeError["BitIndex"] = 0;
                yAxisHomeError["Name"] = "Y轴复位异常";
                yAxisHomeError["Type"] = "Register";
                yAxisHomeError["IoAddress"] = 37; // 使用D37寄存器
                yAxisHomeError["Description"] = "Y轴复位过程中发生异常";
                motionAlarms["yAxisHomeError"] = yAxisHomeError;

                // Y轴移动异常
                var yAxisMoveError = new JObject();
                yAxisMoveError["BitIndex"] = 0;
                yAxisMoveError["Name"] = "Y轴移动异常";
                yAxisMoveError["Type"] = "Register";
                yAxisMoveError["IoAddress"] = 38; // 使用D38寄存器
                yAxisMoveError["Description"] = "Y轴移动过程中发生异常";
                motionAlarms["yAxisMoveError"] = yAxisMoveError;

                // Z轴复位异常
                var zAxisHomeError = new JObject();
                zAxisHomeError["BitIndex"] = 0;
                zAxisHomeError["Name"] = "Z轴复位异常";
                zAxisHomeError["Type"] = "Register";
                zAxisHomeError["IoAddress"] = 53; // 使用D53寄存器
                zAxisHomeError["Description"] = "Z轴复位过程中发生异常";
                motionAlarms["zAxisHomeError"] = zAxisHomeError;

                // Z轴移动异常
                var zAxisMoveError = new JObject();
                zAxisMoveError["BitIndex"] = 0;
                zAxisMoveError["Name"] = "Z轴移动异常";
                zAxisMoveError["Type"] = "Register";
                zAxisMoveError["IoAddress"] = 54; // 使用D54寄存器
                zAxisMoveError["Description"] = "Z轴移动过程中发生异常";
                motionAlarms["zAxisMoveError"] = zAxisMoveError;

                // 添加servoAlarms和motionAlarms到alarms
                alarms["ServoAlarms"] = servoAlarms;
                alarms["MotionAlarms"] = motionAlarms;

                // 添加alarms到根配置
                config["Alarms"] = alarms;

                _logger?.LogInformation("成功从机器配置创建报警系统配置");
                return config;
            }
            catch (Exception ex)
            {
                _logger?.LogError(
                    $"从机器配置创建报警系统配置时发生异常: {ex.Message}",
                    "[AlarmSystemHelper]"
                );
                return new JObject();
            }
        }

        /// <summary>
        /// 重置报警系统单例，主要用于测试
        /// </summary>
        internal static void Reset()
        {
            lock (_syncLock)
            {
                if (_instance != null && _instance is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                _instance = null;
            }
        }

        /// <summary>
        /// 更新报警配置
        /// </summary>
        /// <returns>更新是否成功</returns>
        public static bool UpdateAlarmsConfiguration()
        {
            try
            {
                _logger?.LogInformation("开始更新报警配置");

                // 尝试读取现有配置
                JObject config = ReadConfig();
                if (config == null)
                {
                    _logger?.LogWarning("读取配置文件失败，将创建新的配置");
                    config = new JObject();
                }

                // 确保alarms存在
                if (config["Alarms"] == null)
                {
                    config["Alarms"] = new JObject();
                }

                JObject alarms = (JObject)config["Alarms"];

                // 检查是否已有完整的报警配置
                bool hasServoAlarms = alarms["ServoAlarms"] != null;
                bool hasMotionAlarms = alarms["MotionAlarms"] != null;

                if (hasServoAlarms && hasMotionAlarms)
                {
                    _logger?.LogInformation("已存在完整的报警配置，不需要更新");
                    return true;
                }

                // 创建或更新alarms配置
                if (!hasServoAlarms)
                {
                    _logger?.LogInformation("添加伺服报警配置");
                    var servoAlarms = new JObject();

                    // X轴伺服报警
                    var xAxisServoAlarm = new JObject();
                    xAxisServoAlarm["BitIndex"] = 0;
                    xAxisServoAlarm["Name"] = "X轴伺服报警";
                    xAxisServoAlarm["Type"] = "IO";
                    xAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示X轴伺服报警
                    xAxisServoAlarm["Description"] = "X轴伺服驱动器异常报警";
                    servoAlarms["xAxisServoAlarm"] = xAxisServoAlarm;

                    // Y轴伺服报警
                    var yAxisServoAlarm = new JObject();
                    yAxisServoAlarm["BitIndex"] = 1;
                    yAxisServoAlarm["Name"] = "Y轴伺服报警";
                    yAxisServoAlarm["Type"] = "IO";
                    yAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示Y轴伺服报警
                    yAxisServoAlarm["Description"] = "Y轴伺服驱动器异常报警";
                    servoAlarms["yAxisServoAlarm"] = yAxisServoAlarm;

                    // Z轴伺服报警
                    var zAxisServoAlarm = new JObject();
                    zAxisServoAlarm["BitIndex"] = 2;
                    zAxisServoAlarm["Name"] = "Z轴伺服报警";
                    zAxisServoAlarm["Type"] = "IO";
                    zAxisServoAlarm["IoAddress"] = 0; // 使用IO地址0表示Z轴伺服报警
                    zAxisServoAlarm["Description"] = "Z轴伺服驱动器异常报警";
                    servoAlarms["zAxisServoAlarm"] = zAxisServoAlarm;

                    alarms["ServoAlarms"] = servoAlarms;
                }

                if (!hasMotionAlarms)
                {
                    _logger?.LogInformation("添加运动报警配置");
                    var motionAlarms = new JObject();

                    // 运动报警复位配置
                    var resetMotionAlarm = new JObject();
                    resetMotionAlarm["BitIndex"] = 0;
                    resetMotionAlarm["Name"] = "运动报警复位";
                    resetMotionAlarm["Type"] = "Register";
                    resetMotionAlarm["IoAddress"] = 16; // 使用D16寄存器作为运动报警复位
                    resetMotionAlarm["Description"] = "写入1到D16寄存器复位所有运动报警";
                    motionAlarms["resetMotionAlarm"] = resetMotionAlarm;

                    // 控制卡总异常
                    var cardTotalError = new JObject();
                    cardTotalError["BitIndex"] = 0;
                    cardTotalError["Name"] = "控制卡总异常";
                    cardTotalError["Type"] = "Register";
                    cardTotalError["IoAddress"] = 17; // 使用D17寄存器检测总异常
                    cardTotalError["Description"] = "控制卡总异常，1代表有异常";
                    motionAlarms["cardTotalError"] = cardTotalError;

                    // X轴复位异常
                    var xAxisHomeError = new JObject();
                    xAxisHomeError["BitIndex"] = 0;
                    xAxisHomeError["Name"] = "X轴复位异常";
                    xAxisHomeError["Type"] = "Register";
                    xAxisHomeError["IoAddress"] = 21; // 使用D21寄存器
                    xAxisHomeError["Description"] = "X轴复位过程中发生异常";
                    motionAlarms["xAxisHomeError"] = xAxisHomeError;

                    // X轴移动异常
                    var xAxisMoveError = new JObject();
                    xAxisMoveError["BitIndex"] = 0;
                    xAxisMoveError["Name"] = "X轴移动异常";
                    xAxisMoveError["Type"] = "Register";
                    xAxisMoveError["IoAddress"] = 22; // 使用D22寄存器
                    xAxisMoveError["Description"] = "X轴移动过程中发生异常";
                    motionAlarms["xAxisMoveError"] = xAxisMoveError;

                    // Y轴复位异常
                    var yAxisHomeError = new JObject();
                    yAxisHomeError["BitIndex"] = 0;
                    yAxisHomeError["Name"] = "Y轴复位异常";
                    yAxisHomeError["Type"] = "Register";
                    yAxisHomeError["IoAddress"] = 37; // 使用D37寄存器
                    yAxisHomeError["Description"] = "Y轴复位过程中发生异常";
                    motionAlarms["yAxisHomeError"] = yAxisHomeError;

                    // Y轴移动异常
                    var yAxisMoveError = new JObject();
                    yAxisMoveError["BitIndex"] = 0;
                    yAxisMoveError["Name"] = "Y轴移动异常";
                    yAxisMoveError["Type"] = "Register";
                    yAxisMoveError["IoAddress"] = 38; // 使用D38寄存器
                    yAxisMoveError["Description"] = "Y轴移动过程中发生异常";
                    motionAlarms["yAxisMoveError"] = yAxisMoveError;

                    // Z轴复位异常
                    var zAxisHomeError = new JObject();
                    zAxisHomeError["BitIndex"] = 0;
                    zAxisHomeError["Name"] = "Z轴复位异常";
                    zAxisHomeError["Type"] = "Register";
                    zAxisHomeError["IoAddress"] = 53; // 使用D53寄存器
                    zAxisHomeError["Description"] = "Z轴复位过程中发生异常";
                    motionAlarms["zAxisHomeError"] = zAxisHomeError;

                    // Z轴移动异常
                    var zAxisMoveError = new JObject();
                    zAxisMoveError["BitIndex"] = 0;
                    zAxisMoveError["Name"] = "Z轴移动异常";
                    zAxisMoveError["Type"] = "Register";
                    zAxisMoveError["IoAddress"] = 54; // 使用D54寄存器
                    zAxisMoveError["Description"] = "Z轴移动过程中发生异常";
                    motionAlarms["zAxisMoveError"] = zAxisMoveError;

                    alarms["MotionAlarms"] = motionAlarms;
                }

                // 保存更新后的配置
                bool success = SaveConfig(config);
                if (success)
                {
                    _logger?.LogInformation("报警配置更新成功");
                }
                else
                {
                    _logger?.LogError("保存报警配置失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"更新报警配置时发生异常: {ex.Message}", "[AlarmSystemHelper]");
                return false;
            }
        }

        /// <summary>
        /// 读取配置文件
        /// </summary>
        /// <returns>配置对象</returns>
        private static JObject ReadConfig()
        {
            try
            {
                string filePath = "DeviceConfiguration.json";
                if (File.Exists(filePath))
                {
                    string json = File.ReadAllText(filePath);
                    return JObject.Parse(json);
                }
                _logger?.LogWarning("配置文件不存在: " + filePath, "报警系统");
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "读取配置文件失败", "报警系统");
                return null;
            }
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <returns>是否保存成功</returns>
        private static bool SaveConfig(JObject config)
        {
            try
            {
                string filePath = "DeviceConfiguration.json";
                File.WriteAllText(filePath, config.ToString(Formatting.Indented));
                _logger?.LogInformation("配置已保存到: " + filePath, "报警系统");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "保存配置文件失败", "报警系统");
                return false;
            }
        }
    }
}
