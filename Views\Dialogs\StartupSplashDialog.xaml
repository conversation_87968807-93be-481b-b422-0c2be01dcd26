<hc:Window
    x:Class="Nickel_Inspect.Views.Dialogs.StartupSplashDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="系统启动中"
    Width="500"
    Height="400"
    AllowsTransparency="True"
    Background="Transparent"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterScreen"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Images/startup_logo.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Border
        Background="{DynamicResource RegionBrush}"
        BorderBrush="{DynamicResource BorderBrush}"
        BorderThickness="1"
        CornerRadius="8">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <TextBlock
                Grid.Row="0"
                Margin="0,0,0,10"
                HorizontalAlignment="Center"
                FontSize="20"
                FontWeight="Bold"
                Text="景旺镍片检查系统" />

            <ContentControl 
                Grid.Row="1"
                Width="240"
                Height="240"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                x:Name="SplashGifImage"
                RenderTransformOrigin="0.5,0.5"
                Content="{StaticResource StartupLogo}">
                <ContentControl.RenderTransform>
                    <ScaleTransform x:Name="LogoScaleTransform" />
                </ContentControl.RenderTransform>
            </ContentControl>

            <StackPanel Grid.Row="2" Margin="0,10,0,0">
                <TextBlock
                    x:Name="StatusTextBlock"
                    Margin="0,0,0,10"
                    HorizontalAlignment="Center"
                    Text="系统初始化中..."
                    TextWrapping="Wrap" />

                <ProgressBar
                    x:Name="ProgressBar"
                    Height="4"
                    Margin="0,5,0,0"
                    IsIndeterminate="True" />
            </StackPanel>
        </Grid>
    </Border>
</hc:Window> 