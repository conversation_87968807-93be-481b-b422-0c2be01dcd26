<UserControl
    x:Class="Nickel_Inspect.Views.ConfigPointView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    d:DesignHeight="1080"
    d:DesignWidth="1920"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Focusable="True"
    KeyDown="UserControl_KeyDown"
    PreviewKeyUp="UserControl_KeyUp"
    PreviewKeyDown="UserControl_PreviewKeyDown"
    mc:Ignorable="d">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="50" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <StackPanel Margin="0,0,0,5" Orientation="Horizontal">
                <ComboBox
                    Grid.Row="1"
                    Height="30"
                    MinWidth="150"
                    Margin="5"
                    hc:DropDownElement.ConsistentWidth="False"
                    DisplayMemberPath="ModelName"
                    FontSize="20"
                    ItemsSource="{Binding ProductModels}"
                    SelectedIndex="0"
                    SelectedItem="{Binding SelectedModel}" />
                <Button
                    Height="35"
                    Margin="0,0,5,0"
                    Command="{Binding LoadProductModelsCommand}"
                    Content="加载机种列表"
                    FontSize="20" />
                <Button
                    Height="35"
                    Margin="0,0,5,0"
                    Command="{Binding AddProductModelCommand}"
                    Content="添加机种"
                    FontSize="20" />
                <Button
                    Height="35"
                    Margin="0,0,5,0"
                    Command="{Binding DeleteProductModelCommand}"
                    Content="删除机种"
                    FontSize="20" />
                <Button
                    Height="35"
                    Margin="5,0"
                    Command="{Binding OpenTrackControlCommand}"
                    Content="轨道控制"
                    FontSize="20" />
            </StackPanel>

            <DataGrid
                Grid.Row="1"
                AutoGenerateColumns="False"
                IsReadOnly="True"
                ItemsSource="{Binding InspectionPoints}"
                SelectedItem="{Binding SelectedPoint, Mode=TwoWay}">
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem
                            Command="{Binding EditInspectionPointCommand}"
                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                            Header="编辑检查点" />
                        <MenuItem
                            Command="{Binding DeleteInspectionPointCommand}"
                            CommandParameter="{Binding PlacementTarget.SelectedItem, RelativeSource={RelativeSource AncestorType=ContextMenu}}"
                            Header="删除检查点" />
                    </ContextMenu>
                </DataGrid.ContextMenu>
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="120"
                        Binding="{Binding PointName}"
                        Header="点位名称" />
                    <!--<DataGridTextColumn Header="点位编码"  Binding="{Binding PointCode}" />-->
                    <!--<DataGridTextColumn Header="序号" Binding="{Binding SequenceNo}" />-->
                    <DataGridTextColumn
                        Width="70"
                        Binding="{Binding XPosition}"
                        Header="X" />
                    <DataGridTextColumn
                        Width="70"
                        Binding="{Binding YPosition}"
                        Header="Y" />
                    <DataGridTextColumn
                        Width="70"
                        Binding="{Binding ZPosition}"
                        Header="Z" />
                    <DataGridTemplateColumn Width="150" Header="检查文件路径">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Text="{Binding InspectionFilePath, Converter={StaticResource FilePathToNameConverter}}"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip="{Binding InspectionFilePath}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  光源亮度列  -->
                    <DataGridTemplateColumn Width="120" Header="光源亮度(RGB)">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Foreground="Red" Text="{Binding RedChannelBrightness}" />
                                    <TextBlock Text=" / " />
                                    <TextBlock Foreground="Green" Text="{Binding GreenChannelBrightness}" />
                                    <TextBlock Text=" / " />
                                    <TextBlock Foreground="Blue" Text="{Binding BlueChannelBrightness}" />
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <DataGridTemplateColumn Width="80" Header="白光">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding WhiteChannelBrightness}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <DataGridTemplateColumn Width="50" Header="启用">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox
                                    HorizontalAlignment="Center"
                                    IsChecked="{Binding IsActive, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    IsEnabled="False" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <!--  Go按钮列  -->
                    <DataGridTemplateColumn Width="70" Header="操作">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button
                                    Width="60"
                                    Height="30"
                                    Margin="2"
                                    Command="{Binding DataContext.CommandGo, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                    CommandParameter="{Binding}"
                                    Content="Go"
                                    Style="{StaticResource ButtonDanger}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!--  手动触发检查按钮列  -->
                    <DataGridTemplateColumn Width="110" Header="检查">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button
                                    Width="100"
                                    Height="30"
                                    Margin="2"
                                    Command="{Binding DataContext.ManualInspectCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                    CommandParameter="{Binding}"
                                    Content="手动触发检查"
                                    Style="{StaticResource ButtonInfo}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>

        </Grid>

        <Grid Grid.Column="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="300" />
            </Grid.RowDefinitions>

            <!--  检查结果图像显示区域  -->
            <Border
                Margin="0,0,0,10"
                BorderBrush="Gray"
                BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Margin="5"
                        HorizontalAlignment="Center"
                        FontSize="20"
                        FontWeight="Bold"
                        Text="检查结果图像" />

                    <Image
                        Grid.Row="1"
                        Margin="10"
                        Source="{Binding CurrentImage}"
                        Stretch="Uniform" />

                    <!--  进度条覆盖层  -->
                    <Grid
                        Grid.Row="1"
                        Background="#80000000"
                        Visibility="{Binding IsLoadingVpp, Converter={StaticResource Boolean2VisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock
                                Margin="0,0,0,10"
                                HorizontalAlignment="Center"
                                Foreground="White"
                                Text="正在加载检查文件..." />
                            <ProgressBar
                                Width="300"
                                Height="15"
                                Value="{Binding LoadingProgress}" />
                            <TextBlock
                                Margin="0,5,0,0"
                                HorizontalAlignment="Center"
                                Foreground="White"
                                Text="{Binding LoadingProgress, StringFormat={}{0:F0}%}" />
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>

            <!--  控制区域与光源亮度控制区域  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  速度和轴控制区域  -->
                <Border
                    Grid.Column="0"
                    Margin="0,0,5,0"
                    BorderBrush="Cyan"
                    BorderThickness="2">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  速度控制  -->
                        <StackPanel Grid.Row="0" Margin="0,0,0,10">
                            <TextBlock Margin="0,0,0,5" Text="JOG速度控制" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80" />
                                    <ColumnDefinition Width="100" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock
                                    Grid.Column="0"
                                    Margin="10,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="平面XY速度:" />
                                <TextBlock
                                    Grid.Column="1"
                                    VerticalAlignment="Center"
                                    Text="{Binding JogSpeed, StringFormat={}{0}mm/s}" />
                                <Slider
                                    Grid.Column="2"
                                    Margin="0,0"
                                    VerticalAlignment="Center"
                                    Maximum="500"
                                    Minimum="1"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding JogSpeedToggled}" />
                            </Grid>
                            <!--  Z轴JOG速度控制  -->
                            <Grid Margin="0,5,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80" />
                                    <ColumnDefinition Width="100" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock
                                    Grid.Column="0"
                                    Margin="10,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="Z轴速度:" />
                                <TextBlock
                                    Grid.Column="1"
                                    VerticalAlignment="Center"
                                    Text="{Binding ZJogSpeed, StringFormat={}{0}mm/s}" />
                                <Slider
                                    Grid.Column="2"
                                    Margin="0,0"
                                    VerticalAlignment="Center"
                                    Maximum="200"
                                    Minimum="1"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding ZJogSpeedToggled}" />
                            </Grid>
                        </StackPanel>

                        <StackPanel
                            Grid.Row="1"
                            HorizontalAlignment="Center"
                            Orientation="Horizontal">
                            <!--  XY轴控制  -->
                            <Border
                                Margin="0,0,0,10"
                                Padding="10"
                                BorderBrush="{DynamicResource BorderBrush}"
                                BorderThickness="1">
                                <Grid Width="200" Height="200">
                                    <Ellipse
                                        Width="8"
                                        Height="8"
                                        Fill="{DynamicResource PrimaryBrush}" />

                                    <!--  Y+ 按钮  -->
                                    <Button
                                        Width="50"
                                        Height="50"
                                        Margin="0,10,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Top"
                                        Content="↑"
                                        FontSize="30"
                                        Style="{StaticResource ButtonPrimary}"
                                        ToolTip="快捷键: W">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="PreviewMouseDown">
                                                <i:InvokeCommandAction Command="{Binding StartJogCommand}" CommandParameter="Y-" />
                                            </i:EventTrigger>
                                            <i:EventTrigger EventName="PreviewMouseUp">
                                                <i:InvokeCommandAction Command="{Binding StopJogCommand}" CommandParameter="Y" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Button>

                                    <!--  Y- 按钮  -->
                                    <Button
                                        Width="50"
                                        Height="50"
                                        Margin="0,0,0,10"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Bottom"
                                        Content="↓"
                                        FontSize="30"
                                        Style="{StaticResource ButtonPrimary}"
                                        ToolTip="快捷键: S">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="PreviewMouseDown">
                                                <i:InvokeCommandAction Command="{Binding StartJogCommand}" CommandParameter="Y+" />
                                            </i:EventTrigger>
                                            <i:EventTrigger EventName="PreviewMouseUp">
                                                <i:InvokeCommandAction Command="{Binding StopJogCommand}" CommandParameter="Y" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Button>

                                    <!--  X- 按钮  -->
                                    <Button
                                        Width="50"
                                        Height="50"
                                        Margin="10,0,0,0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Content="←"
                                        FontSize="30"
                                        Style="{StaticResource ButtonPrimary}"
                                        ToolTip="快捷键: A">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="PreviewMouseDown">
                                                <i:InvokeCommandAction Command="{Binding StartJogCommand}" CommandParameter="X-" />
                                            </i:EventTrigger>
                                            <i:EventTrigger EventName="PreviewMouseUp">
                                                <i:InvokeCommandAction Command="{Binding StopJogCommand}" CommandParameter="X" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Button>

                                    <!--  X+ 按钮  -->
                                    <Button
                                        Width="50"
                                        Height="50"
                                        Margin="0,0,10,0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Content="→"
                                        FontSize="30"
                                        Style="{StaticResource ButtonPrimary}"
                                        ToolTip="快捷键: D">
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="PreviewMouseDown">
                                                <i:InvokeCommandAction Command="{Binding StartJogCommand}" CommandParameter="X+" />
                                            </i:EventTrigger>
                                            <i:EventTrigger EventName="PreviewMouseUp">
                                                <i:InvokeCommandAction Command="{Binding StopJogCommand}" CommandParameter="X" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Button>

                                    <!--  XY轴复位按钮  -->
                                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <!--  X轴复位按钮  -->
                                        <Button
                                            Width="36"
                                            Height="36"
                                            Margin="2"
                                            Command="{Binding HomeAxisCommand}"
                                            CommandParameter="X"
                                            Style="{StaticResource ButtonWarning}"
                                            ToolTip="X轴复位">
                                            <hc:SimplePanel>
                                                <Path
                                                    Width="16"
                                                    Height="16"
                                                    Data="M8,1L16,8L14,8L14,16L10,16L10,10L6,10L6,16L2,16L2,8L0,8L8,1z"
                                                    Fill="{DynamicResource TextIconBrush}"
                                                    Stretch="Uniform" />
                                            </hc:SimplePanel>
                                        </Button>

                                        <!--  Y轴复位按钮  -->
                                        <Button
                                            Width="36"
                                            Height="36"
                                            Margin="2"
                                            Command="{Binding HomeAxisCommand}"
                                            CommandParameter="Y"
                                            Style="{StaticResource ButtonWarning}"
                                            ToolTip="Y轴复位">
                                            <hc:SimplePanel>
                                                <Path
                                                    Width="16"
                                                    Height="16"
                                                    Data="M8,1L16,8L14,8L14,16L10,16L10,10L6,10L6,16L2,16L2,8L0,8L8,1z"
                                                    Fill="{DynamicResource TextIconBrush}"
                                                    Stretch="Uniform" />
                                            </hc:SimplePanel>
                                        </Button>
                                    </StackPanel>
                                </Grid>
                            </Border>
                            <!--  Z轴控制  -->
                            <Border
                                Padding="10"
                                BorderBrush="{DynamicResource BorderBrush}"
                                BorderThickness="1">
                                <StackPanel VerticalAlignment="Center" Orientation="Vertical">
                                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                        <!--  Z+ 按钮  -->
                                        <Button
                                            Width="50"
                                            Height="50"
                                            Margin="5"
                                            Content="↑"
                                            FontSize="30"
                                            Style="{StaticResource ButtonPrimary}"
                                            ToolTip="快捷键: Q">
                                            <i:Interaction.Triggers>
                                                <i:EventTrigger EventName="PreviewMouseDown">
                                                    <i:InvokeCommandAction Command="{Binding StartJogCommand}" CommandParameter="Z-" />
                                                </i:EventTrigger>
                                                <i:EventTrigger EventName="PreviewMouseUp">
                                                    <i:InvokeCommandAction Command="{Binding StopJogCommand}" CommandParameter="Z" />
                                                </i:EventTrigger>
                                            </i:Interaction.Triggers>
                                        </Button>

                                        <!--  Z轴复位按钮  -->
                                        <Button
                                            Width="36"
                                            Height="36"
                                            Margin="5"
                                            Command="{Binding HomeAxisCommand}"
                                            CommandParameter="Z"
                                            Style="{StaticResource ButtonWarning}"
                                            ToolTip="Z轴复位">
                                            <hc:SimplePanel>
                                                <Path
                                                    Width="16"
                                                    Height="16"
                                                    Data="M8,1L16,8L14,8L14,16L10,16L10,10L6,10L6,16L2,16L2,8L0,8L8,1z"
                                                    Fill="{DynamicResource TextIconBrush}"
                                                    Stretch="Uniform" />
                                            </hc:SimplePanel>
                                        </Button>

                                        <!--  Z- 按钮  -->
                                        <Button
                                            Width="50"
                                            Height="50"
                                            Margin="5"
                                            Content="↓"
                                            FontSize="30"
                                            Style="{StaticResource ButtonPrimary}"
                                            ToolTip="快捷键: Z">
                                            <i:Interaction.Triggers>
                                                <i:EventTrigger EventName="PreviewMouseDown">
                                                    <i:InvokeCommandAction Command="{Binding StartJogCommand}" CommandParameter="Z+" />
                                                </i:EventTrigger>
                                                <i:EventTrigger EventName="PreviewMouseUp">
                                                    <i:InvokeCommandAction Command="{Binding StopJogCommand}" CommandParameter="Z" />
                                                </i:EventTrigger>
                                            </i:Interaction.Triggers>
                                        </Button>
                                    </StackPanel>

                                    <!--  单次采集按钮  -->
                                    <Button
                                        Width="120"
                                        Height="50"
                                        Margin="5,10,5,0"
                                        Command="{Binding SingleAcquireCommand}"
                                        Content="单次采集"
                                        FontSize="20"
                                        Style="{StaticResource ButtonInfo}" />
                                    <!--  添加检查点按钮  -->
                                    <Button
                                        Width="120"
                                        Height="50"
                                        Margin="5,10,5,0"
                                        Command="{Binding ShowAddPointDialogCommand}"
                                        Content="添加检查点"
                                        FontSize="20"
                                        Style="{StaticResource ButtonPrimary}" />
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </Border>

                <!--  光源亮度控制区域  -->
                <Border
                    Grid.Column="1"
                    Margin="5,0,0,0"
                    BorderBrush="Gray"
                    BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <TextBlock
                            Margin="5"
                            HorizontalAlignment="Center"
                            FontSize="16"
                            FontWeight="Bold"
                            Text="光源亮度控制" />


                        <StackPanel Grid.Row="1" Margin="10">
                            <!--  添加光源控制器下拉列表  -->
                            <DockPanel Grid.Row="1" Margin="0,5">
                                <TextBlock
                                    Width="80"
                                    VerticalAlignment="Center"
                                    Text="光源控制器：" />
                                <ComboBox
                                    DisplayMemberPath="Name"
                                    ItemsSource="{Binding LightControllers}"
                                    SelectedIndex="0"
                                    SelectedItem="{Binding SelectedLightController}"
                                    SelectedValuePath="ControllerId" />
                            </DockPanel>

                            <!--  红色通道亮度  -->
                            <DockPanel Margin="0,5">
                                <TextBlock
                                    Width="80"
                                    VerticalAlignment="Center"
                                    Text="红色通道：" />
                                <TextBlock
                                    Width="40"
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="{Binding RedChannelBrightness}" />
                                <Slider
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding RedChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="ValueChanged">
                                            <i:InvokeCommandAction Command="{Binding UpdateChannelBrightnessCommand}" CommandParameter="Red" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                </Slider>
                            </DockPanel>

                            <!--  绿色通道亮度  -->
                            <DockPanel Margin="0,5">
                                <TextBlock
                                    Width="80"
                                    VerticalAlignment="Center"
                                    Text="绿色通道：" />
                                <TextBlock
                                    Width="40"
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="{Binding GreenChannelBrightness}" />
                                <Slider
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding GreenChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="ValueChanged">
                                            <i:InvokeCommandAction Command="{Binding UpdateChannelBrightnessCommand}" CommandParameter="Green" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                </Slider>
                            </DockPanel>

                            <!--  蓝色通道亮度  -->
                            <DockPanel Margin="0,5">
                                <TextBlock
                                    Width="80"
                                    VerticalAlignment="Center"
                                    Text="蓝色通道：" />
                                <TextBlock
                                    Width="40"
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="{Binding BlueChannelBrightness}" />
                                <Slider
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding BlueChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="ValueChanged">
                                            <i:InvokeCommandAction Command="{Binding UpdateChannelBrightnessCommand}" CommandParameter="Blue" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                </Slider>
                            </DockPanel>

                            <!--  白光通道亮度  -->
                            <DockPanel Margin="0,5">
                                <TextBlock
                                    Width="80"
                                    VerticalAlignment="Center"
                                    Text="白光通道：" />
                                <TextBlock
                                    Width="40"
                                    Margin="0,0,5,0"
                                    VerticalAlignment="Center"
                                    Text="{Binding WhiteChannelBrightness}" />
                                <Slider
                                    Maximum="255"
                                    Minimum="0"
                                    Style="{StaticResource SliderBaseStyle}"
                                    Value="{Binding WhiteChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="ValueChanged">
                                            <i:InvokeCommandAction Command="{Binding UpdateChannelBrightnessCommand}" CommandParameter="White" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                </Slider>
                            </DockPanel>

                            <!--  应用光源设置按钮  -->
                            <Button
                                Height="50"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Right"
                                Command="{Binding ApplyLightSettingsCommand}"
                                Content="应用光源设置"
                                FontSize="20"
                                Style="{StaticResource ButtonInfo}"
                                ToolTip="将当前光源参数应用到选中的检查点" />
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</UserControl> 