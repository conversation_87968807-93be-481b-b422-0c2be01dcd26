using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;

namespace Nickel_Inspect.Services.Alarms
{
    /// <summary>
    /// 报警系统初始化器，负责初始化和配置报警系统
    /// </summary>
    public class AlarmSystemInitializer
    {
        private readonly AlarmManager _alarmManager;
        private readonly BkMotionCard _motionCard;
        private readonly JObject _config;
        private readonly ILogService _logService;

        /// <summary>
        /// 日志消息事件
        /// </summary>
        public event EventHandler<LogMessageEventArgs> LogMessageReceived;

        /// <summary>
        /// 创建一个新的报警系统初始化器
        /// </summary>
        /// <param name="config">配置对象</param>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        public AlarmSystemInitializer(
            JObject config,
            BkMotionCard motionCard,
            ILogService logService
        )
        {
            _config = config;
            _motionCard = motionCard;
            _logService = logService;
            _alarmManager = new AlarmManager(_logService);

            // 订阅报警管理器的日志事件
            _alarmManager.LogMessageReceived += ForwardLogMessage;

            LogInfo("报警系统初始化器已创建");
        }

        private void ForwardLogMessage(object sender, LogMessageEventArgs e)
        {
            // 转发日志事件
            LogMessageReceived?.Invoke(sender, e);
        }

        protected virtual void LogInfo(string message)
        {
            _logService.LogInformation(message, "报警系统");
            LogMessageReceived?.Invoke(this, new LogMessageEventArgs("信息", message));
        }

        protected virtual void LogWarning(string message)
        {
            _logService.LogWarning(message, "报警系统");
            LogMessageReceived?.Invoke(this, new LogMessageEventArgs("警告", message));
        }

        protected virtual void LogError(string message, Exception ex = null)
        {
            if (ex != null)
                _logService.LogError(ex, message, "报警系统");
            else
                _logService.LogError(message, "报警系统");

            LogMessageReceived?.Invoke(this, new LogMessageEventArgs("错误", message));
        }

        /// <summary>
        /// 初始化报警系统
        /// </summary>
        public void Initialize()
        {
            LogInfo("开始初始化报警系统");
            RegisterServoAlarms();
            RegisterTotalMotionException();
            RegisterMotionAlarms();
            _alarmManager.StartMonitoring();
            LogInfo("报警系统初始化完成");
        }

        /// <summary>
        /// 注册伺服报警源
        /// </summary>
        private void RegisterServoAlarms()
        {
            // 检查配置对象
            if (_config == null)
            {
                LogError("配置对象为null，无法注册报警源");
                return;
            }

            LogInfo($"配置对象类型: {_config.GetType().FullName}");

            // 尝试获取报警配置
            Dictionary<string, IoConfig> servoAlarms = null;

            // 方法1：检查配置中是否包含直接的报警配置
            JToken alarmsToken = _config["Alarms"];
            if (alarmsToken != null)
            {
                LogInfo("在配置中找到alarms节点");

                try
                {
                    // 尝试获取servoAlarms
                    JToken servoAlarmsToken = alarmsToken["ServoAlarms"];
                    if (servoAlarmsToken != null)
                    {
                        LogInfo($"找到servoAlarms节点，类型: {servoAlarmsToken.GetType().Name}");

                        // 尝试转换为Dictionary
                        servoAlarms = servoAlarmsToken.ToObject<Dictionary<string, IoConfig>>();
                        if (servoAlarms != null)
                        {
                            LogInfo($"成功解析servoAlarms，包含 {servoAlarms.Count} 个报警项");
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogError($"解析servoAlarms失败: {ex.Message}", ex);
                }
            }

            // 方法2：检查是否有Root节点下的alarms配置
            if (servoAlarms == null)
            {
                JToken rootToken = _config["root"];
                if (rootToken != null)
                {
                    alarmsToken = rootToken["alarms"];
                    if (alarmsToken != null)
                    {
                        LogInfo("在root节点下找到alarms配置");

                        try
                        {
                            JToken servoAlarmsToken = alarmsToken["servoAlarms"];
                            if (servoAlarmsToken != null)
                            {
                                servoAlarms = servoAlarmsToken.ToObject<
                                    Dictionary<string, IoConfig>
                                >();
                                if (servoAlarms != null)
                                {
                                    LogInfo(
                                        $"成功解析root.alarms.servoAlarms，包含 {servoAlarms.Count} 个报警项"
                                    );
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogError($"解析root.alarms.servoAlarms失败: {ex.Message}", ex);
                        }
                    }
                }
            }

            // 如果我们找到了伺服报警配置，注册它们
            if (servoAlarms != null && servoAlarms.Count > 0)
            {
                foreach (var pair in servoAlarms)
                {
                    try
                    {
                        string id = pair.Key;
                        IoConfig config = pair.Value;

                        // 验证配置的有效性
                        if (config == null)
                        {
                            LogWarning($"报警配置无效: {id}");
                            continue;
                        }

                        if (string.IsNullOrEmpty(config.Group))
                        {
                            LogWarning($"报警配置缺少Group: {id}");
                            continue;
                        }

                        // 确保Name属性不为空
                        if (string.IsNullOrEmpty(config.Name))
                        {
                            config.Name = id; // 使用ID作为名称
                        }

                        // 确保Type属性不为空
                        if (string.IsNullOrEmpty(config.Type))
                        {
                            config.Type = "NC"; // 默认使用常闭类型
                            LogInfo($"报警配置 {id} 未指定Type，使用默认值NC");
                        }

                        // 获取轴名称（用于relatedDevice）
                        string axisName = config.Name.Contains("轴")
                            ? config.Name.Split('轴')[0] + "轴"
                            : id;

                        // 注册报警源
                        RegisterAlarmSource(id, config, axisName);
                    }
                    catch (Exception ex)
                    {
                        LogError($"注册伺服报警源 {pair.Key} 失败: {ex.Message}", ex);
                    }
                }
                return; // 成功注册了报警源，直接返回
            }

            // 如果没有找到有效的配置，使用硬编码默认值
            LogWarning("未找到有效的伺服报警配置，使用硬编码默认值");

            // 创建默认的伺服报警配置
            //RegisterDefaultServoAlarms();
        }

        /// <summary>
        /// 注册默认的伺服报警
        /// </summary>
        private void RegisterDefaultServoAlarms()
        {
            try
            {
                // X轴伺服报警 (Z0.4)
                var xAxisConfig = new IoConfig
                {
                    Group = "Z",
                    SubGroup = 0,
                    IoBitIndex = 4,
                    Name = "X轴伺服报警",
                    Type = "NC",
                    Description = "X轴伺服报警信号",
                };
                RegisterAlarmSource("xAxisServoAlarm", xAxisConfig, "X轴");

                // Y轴伺服报警 (X0.5)
                var yAxisConfig = new IoConfig
                {
                    Group = "X",
                    SubGroup = 0,
                    IoBitIndex = 5,
                    Name = "Y轴伺服报警",
                    Type = "NC",
                    Description = "Y轴伺服报警信号",
                };
                RegisterAlarmSource("yAxisServoAlarm", yAxisConfig, "Y轴");

                // Z轴伺服报警 (X0.6)
                var zAxisConfig = new IoConfig
                {
                    Group = "X",
                    SubGroup = 0,
                    IoBitIndex = 6,
                    Name = "Z轴伺服报警",
                    Type = "NC",
                    Description = "Z轴伺服报警信号",
                };
                RegisterAlarmSource("zAxisServoAlarm", zAxisConfig, "Z轴");

                LogInfo("已注册默认的伺服报警配置");
            }
            catch (Exception ex)
            {
                LogError($"注册默认伺服报警配置失败: {ex.Message}", ex);
            }
        }

        private void RegisterAlarmSource(string id, IoConfig config, string relatedDevice)
        {
            LogInfo($"注册报警源: {id}, 位于{config.Group}{config.SubGroup}.{config.IoBitIndex}");

            var alarmSource = new DigitalInputAlarmSource(
                id,
                config,
                AlarmType.ServoAlarm,
                relatedDevice,
                _motionCard,
                _logService
            );

            // 订阅报警源的日志事件
            alarmSource.LogMessageReceived += ForwardLogMessage;

            _alarmManager.RegisterAlarmSource(alarmSource);
            LogInfo($"伺服报警源 {id} 注册成功");
        }

        /// <summary>
        /// 注册总运动异常报警
        /// </summary>
        private void RegisterTotalMotionException()
        {
            LogInfo("开始注册总运动异常报警源");

            try
            {
                // 检查是否有配置
                JToken alarmsToken = _config["Alarms"];
                JToken motionAlarmsToken = null;

                if (alarmsToken != null)
                {
                    motionAlarmsToken = alarmsToken["MotionAlarms"];
                }

                string id = "cardTotalError";
                string name = "控制卡总异常";
                string description = "控制卡总异常，1代表有异常";
                int register = 17; // D17寄存器
                int bitIndex = 0; // 第0位

                // 尝试从配置中读取
                if (motionAlarmsToken != null)
                {
                    JToken totalErrorToken = motionAlarmsToken[id];
                    if (totalErrorToken != null)
                    {
                        name = totalErrorToken["Name"]?.Value<string>() ?? name;
                        description =
                            totalErrorToken["Description"]?.Value<string>() ?? description;
                        register = totalErrorToken["IoAddress"]?.Value<int>() ?? register;
                        bitIndex = totalErrorToken["BitIndex"]?.Value<int>() ?? bitIndex;
                    }
                }

                RegisterMotionAlarmSource(
                    id,
                    name,
                    description,
                    "控制卡",
                    -1, // 不关联具体轴
                    register,
                    bitIndex
                );

                // 检查并记录复位寄存器配置
                if (motionAlarmsToken != null)
                {
                    JToken resetToken = motionAlarmsToken["resetMotionAlarm"];
                    if (resetToken != null)
                    {
                        int resetRegister = resetToken["IoAddress"]?.Value<int>() ?? 16;
                        int resetBitIndex = resetToken["BitIndex"]?.Value<int>() ?? 0;
                        LogInfo($"运动报警复位配置: 寄存器D{resetRegister}, 位{resetBitIndex}");
                    }
                }

                LogInfo("总运动异常报警源注册成功");
            }
            catch (Exception ex)
            {
                LogError($"注册总运动异常报警源时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册运动报警源
        /// </summary>
        private void RegisterMotionAlarms()
        {
            // 检查配置对象
            if (_config == null)
            {
                LogError("配置对象为null，无法注册运动报警源");
                return;
            }

            LogInfo("开始注册轴运动报警源");

            // 尝试获取报警配置
            JToken alarmsToken = _config["Alarms"];
            if (alarmsToken == null)
            {
                LogWarning("配置中未找到Alarms节点，无法注册运动报警源");
                return;
            }

            // 尝试获取motionAlarms，检查其中的轴复位和轴移动异常报警
            JToken motionAlarmsToken = alarmsToken["MotionAlarms"];
            if (motionAlarmsToken == null)
            {
                LogWarning("配置中未找到MotionAlarms节点，无法注册运动报警源");
                return;
            }

            // 注册X轴运动报警
            RegisterAxisMotionAlarms("X");

            // 注册Y轴运动报警
            RegisterAxisMotionAlarms("Y");

            // 注册Z轴运动报警
            RegisterAxisMotionAlarms("Z");

            // 注册宽度轴运动报警 (如果有)
            RegisterAxisMotionAlarms("Width");

            // 注册皮带轴运动报警 (如果有)
            RegisterAxisMotionAlarms("Belt");

            LogInfo("轴运动报警源注册完成");
        }

        /// <summary>
        /// 为指定轴注册运动报警
        /// </summary>
        /// <param name="axisName">轴名称</param>
        private void RegisterAxisMotionAlarms(string axisName)
        {
            try
            {
                JToken motionAlarmsToken = _config["Alarms"]["MotionAlarms"];

                // 查找轴复位异常报警
                string homeErrorId = $"{axisName.ToLower()}AxisHomeError";
                JToken homeErrorToken = motionAlarmsToken[homeErrorId];
                if (homeErrorToken != null)
                {
                    // 解析配置
                    int axisId = GetAxisIdByName(axisName);
                    int register = homeErrorToken["IoAddress"]?.Value<int>() ?? 0;
                    int bitIndex = homeErrorToken["BitIndex"]?.Value<int>() ?? 0;
                    string name =
                        homeErrorToken["Name"]?.Value<string>() ?? $"{axisName}轴复位异常";
                    string description =
                        homeErrorToken["Description"]?.Value<string>()
                        ?? $"{axisName}轴复位过程中发生异常";

                    // 创建并注册运动报警源
                    RegisterMotionAlarmSource(
                        homeErrorId,
                        name,
                        description,
                        $"{axisName}轴",
                        axisId,
                        register,
                        bitIndex
                    );
                }

                // 查找轴移动异常报警
                string moveErrorId = $"{axisName.ToLower()}AxisMoveError";
                JToken moveErrorToken = motionAlarmsToken[moveErrorId];
                if (moveErrorToken != null)
                {
                    // 解析配置
                    int axisId = GetAxisIdByName(axisName);
                    int register = moveErrorToken["IoAddress"]?.Value<int>() ?? 0;
                    int bitIndex = moveErrorToken["BitIndex"]?.Value<int>() ?? 0;
                    string name =
                        moveErrorToken["Name"]?.Value<string>() ?? $"{axisName}轴移动异常";
                    string description =
                        moveErrorToken["Description"]?.Value<string>()
                        ?? $"{axisName}轴移动过程中发生异常";

                    // 创建并注册运动报警源
                    RegisterMotionAlarmSource(
                        moveErrorId,
                        name,
                        description,
                        $"{axisName}轴",
                        axisId,
                        register,
                        bitIndex
                    );
                }
            }
            catch (Exception ex)
            {
                LogError($"注册{axisName}轴运动报警源时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据轴名称获取轴ID
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <returns>轴ID</returns>
        private int GetAxisIdByName(string axisName)
        {
            try
            {
                // 尝试从配置中获取轴配置
                JToken deviceConfig = _config["DeviceConfig"];
                if (deviceConfig != null)
                {
                    JToken axes = deviceConfig["Axes"];
                    if (axes != null && axes.Type == JTokenType.Array)
                    {
                        foreach (var axis in axes)
                        {
                            string name = axis["Name"]?.Value<string>();
                            if (string.Equals(name, axisName, StringComparison.OrdinalIgnoreCase))
                            {
                                return axis["AxisNo"]?.Value<int>() ?? 0;
                            }
                        }
                    }
                }

                // 如果未找到，返回默认值，根据轴名称推断
                switch (axisName.ToUpper())
                {
                    case "X":
                        return 0;
                    case "Y":
                        return 1;
                    case "Z":
                        return 2;
                    case "WIDTH":
                        return 3;
                    case "BELT":
                        return 4;
                    default:
                        return -1;
                }
            }
            catch (Exception ex)
            {
                LogError($"根据轴名称获取轴ID时发生异常: {ex.Message}", ex);
                return -1;
            }
        }

        /// <summary>
        /// 注册运动报警源
        /// </summary>
        private void RegisterMotionAlarmSource(
            string id,
            string name,
            string description,
            string relatedDevice,
            int axisId,
            int register,
            int bitIndex
        )
        {
            try
            {
                var alarmSource = new MotionAlarmSource(
                    id,
                    name,
                    description,
                    relatedDevice,
                    axisId,
                    register,
                    bitIndex,
                    _motionCard,
                    _logService,
                    _config // 传入配置对象
                );

                // 注册日志事件
                alarmSource.LogMessageReceived += ForwardLogMessage;

                // 将报警源添加到报警管理器
                _alarmManager.RegisterAlarmSource(alarmSource);

                LogInfo(
                    $"已注册运动报警源: {name}, 轴ID: {axisId}, 寄存器: {register}, 位索引: {bitIndex}"
                );
            }
            catch (Exception ex)
            {
                LogError($"注册运动报警源失败: {name}", ex);
            }
        }

        /// <summary>
        /// 获取报警管理器实例
        /// </summary>
        /// <returns>报警管理器</returns>
        public AlarmManager GetAlarmManager()
        {
            return _alarmManager;
        }
    }
}
