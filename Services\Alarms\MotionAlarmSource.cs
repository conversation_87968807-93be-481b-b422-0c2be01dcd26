using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;

namespace Nickel_Inspect.Services.Alarms
{
    /// <summary>
    /// 运动报警源类，用于监控轴运动过程中的异常情况
    /// </summary>
    public class MotionAlarmSource : IAlarmSignalSource
    {
        private readonly BkMotionCard _motionCard;
        private readonly ILogService _logService;
        private readonly int _axisId;
        private readonly int _alarmRegister;
        private readonly int _bitIndex;
        private readonly JObject _config;
        private bool _isEnabled = true;

        /// <summary>
        /// 日志消息事件
        /// </summary>
        public event EventHandler<LogMessageEventArgs> LogMessageReceived;

        /// <summary>
        /// 报警源ID
        /// </summary>
        public string Id { get; }

        /// <summary>
        /// 报警源名称
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// 报警类型
        /// </summary>
        public AlarmType AlarmType => AlarmType.MotionAlarm;

        /// <summary>
        /// 相关设备
        /// </summary>
        public string RelatedDevice { get; }

        /// <summary>
        /// 报警描述
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// 创建一个新的运动报警源
        /// </summary>
        /// <param name="id">报警源ID</param>
        /// <param name="name">报警源名称</param>
        /// <param name="description">报警描述</param>
        /// <param name="relatedDevice">相关设备</param>
        /// <param name="axisId">轴ID</param>
        /// <param name="alarmRegister">报警寄存器地址</param>
        /// <param name="bitIndex">报警位索引</param>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        /// <param name="config">配置对象</param>
        public MotionAlarmSource(
            string id,
            string name,
            string description,
            string relatedDevice,
            int axisId,
            int alarmRegister,
            int bitIndex,
            BkMotionCard motionCard,
            ILogService logService,
            JObject config = null
        )
        {
            Id = id ?? throw new ArgumentNullException(nameof(id));
            Name = name ?? throw new ArgumentNullException(nameof(name));
            Description = description ?? throw new ArgumentNullException(nameof(description));
            RelatedDevice = relatedDevice ?? throw new ArgumentNullException(nameof(relatedDevice));
            _axisId = axisId;
            _alarmRegister = alarmRegister;
            _bitIndex = bitIndex;
            _motionCard = motionCard ?? throw new ArgumentNullException(nameof(motionCard));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _config = config;
        }

        /// <summary>
        /// 初始化报警源
        /// </summary>
        public void Initialize()
        {
            LogInfo(
                $"初始化运动报警源: {Name}，轴ID: {_axisId}，寄存器: {_alarmRegister}，位索引: {_bitIndex}"
            );
        }

        /// <summary>
        /// 检查报警是否激活
        /// </summary>
        /// <returns>如果报警被触发，返回true；否则返回false</returns>
        public bool IsAlarmActive()
        {
            if (!_isEnabled)
                return false;

            try
            {
                // 首先检查总异常状态(D17)
                ushort totalException = _motionCard.GetTotalException();
                if (totalException == 0)
                {
                    // 总异常为0，表示没有任何报警
                    return false;
                }

                // 如果有总异常，再检查具体轴的具体报警位
                // 从运动控制卡读取报警状态
                bool isActive = _motionCard.GetInputBitStatus(0, _bitIndex);

                // 如果使用地址，使用GetInputBit方法
                if (_alarmRegister > 0)
                {
                    isActive = _motionCard.GetInputBit(_alarmRegister, _bitIndex);
                }

                return isActive;
            }
            catch (Exception ex)
            {
                LogError($"检查运动报警状态时发生错误: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 启用报警监控
        /// </summary>
        public void Enable()
        {
            _isEnabled = true;
            LogInfo($"已启用运动报警源监控: {Name}");
        }

        /// <summary>
        /// 禁用报警监控
        /// </summary>
        public void Disable()
        {
            _isEnabled = false;
            LogInfo($"已禁用运动报警源监控: {Name}");
        }

        /// <summary>
        /// 复位报警
        /// </summary>
        /// <returns>如果复位成功，返回true；否则返回false</returns>
        public bool ResetAlarm()
        {
            try
            {
                // 通过设置D16=1来复位所有运动报警
                // 使用固定地址16，位0
                int resetRegister = GetResetRegisterAddress();
                int resetBit = 0; // 复位位，默认为第0位

                _motionCard.SetOutputBit(resetRegister, resetBit, true);

                // 短暂延迟后再清除复位信号
                System.Threading.Thread.Sleep(100);
                _motionCard.SetOutputBit(resetRegister, resetBit, false);

                LogInfo($"已尝试复位运动报警: {Name}, 寄存器: {resetRegister}, 位: {resetBit}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"复位运动报警时发生错误: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取附加信息
        /// </summary>
        /// <returns>包含附加信息的字典</returns>
        public Dictionary<string, object> GetAdditionalInfo()
        {
            return new Dictionary<string, object>
            {
                { "AxisId", _axisId },
                { "Register", _alarmRegister },
                { "BitIndex", _bitIndex },
            };
        }

        /// <summary>
        /// 获取复位寄存器地址
        /// </summary>
        /// <returns>复位寄存器地址</returns>
        private int GetResetRegisterAddress()
        {
            try
            {
                // 默认使用D16作为复位寄存器
                int defaultRegister = 16;

                // 如果没有配置对象，使用默认值
                if (_config == null)
                {
                    LogWarning("配置对象为空，使用默认复位寄存器D16");
                    return defaultRegister;
                }

                // 尝试从配置中读取
                var alarmsNode = _config["Alarms"];
                if (alarmsNode == null)
                {
                    LogWarning("配置中未找到Alarms节点，使用默认复位寄存器D16");
                    return defaultRegister;
                }

                var motionAlarmsNode = alarmsNode["MotionAlarms"];
                if (motionAlarmsNode == null)
                {
                    LogWarning("配置中未找到MotionAlarms节点，使用默认复位寄存器D16");
                    return defaultRegister;
                }

                var resetNode = motionAlarmsNode["resetMotionAlarm"];
                if (resetNode == null)
                {
                    LogWarning("配置中未找到resetMotionAlarm配置，使用默认复位寄存器D16");
                    return defaultRegister;
                }

                int resetRegister = resetNode["IoAddress"]?.Value<int>() ?? defaultRegister;
                LogInfo($"从配置中读取到运动报警复位寄存器D{resetRegister}");
                return resetRegister;
            }
            catch (Exception ex)
            {
                LogError($"获取复位寄存器地址时发生异常: {ex.Message}", ex);
                return 16; // 出错时使用默认值D16
            }
        }

        protected virtual void LogInfo(string message)
        {
            _logService?.LogInformation(message, "运动报警源");
            LogMessageReceived?.Invoke(this, new LogMessageEventArgs("信息", message));
        }

        protected virtual void LogWarning(string message)
        {
            _logService?.LogWarning(message, "运动报警源");
            LogMessageReceived?.Invoke(this, new LogMessageEventArgs("警告", message));
        }

        protected virtual void LogError(string message, Exception ex = null)
        {
            if (ex != null)
                _logService?.LogError(ex, message, "运动报警源");
            else
                _logService?.LogError(message, "运动报警源");

            LogMessageReceived?.Invoke(this, new LogMessageEventArgs("错误", message));
        }
    }
}
