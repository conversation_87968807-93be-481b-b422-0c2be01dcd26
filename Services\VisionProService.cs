using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Cognex.VisionPro;
using Cognex.VisionPro.ImageFile;
using Cognex.VisionPro.ToolBlock;
using Cognex.Applications.ImageSave;

namespace Nickel_Inspect.Services
{
    /// <summary>
    /// VisionPro服务实现类
    /// </summary>
    public class VisionProService : IVisionProService, IDisposable
    {
        private CogToolBlock _toolBlock;
        private clsSilentForm mSilentForm = null;
        private CogImageFileTool _imageFileTool;
        private readonly ILogService _logService;
        private bool _isInitialized = false;
        private string _vppFilePath;
        // STA线程专用调度器
        private Dispatcher _staDispatcher;
        private Thread _staThread;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logService">日志服务</param>
        public VisionProService(ILogService logService)
        {
            _logService = logService;
            // 创建STA线程用于处理ActiveX控件
            CreateSTAThread();
        }

        /// <summary>
        /// 创建STA线程以处理ActiveX控件
        /// </summary>
        private void CreateSTAThread()
        {
            var manualResetEvent = new ManualResetEvent(false);
            _staThread = new Thread(() =>
            {
                // 设置调度器
                _staDispatcher = Dispatcher.CurrentDispatcher;
                // 通知主线程已准备好
                manualResetEvent.Set();
                // 开始消息循环
                Dispatcher.Run();
            });
            
            // 设置为STA模式
            _staThread.SetApartmentState(ApartmentState.STA);
            _staThread.IsBackground = true;
            _staThread.Start();
            
            // 等待STA线程初始化完成
            manualResetEvent.WaitOne();
        }

        /// <summary>
        /// 初始化VisionPro服务
        /// </summary>
        public bool Initialize()
        {
            try
            {
                // 初始化VisionPro环境
                //CogImageConvertParams.Default.ShallowCopyDisparateImages = false;
                _isInitialized = true;
                LogMessage("VisionPro服务初始化成功", "信息");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"VisionPro服务初始化失败: {ex.Message}", "错误");
                return false;
            }
        }

        /// <summary>
        /// 设置当前正在使用的工具块
        /// </summary>
        /// <param name="toolBlock"></param>
        public void SetWorkingToolBlock(CogToolBlock toolBlock)
        {
            _toolBlock = toolBlock;
        }

        /// <summary>
        /// 加载VisionPro工具块文件(.vpp)
        /// </summary>
        /// <param name="vppFilePath">VisionPro工具块文件路径</param>
        public bool LoadToolBlock(string vppFilePath)
        {
            try
            {
                if (!_isInitialized)
                {
                    LogMessage("VisionPro服务尚未初始化", "错误");
                    return false;
                }

                if (!File.Exists(vppFilePath))
                {
                    LogMessage($"VPP文件不存在: {vppFilePath}", "错误");
                    return false;
                }

                _vppFilePath = vppFilePath;

                // 加载工具块
                _toolBlock = CogSerializer.LoadObjectFromFile(vppFilePath) as CogToolBlock;

                if (_toolBlock == null)
                {
                    LogMessage("加载工具块失败，无效的VPP文件", "错误");
                    return false;
                }

                // 初始化图像文件工具（用于保存图像）
                _imageFileTool = new CogImageFileTool();

                LogMessage($"成功加载工具块: {vppFilePath}", "信息");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"加载工具块失败: {ex.Message}", "错误");
                return false;
            }
        }

        /// <summary>
        /// 获取图像并执行工具块
        /// </summary>
        public async Task<InspectionResult> AcquireAndProcessImageAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (_toolBlock == null)
                    {
                        LogMessage("工具块未加载，无法执行", "错误");
                        return new InspectionResult
                        {
                            Success = false,
                            ErrorMessage = "工具块未加载，无法执行",
                        };
                    }

                    // 记录开始时间
                    var startTime = DateTime.Now;

                    // 运行工具块
                    _toolBlock.Run();

                    ICogRecord lastRunRecord = _toolBlock.CreateLastRunRecord();
                    
                    // 将涉及ActiveX控件的操作移到专用STA线程
                    System.Drawing.Image image1 = null;
                    string status = null;
                    
                    // 在STA线程中处理ActiveX控件操作
                    if (_staDispatcher != null)
                    {
                        try 
                        {
                            var taskCompletionSource = new TaskCompletionSource<System.Drawing.Image>();
                            
                            _staDispatcher.Invoke(() =>
                            {
                                try
                                {
                                    // 在STA线程中安全地创建和操作ActiveX控件
                                    if (mSilentForm == null)
                                    {
                                        mSilentForm = new clsSilentForm();
                                    }
                                    mSilentForm.InitSilentForm();
                                    System.Drawing.Image img = mSilentForm.GraphicsImage(lastRunRecord.SubRecords[1], out status);
                                    taskCompletionSource.SetResult(img);
                                }
                                catch (Exception ex)
                                {
                                    LogMessage($"在STA线程中处理ActiveX控件时出错: {ex.Message}", "错误");
                                    taskCompletionSource.SetException(ex);
                                }
                            });
                            
                            // 等待STA线程的结果
                            try
                            {
                                image1 = taskCompletionSource.Task.Result;
                                if (image1 != null)
                                {
                                    image1.Save("D:\\testVpp.bmp");
                                }
                            }
                            catch (Exception ex)
                            {
                                LogMessage($"获取ActiveX处理结果时出错: {ex.InnerException?.Message ?? ex.Message}", "错误");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogMessage($"调用STA线程时出错: {ex.Message}", "错误");
                        }
                    }
                    else
                    {
                        LogMessage("STA线程未初始化，无法处理ActiveX控件", "错误");
                    }

                    // 计算执行时间
                    var processTime = (DateTime.Now - startTime).TotalMilliseconds;

                    // 获取结果
                    bool success = _toolBlock.RunStatus.Result == CogToolResultConstants.Accept;
                    string resultDescription = success ? "OK" : "NG";

                    // 获取输出图像（假设OutputImage是工具块的输出）
                    CogImage8Grey originalImage = null;
                    CogImage8Grey outputImage = null;
                    List<DefectInfo> defects = new List<DefectInfo>();
                    int finalResult = 0;
                    string errorMessage = "-1";
                    try
                    {
                        // 尝试获取原始图像
                        if (_toolBlock.Outputs.Contains("OriginImage"))
                        {
                            originalImage =
                                _toolBlock.Outputs["OriginImage"].Value as CogImage8Grey;
                        }

                        // 尝试获取处理之后的图像，如果存在的话
                        if (_toolBlock.Outputs.Contains("ProcessedImage"))
                        {
                            outputImage =
                                _toolBlock.Outputs["ProcessedImage"].Value as CogImage8Grey;
                        }

                        // 如果找不到输出图像，使用原始图像
                        if (outputImage == null && originalImage != null)
                        {
                            outputImage = originalImage;
                        }

                        // 尝试获取缺陷信息
                        if (_toolBlock.Outputs.Contains("Defects") && !success)
                        {
                            var defectsList =
                                _toolBlock.Outputs["Defects"].Value as CogGraphicCollection;
                            if (defectsList != null)
                            {
                                foreach (ICogGraphic graphic in defectsList)
                                {
                                    if (graphic is CogRectangle rect)
                                    {
                                        defects.Add(
                                            new DefectInfo
                                            {
                                                X = rect.X,
                                                Y = rect.Y,
                                                Width = rect.Width,
                                                Height = rect.Height,
                                                Type = "矩形区域",
                                            }
                                        );
                                    }
                                    else if (graphic is CogCircle circle)
                                    {
                                        defects.Add(
                                            new DefectInfo
                                            {
                                                X = circle.CenterX,
                                                Y = circle.CenterY,
                                                Radius = circle.Radius,
                                                Type = "圆形区域",
                                            }
                                        );
                                    }
                                }
                            }
                        }

                        // 如果仍然没有找到图像，尝试创建一个默认的灰度图像
                        if (outputImage == null)
                        {
                            LogMessage("无法获取工具块图像，将使用默认图像", "警告");
                            // 创建一个小的默认灰度图像
                            outputImage = new CogImage8Grey(100, 100);
                        }

                        if (_toolBlock.Outputs.Contains("FinalResult"))
                        {
                            var vpResult = _toolBlock.Outputs["FinalResult"].Value;
                            if (vpResult != null)
                            {
                                finalResult = Convert.ToInt32(vpResult);
                            }
                        }
                        if (_toolBlock.Outputs.Contains("ErrorMessage"))
                        {
                            var vpErrorMessage = _toolBlock.Outputs["ErrorMessage"].Value;
                            if (vpErrorMessage != null)
                            {
                                errorMessage = vpErrorMessage.ToString();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"获取输出图像失败: {ex.Message}", "警告");
                    }

                    // 将CogImage转换为Bitmap
                    Bitmap processedBitmap = outputImage?.ToBitmap();
                    Bitmap originalBitmap = originalImage?.ToBitmap();

                    // 提取更多结果数据
                    Dictionary<string, object> resultData = new Dictionary<string, object>();
                    try
                    {
                        // 正确遍历工具块输出
                        for (int i = 0; i < _toolBlock.Outputs.Count; i++)
                        {
                            var output = _toolBlock.Outputs[i];
                            if (output.Value != null && !(output.Value is CogImage8Grey))
                            {
                                resultData[output.Name] = output.Value.ToString();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"提取结果数据失败: {ex.Message}", "警告");
                    }

                    // 创建检测结果
                    var result = new InspectionResult
                    {
                        Success = success,
                        FinalResult = finalResult == 1,
                        ResultDescription = resultDescription,
                        OriginImage = originalBitmap,
                        ProcessedImage = processedBitmap,
                        ProcessTimeMs = processTime,
                        Defects = defects,
                        ResultData = resultData,
                        ErrorMessage = success ? null : errorMessage,
                    };

                    // 触发图像采集完成事件
                    OnImageAcquired(
                        new ImageAcquiredEventArgs
                        {
                            Image = originalBitmap,
                            ProcessResult = success,
                            ResultDescription = resultDescription,
                        }
                    );

                    return result;
                }
                catch (Exception ex)
                {
                    LogMessage($"执行工具块失败: {ex.Message}", "错误");
                    return new InspectionResult
                    {
                        Success = false,
                        ErrorMessage = $"执行工具块失败: {ex.Message}",
                    };
                }
            });
        }

        /// <summary>
        /// 保存图像到指定路径
        /// </summary>
        /// <param name="image">要保存的图像</param>
        /// <param name="filePath">保存路径</param>
        public bool SaveImage(CogImage8Grey image, string filePath)
        {
            try
            {
                if (image == null)
                {
                    LogMessage("图像为空，无法保存", "错误");
                    return false;
                }

                // 确保目录存在
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 保存图像
                image.SaveAsBitmap(filePath);
                LogMessage($"图像已保存到: {filePath}", "信息");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"保存图像失败: {ex.Message}", "错误");
                return false;
            }
        }

        /// <summary>
        /// 图像采集完成事件
        /// </summary>
        public event EventHandler<ImageAcquiredEventArgs> ImageAcquired;

        /// <summary>
        /// 触发图像采集完成事件
        /// </summary>
        protected virtual void OnImageAcquired(ImageAcquiredEventArgs e)
        {
            ImageAcquired?.Invoke(this, e);
        }

        /// <summary>
        /// 记录日志消息
        /// </summary>
        private void LogMessage(string message, string level)
        {
            switch (level.ToLower())
            {
                case "错误":
                    _logService?.LogError(message, "VisionPro服务");
                    break;
                case "警告":
                    _logService?.LogWarning(message, "VisionPro服务");
                    break;
                case "信息":
                default:
                    _logService?.LogInformation(message, "VisionPro服务");
                    break;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 关闭STA线程
                if (_staDispatcher != null && _staThread != null && _staThread.IsAlive)
                {
                    try
                    {
                        _staDispatcher.InvokeShutdown();
                        // 给线程一些时间来关闭
                        if (!_staThread.Join(1000))
                        {
                            // 如果线程没有在1秒内结束，则中止它
                            _staThread.Abort();
                        }
                    }
                    catch (Exception ex)
                    {
                        if (_logService != null)
                        {
                            _logService.LogError(ex, "关闭STA线程时发生错误", "VisionPro服务");
                        }
                    }
                    finally
                    {
                        _staThread = null;
                        _staDispatcher = null;
                    }
                }

                if (_toolBlock != null)
                {
                    if (_toolBlock.Container != null)
                    {
                        _toolBlock.Dispose();
                        _toolBlock = null;
                    }
                }

                if (_imageFileTool != null)
                {
                    _imageFileTool.Dispose();
                    _imageFileTool = null;
                }

                LogMessage("VisionPro服务已释放", "信息");
            }
            catch (Exception ex)
            {
                // 确保Dispose不会抛出异常
                if (_logService != null)
                {
                    _logService.LogError(ex, "释放VisionPro资源时发生错误", "VisionPro服务");
                }
            }
        }

        /// <summary>
        /// 检测结果类
        /// </summary>
        public class InspectionResult
        {
            /// <summary>
            /// 检测是否成功
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 最终结果（OK/NG）
            /// </summary>
            public bool FinalResult { get; set; }

            /// <summary>
            /// 结果描述（OK/NG）
            /// </summary>
            public string ResultDescription { get; set; }

            /// <summary>
            /// 原始图像
            /// </summary>
            public Bitmap OriginImage { get; set; }

            /// <summary>
            /// 处理后的图像
            /// </summary>
            public Bitmap ProcessedImage { get; set; }

            /// <summary>
            /// 处理耗时（毫秒）
            /// </summary>
            public double ProcessTimeMs { get; set; }

            /// <summary>
            /// 缺陷信息列表
            /// </summary>
            public List<DefectInfo> Defects { get; set; } = new List<DefectInfo>();

            /// <summary>
            /// 结果数据
            /// </summary>
            public Dictionary<string, object> ResultData { get; set; } =
                new Dictionary<string, object>();

            /// <summary>
            /// 错误信息
            /// </summary>
            public string ErrorMessage { get; set; }
        }

        /// <summary>
        /// 缺陷信息类
        /// </summary>
        public class DefectInfo
        {
            /// <summary>
            /// X坐标
            /// </summary>
            public double X { get; set; }

            /// <summary>
            /// Y坐标
            /// </summary>
            public double Y { get; set; }

            /// <summary>
            /// 宽度（矩形缺陷）
            /// </summary>
            public double Width { get; set; }

            /// <summary>
            /// 高度（矩形缺陷）
            /// </summary>
            public double Height { get; set; }

            /// <summary>
            /// 半径（圆形缺陷）
            /// </summary>
            public double Radius { get; set; }

            /// <summary>
            /// 缺陷类型
            /// </summary>
            public string Type { get; set; }

            /// <summary>
            /// 缺陷评分
            /// </summary>
            public double Score { get; set; }
        }
    }
}
