<hc:Window
    x:Class="Nickel_Inspect.Views.Dialogs.ShutdownProgressDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="正在关闭"
    Width="400"
    Height="300"
    Background="{DynamicResource RegionBrush}"
    ResizeMode="NoResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <TextBlock
            Grid.Row="0"
            Margin="0,0,0,20"
            HorizontalAlignment="Center"
            FontSize="18"
            FontWeight="Bold"
            Text="正在关闭系统..." />

        <StackPanel Grid.Row="1" VerticalAlignment="Center">
            <hc:CircleProgressBar
                Width="60"
                Height="60"
                Margin="0,0,0,20"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Foreground="{DynamicResource PrimaryBrush}"
                IsIndeterminate="True"
                Value="0" />

            <TextBlock
                x:Name="StatusTextBlock"
                Margin="0,10,0,0"
                HorizontalAlignment="Center"
                Text="正在释放系统资源..."
                TextWrapping="Wrap" />

            <ProgressBar
                x:Name="ProgressBar"
                Height="10"
                Margin="0,20,0,0"
                IsIndeterminate="True" />
        </StackPanel>

        <TextBlock
            Grid.Row="2"
            Margin="0,20,0,0"
            HorizontalAlignment="Center"
            Foreground="Gray"
            Text="请稍候，程序将在完成清理后自动关闭" />
    </Grid>
</hc:Window>