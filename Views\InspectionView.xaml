<UserControl
    x:Class="Nickel_Inspect.Views.InspectionView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:Nickel_Inspect.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    d:DesignHeight="1080"
    d:DesignWidth="1920"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <UserControl.Resources>
        <sys:Double x:Key="ButtonFontSize">40</sys:Double>
        <Style TargetType="{x:Type Control}">
            <Setter Property="FontSize" Value="30" />
        </Style>
        <Style TargetType="{x:Type TextBlock}">
            <Setter Property="FontSize" Value="30" />
        </Style>

        <!--  添加布尔值到颜色的转换器  -->
        <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />

    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="350" />
        </Grid.ColumnDefinitions>

        <!--  左侧检查结果列表  -->
        <Border
            Grid.Column="0"
            Margin="10"
            BorderBrush="{DynamicResource BorderBrush}"
            BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.Row="0"
                    Margin="10,5"
                    FontWeight="Bold"
                    Text="检查结果列表" />

                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl Margin="5" ItemsSource="{Binding InspectionResults}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border
                                    Width="220"
                                    Height="160"
                                    Margin="2"
                                    BorderBrush="{DynamicResource BorderBrush}"
                                    BorderThickness="1"
                                    MouseLeftButtonDown="Border_MouseLeftButtonDown"
                                    ToolTip="左击选中图像，右击可打开图像所在文件夹">
                                    <Border.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Click="OpenImageFolder_Click" Header="打开图像所在文件夹" />
                                        </ContextMenu>
                                    </Border.ContextMenu>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>

                                        <TextBlock
                                            Grid.Row="0"
                                            Margin="5,2"
                                            FontWeight="Bold"
                                            Text="{Binding PointName}"
                                            TextAlignment="Center" />

                                        <Border
                                            Grid.Row="1"
                                            Margin="5"
                                            BorderBrush="LightGray"
                                            BorderThickness="1">
                                            <Image
                                                Width="200"
                                                Height="100"
                                                Source="{Binding Image}"
                                                Stretch="Uniform"
                                                ToolTip="右击可打开图像所在文件夹">
                                                <Image.ContextMenu>
                                                    <ContextMenu>
                                                        <MenuItem Click="OpenImageFolder_Click" Header="打开图像所在文件夹" />
                                                    </ContextMenu>
                                                </Image.ContextMenu>
                                            </Image>
                                        </Border>

                                        <TextBlock
                                            Grid.Row="2"
                                            Margin="5,2"
                                            FontWeight="Bold"
                                            Text="{Binding Result}"
                                            TextAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Result}" Value="NG">
                                                            <Setter Property="Foreground" Value="Red" />
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Result}" Value="OK">
                                                            <Setter Property="Foreground" Value="Green" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>

        <!--  中间区域  -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="*" />
                <RowDefinition Height="250" />
            </Grid.RowDefinitions>

            <!--  顶部状态信息  -->
            <Grid Grid.Row="0" Margin="10,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Grid.Column="0"
                        Margin="5,0"
                        Text="当前检查进度:" />
                    <TextBlock
                        Grid.Column="1"
                        Margin="5,0"
                        Text="{Binding InspectionProgress}" />

                    <TextBlock
                        Grid.Column="2"
                        Margin="20,0,5,0"
                        Text="NG结果统计:" />
                    <TextBlock
                        Grid.Column="3"
                        Margin="5,0"
                        HorizontalAlignment="Left"
                        Text="{Binding NgCountDisplay}" />

                    <Border
                        Margin="20,0,0,0"
                        Padding="5,2"
                        BorderBrush="{DynamicResource BorderBrush}"
                        BorderThickness="1">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Margin="5,0"
                                VerticalAlignment="Center"
                                Text="离线模拟模式:" />
                            <ToggleButton
                                Margin="5,0"
                                hc:VisualElement.HighlightBrush="{DynamicResource DangerBrush}"
                                IsChecked="{Binding IsOfflineSimulation}"
                                Style="{StaticResource ToggleButtonSwitch}"
                                ToolTip="开启后将不连接实际硬件设备，使用模拟数据进行测试" />
                        </StackPanel>
                    </Border>
                </StackPanel>

            </Grid>

            <!--  图像显示区域  -->
            <Border
                Grid.Row="1"
                Margin="10"
                BorderBrush="{DynamicResource BorderBrush}"
                BorderThickness="1">
                <Image Source="{Binding CurrentImage}" Stretch="Uniform" />
            </Border>

            <!--  日志显示区域  -->
            <Border
                Grid.Row="2"
                Margin="10,0,10,10"
                BorderBrush="{DynamicResource BorderBrush}"
                BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>



                    <!--  日志过滤选项  -->
                    <StackPanel
                        Grid.Row="1"
                        Margin="10,0,10,5"
                        Orientation="Horizontal">
                        <TextBlock
                            Grid.Row="0"
                            Margin="10,5"
                            FontSize="16"
                            FontWeight="Bold"
                            Text="系统日志" />
                        <CheckBox
                            Margin="0,0,10,0"
                            Content="检查服务"
                            IsChecked="{Binding ShowInspectionLogs}" />
                        <CheckBox
                            Margin="0,0,10,0"
                            Content="运动控制卡"
                            IsChecked="{Binding ShowMotionCardLogs}" />
                        <CheckBox
                            Margin="0,0,10,0"
                            Content="轨道控制"
                            IsChecked="{Binding ShowTrackLogs}" />
                        <CheckBox
                            Margin="0,0,10,0"
                            Content="机台"
                            IsChecked="{Binding ShowMachineLogs}" />
                        <CheckBox
                            Margin="0,0,10,0"
                            Content="指示灯服务"
                            IsChecked="{Binding ShowIndicatorLogs}" />
                        <CheckBox
                            Margin="0,0,10,0"
                            Content="错误信息"
                            IsChecked="{Binding ShowErrorLogs}" />
                    </StackPanel>

                    <RichTextBox
                        x:Name="LogRichTextBox"
                        Grid.Row="2"
                        Margin="0"
                        FontFamily="Consolas"
                        HorizontalScrollBarVisibility="Auto"
                        IsReadOnly="True"
                        VerticalScrollBarVisibility="Auto">
                        <RichTextBox.Document>
                            <FlowDocument>
                                <Paragraph>
                                    <Run Foreground="Gray" Text="系统日志将显示在这里..." />
                                </Paragraph>
                            </FlowDocument>
                        </RichTextBox.Document>
                    </RichTextBox>
                </Grid>
            </Border>
        </Grid>

        <!--  右侧控制面板  -->
        <Grid Grid.Column="2" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  综合判定  -->
            <Border
                Grid.Row="0"
                Margin="0,60,0,10"
                Padding="10"
                BorderBrush="{DynamicResource BorderBrush}"
                BorderThickness="1">
                <StackPanel>
                    <StackPanel HorizontalAlignment="Left">
                        <TextBlock
                            Margin="0,0,0,5"
                            HorizontalAlignment="Left"
                            Text="综合判定:" />
                    </StackPanel>
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock
                            MinHeight="90"
                            FontSize="100"
                            FontWeight="Bold"
                            Text="{Binding FinalJudgment}">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding FinalJudgment}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding FinalJudgment}" Value="OK">
                                            <Setter Property="Foreground" Value="Green" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>

                </StackPanel>
            </Border>

            <!--  机种选择  -->
            <StackPanel Grid.Row="1" Margin="0,0,0,10">
                <TextBlock Margin="0,0,0,5" Text="选择机种:" />
                <ComboBox
                    Height="60"
                    DisplayMemberPath="ModelName"
                    FontSize="{StaticResource ButtonFontSize}"
                    ItemsSource="{Binding ProductModels}"
                    SelectedItem="{Binding SelectedModel}" />
            </StackPanel>

            <!--  仅采集复选框  -->
            <CheckBox
                Grid.Row="2"
                Margin="0,0,0,10"
                Content="仅采集"
                FontSize="20"
                IsChecked="{Binding IsCaptureOnly}" />

            <!--  控制按钮  -->
            <StackPanel Grid.Row="3" Margin="0,10">
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Button
                        Grid.Column="0"
                        Width="180"
                        Height="60"
                        Command="{Binding StartCommand}"
                        Content="启动"
                        FontSize="{StaticResource ButtonFontSize}"
                        Style="{StaticResource ButtonPrimary}"
                        ToolTip="启动检查流程或继续已暂停的检查，也可以通过按下物理开始按钮（X1.2）启动" />
                    <!--<Ellipse
                        Grid.Column="1"
                        Width="20"
                        Height="20"
                        Margin="10,0,0,0"
                        Fill="{Binding StartButtonState, Converter={StaticResource BooleanToColorConverter}}"
                        ToolTip="显示物理开始按钮状态（X1.2）" />-->
                </Grid>

                <Button
                    Width="180"
                    Height="60"
                    Margin="0,0,0,20"
                    Command="{Binding PauseCommand}"
                    Content="暂停"
                    FontSize="{StaticResource ButtonFontSize}"
                    Style="{StaticResource ButtonWarning}" />

                <Button
                    Width="180"
                    Height="60"
                    Margin="0,0,10,20"
                    Command="{Binding StopCommand}"
                    Content="停止"
                    FontSize="{StaticResource ButtonFontSize}"
                    Style="{StaticResource ButtonDanger}" />

                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Button
                        Grid.Column="0"
                        Width="180"
                        Height="60"
                        Command="{Binding ResetCommand}"
                        Content="复位"
                        FontSize="{StaticResource ButtonFontSize}"
                        Style="{StaticResource ButtonInfo}"
                        ToolTip="将XYZ轴轨道复位，也可以通过按下物理复位按钮（X1.3）复位" />
                    <!--<Ellipse
                        Grid.Column="1"
                        Width="20"
                        Height="20"
                        Margin="10,0,0,0"
                        Fill="{Binding ResetButtonState, Converter={StaticResource BooleanToColorConverter}}"
                        ToolTip="显示物理复位按钮状态（X1.3）" />-->
                </Grid>

                <Button
                    Width="180"
                    Height="60"
                    Margin="0,0,0,10"
                    Command="{Binding ResetAlarmsCommand}"
                    Content="清除异常"
                    FontSize="{StaticResource ButtonFontSize}"
                    Style="{StaticResource ButtonInfo}"
                    ToolTip="清除设备异常" />
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
