<UserControl
    x:Class="Nickel_Inspect.Views.NewProductWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    Width="500"
    Height="500"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="{DynamicResource RegionBrush}"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="{DynamicResource RegionBrush}" CornerRadius="5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <TextBlock
                Margin="0,0,0,20"
                FontSize="20"
                FontWeight="Bold"
                Foreground="{DynamicResource PrimaryTextBrush}"
                Text="新增机种信息" />

            <StackPanel Grid.Row="1" Margin="0,10">
                <GroupBox
                    Padding="10"
                    Header="基本信息"
                    Style="{StaticResource GroupBoxBaseStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  机种名称  -->
                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="0,5,10,5"
                            VerticalAlignment="Center"
                            Content="机种名称:"
                            Foreground="{DynamicResource PrimaryTextBrush}" />
                        <TextBox
                            x:Name="TxtModelName"
                            Grid.Row="0"
                            Grid.Column="1"
                            Height="30"
                            Margin="0,5"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding ModelName, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  机种代码  -->
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            Margin="0,5,10,5"
                            VerticalAlignment="Center"
                            Content="机种代码:"
                            Foreground="{DynamicResource PrimaryTextBrush}" />
                        <TextBox
                            x:Name="TxtModelCode"
                            Grid.Row="1"
                            Grid.Column="1"
                            Height="30"
                            Margin="0,5"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding ModelCode, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  描述  -->
                        <Label
                            Grid.Row="2"
                            Grid.Column="0"
                            Margin="0,5,10,5"
                            VerticalAlignment="Top"
                            Content="描述信息:"
                            Foreground="{DynamicResource PrimaryTextBrush}" />
                        <TextBox
                            x:Name="TxtDescription"
                            Grid.Row="2"
                            Grid.Column="1"
                            Height="100"
                            Margin="0,5"
                            AcceptsReturn="True"
                            Style="{StaticResource TextBoxBaseStyle}"
                            Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                            TextWrapping="Wrap"
                            VerticalScrollBarVisibility="Auto" />
                    </Grid>
                </GroupBox>
            </StackPanel>

            <!--  按钮区域  -->
            <StackPanel
                Grid.Row="2"
                Margin="0,20,0,0"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    x:Name="BtnSave"
                    Width="80"
                    Height="30"
                    Command="{Binding SaveCommand}"
                    Content="保存"
                    IsEnabled="{Binding IsBusy, Converter={StaticResource BooleanInvertConverter}}"
                    Style="{StaticResource ButtonPrimary}" />
                <Button
                    x:Name="BtnCancel"
                    Width="80"
                    Height="30"
                    Margin="10,0,0,0"
                    Command="{Binding CancelCommand}"
                    Content="取消"
                    Style="{StaticResource ButtonDefault}" />
            </StackPanel>

        </Grid>
    </Border>
</UserControl> 