using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Nickel_Inspect.Services;

namespace Nickel_Inspect.Services.Alarms
{
    /// <summary>
    /// 报警管理器，负责管理和监控所有报警源
    /// </summary>
    public class AlarmManager : IDisposable
    {
        private readonly ConcurrentDictionary<string, IAlarmSignalSource> _alarmSources;
        private readonly ConcurrentDictionary<string, bool> _activeAlarms;
        private readonly ILogService _logService;
        private bool _isMonitoring;
        private readonly Dictionary<string, DateTime> _lastQueryTimes =
            new Dictionary<string, DateTime>();
        private const int MinQueryIntervalMs = 300;

        // 后台任务相关
        private Task _monitorTask;
        private CancellationTokenSource _cts;
        private readonly int _monitoringIntervalMs = 500; // 增加轮询间隔到500ms

        /// <summary>
        /// 报警触发事件
        /// </summary>
        public event EventHandler<AlarmEventArgs> AlarmRaised;

        /// <summary>
        /// 报警解除事件
        /// </summary>
        public event EventHandler<AlarmEventArgs> AlarmCleared;

        /// <summary>
        /// 日志消息事件
        /// </summary>
        public event EventHandler<LogMessageEventArgs> LogMessageReceived;

        /// <summary>
        /// 创建一个新的报警管理器
        /// </summary>
        /// <param name="logService">日志服务</param>
        public AlarmManager(ILogService logService)
        {
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _alarmSources = new ConcurrentDictionary<string, IAlarmSignalSource>();
            _activeAlarms = new ConcurrentDictionary<string, bool>();

            _logService.LogInformation("报警管理器已创建", "报警系统");
        }

        /// <summary>
        /// 注册报警源
        /// </summary>
        /// <param name="alarmSource">要注册的报警源</param>
        public void RegisterAlarmSource(IAlarmSignalSource alarmSource)
        {
            if (alarmSource == null)
                throw new ArgumentNullException(nameof(alarmSource));

            if (_alarmSources.TryAdd(alarmSource.Id, alarmSource))
            {
                alarmSource.Initialize();
                LogInfo($"已注册报警源: {alarmSource.Name}");
            }
            else
            {
                LogWarning($"报警源已存在，无法重复注册: {alarmSource.Name}");
            }
        }

        /// <summary>
        /// 注销报警源
        /// </summary>
        /// <param name="alarmId">报警源ID</param>
        public void UnregisterAlarmSource(string alarmId)
        {
            if (string.IsNullOrEmpty(alarmId))
                throw new ArgumentNullException(nameof(alarmId));

            if (_alarmSources.TryRemove(alarmId, out var alarmSource))
            {
                _activeAlarms.TryRemove(alarmId, out _);
                LogInfo($"已移除报警源: {alarmSource.Name}");
            }
            else
            {
                LogWarning($"报警源不存在，无法移除: {alarmId}");
            }
        }

        /// <summary>
        /// 获取所有报警源
        /// </summary>
        /// <returns>所有注册的报警源</returns>
        public IEnumerable<IAlarmSignalSource> GetAllAlarmSources()
        {
            return _alarmSources.Values.ToList();
        }

        /// <summary>
        /// 获取所有活动的报警
        /// </summary>
        /// <returns>当前活动的报警信息列表</returns>
        public IEnumerable<AlarmInfo> GetActiveAlarms()
        {
            return _activeAlarms
                .Keys.Where(id => _alarmSources.TryGetValue(id, out var source))
                .Select(id => new AlarmInfo
                {
                    Id = id,
                    Name = _alarmSources[id].Name,
                    Description = _alarmSources[id].Description,
                    AlarmType = _alarmSources[id].AlarmType,
                    RelatedDevice = _alarmSources[id].RelatedDevice,
                    TimeOccurred = DateTime.Now,
                })
                .ToList();
        }

        /// <summary>
        /// 复位指定的报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <returns>如果复位成功，返回true；否则返回false</returns>
        public bool ResetAlarm(string alarmId)
        {
            if (string.IsNullOrEmpty(alarmId))
                throw new ArgumentNullException(nameof(alarmId));

            if (_alarmSources.TryGetValue(alarmId, out var source))
            {
                var result = source.ResetAlarm();
                if (result)
                {
                    _logService.LogInformation($"已复位报警: {source.Name}", "报警管理器");
                }
                else
                {
                    _logService.LogWarning($"复位报警失败: {source.Name}", "报警管理器");
                }
                return result;
            }

            _logService.LogWarning($"报警源不存在，无法复位: {alarmId}", "报警管理器");
            return false;
        }

        /// <summary>
        /// 复位所有报警
        /// </summary>
        /// <returns>如果所有报警都复位成功，返回true；否则返回false</returns>
        public bool ResetAllAlarms()
        {
            var allSourceIds = _alarmSources.Keys.ToList();
            var results = new List<bool>();

            foreach (var id in allSourceIds)
            {
                results.Add(ResetAlarm(id));
            }

            bool allSuccess = results.Count > 0 && results.All(x => x);
            if (allSuccess)
            {
                _logService.LogInformation("所有报警已成功复位", "报警管理器");
            }
            else
            {
                _logService.LogWarning("部分或全部报警复位失败", "报警管理器");
            }

            return allSuccess;
        }

        /// <summary>
        /// 启动报警监控
        /// </summary>
        public void StartMonitoring()
        {
            if (!_isMonitoring)
            {
                _isMonitoring = true;
                _cts = new CancellationTokenSource();

                // 创建并启动监控任务
                _monitorTask = Task.Run(
                    async () =>
                    {
                        LogInfo("报警监控任务已启动");

                        try
                        {
                            while (!_cts.Token.IsCancellationRequested)
                            {
                                try
                                {
                                    MonitorAlarms();
                                }
                                catch (Exception ex)
                                {
                                    LogError("报警监控周期内发生异常", ex);
                                }

                                // 使用Task.Delay代替线程休眠，更容易控制取消
                                await Task.Delay(_monitoringIntervalMs, _cts.Token);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            // 正常取消，不需要处理
                            LogInfo("报警监控任务已正常取消");
                        }
                        catch (Exception ex)
                        {
                            LogError("报警监控任务异常退出", ex);
                        }

                        LogInfo("报警监控任务已退出");
                    },
                    _cts.Token
                );

                LogInfo("报警监控已启动");
            }
            else
            {
                LogInfo("报警监控已经处于启动状态");
            }
        }

        /// <summary>
        /// 停止报警监控
        /// </summary>
        public void StopMonitoring()
        {
            if (_isMonitoring)
            {
                _isMonitoring = false;

                // 取消监控任务
                if (_cts != null && !_cts.IsCancellationRequested)
                {
                    _cts.Cancel();

                    try
                    {
                        // 等待任务完成，但设置超时避免无限等待
                        if (_monitorTask != null)
                        {
                            Task.WaitAny(new[] { _monitorTask }, 1000);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogWarning($"等待监控任务结束时发生异常: {ex.Message}");
                    }

                    _cts.Dispose();
                    _cts = null;
                }

                LogInfo("报警监控已停止");
            }
            else
            {
                LogInfo("报警监控已经处于停止状态");
            }
        }

        /// <summary>
        /// 监控所有报警源的状态
        /// </summary>
        private void MonitorAlarms()
        {
            if (!_isMonitoring)
                return;

            var now = DateTime.Now;

            // 批量处理报警源以减少循环次数
            var sourcesToProcess = new List<IAlarmSignalSource>();

            // 创建一个值的副本，避免枚举过程中集合被修改
            var sources = _alarmSources.Values.ToList();

            // 先筛选需要处理的报警源
            foreach (var source in sources)
            {
                string sourceId = source.Id;

                // 实现查询节流，避免对相同信号源的频繁查询
                if (_lastQueryTimes.ContainsKey(sourceId))
                {
                    var lastQueryTime = _lastQueryTimes[sourceId];
                    var elapsed = (now - lastQueryTime).TotalMilliseconds;

                    // 如果距离上次查询间隔不足最小间隔时间，则跳过此次查询
                    if (elapsed < MinQueryIntervalMs)
                    {
                        continue;
                    }
                }

                // 将需要处理的源添加到列表
                sourcesToProcess.Add(source);
                // 更新该信号源的最后查询时间
                _lastQueryTimes[sourceId] = now;
            }

            // 批量处理需要查询的报警源
            foreach (var source in sourcesToProcess)
            {
                try
                {
                    string sourceId = source.Id;
                    bool isActive = source.IsAlarmActive();
                    bool wasActive = _activeAlarms.ContainsKey(sourceId);

                    if (isActive && !wasActive)
                    {
                        // 新报警触发
                        _activeAlarms.TryAdd(sourceId, true);
                        var alarmInfo = new AlarmInfo
                        {
                            Id = sourceId,
                            Name = source.Name,
                            Description = source.Description,
                            AlarmType = source.AlarmType,
                            RelatedDevice = source.RelatedDevice,
                            TimeOccurred = now,
                        };

                        // 需要通过同步上下文或调度器更新UI
                        RaiseEventSafely(() => OnAlarmRaised(alarmInfo));
                        LogWarningSafely($"报警触发: {source.Name} - {source.Description}");
                    }
                    else if (!isActive && wasActive)
                    {
                        // 报警解除
                        _activeAlarms.TryRemove(sourceId, out _);
                        var alarmInfo = new AlarmInfo
                        {
                            Id = sourceId,
                            Name = source.Name,
                            Description = source.Description,
                            AlarmType = source.AlarmType,
                            RelatedDevice = source.RelatedDevice,
                            TimeCleared = now,
                        };

                        // 需要通过同步上下文或调度器更新UI
                        RaiseEventSafely(() => OnAlarmCleared(alarmInfo));
                        LogInfoSafely($"报警解除: {source.Name}");
                    }
                }
                catch (Exception ex)
                {
                    LogErrorSafely($"监控报警源时发生错误: {source.Name}", ex);
                }
            }
        }

        // 线程安全地提交操作到UI线程
        private void RaiseEventSafely(Action action)
        {
            try
            {
                // 如果有同步上下文（如UI线程），则使用它
                if (SynchronizationContext.Current != null)
                {
                    SynchronizationContext.Current.Post(_ => action(), null);
                }
                else
                {
                    // 否则尝试通过UI调度器
                    if (System.Windows.Application.Current != null)
                    {
                        System.Windows.Application.Current.Dispatcher.BeginInvoke(action);
                    }
                    else
                    {
                        // 如果都不可用，直接执行（可能不在UI线程）
                        action();
                    }
                }
            }
            catch
            {
                // 如果发生异常，回退到直接调用
                action();
            }
        }

        // 线程安全地记录信息日志
        private void LogInfoSafely(string message)
        {
            RaiseEventSafely(() => LogInfo(message));
        }

        // 线程安全地记录警告日志
        private void LogWarningSafely(string message)
        {
            RaiseEventSafely(() => LogWarning(message));
        }

        // 线程安全地记录错误日志
        private void LogErrorSafely(string message, Exception ex = null)
        {
            RaiseEventSafely(() => LogError(message, ex));
        }

        /// <summary>
        /// 触发报警事件
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        protected virtual void OnAlarmRaised(AlarmInfo alarmInfo)
        {
            AlarmRaised?.Invoke(this, new AlarmEventArgs(alarmInfo));
        }

        /// <summary>
        /// 触发报警解除事件
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        protected virtual void OnAlarmCleared(AlarmInfo alarmInfo)
        {
            AlarmCleared?.Invoke(this, new AlarmEventArgs(alarmInfo));
        }

        protected virtual void LogInfo(string message)
        {
            _logService.LogInformation(message, "报警系统");
            OnLogMessage("信息", message);
        }

        protected virtual void LogWarning(string message)
        {
            _logService.LogWarning(message, "报警系统");
            OnLogMessage("警告", message);
        }

        protected virtual void LogError(string message, Exception ex = null)
        {
            if (ex != null)
                _logService.LogError(ex, message, "报警系统");
            else
                _logService.LogError(message, "报警系统");

            OnLogMessage("错误", message);
        }

        protected virtual void OnLogMessage(string type, string message)
        {
            LogMessageReceived?.Invoke(this, new LogMessageEventArgs(type, message));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopMonitoring();
            _cts?.Dispose();
            LogInfo("报警管理器已释放");
        }
    }

    /// <summary>
    /// 报警信息类
    /// </summary>
    public class AlarmInfo
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 报警名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 报警描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 报警类型
        /// </summary>
        public AlarmType AlarmType { get; set; }

        /// <summary>
        /// 相关设备
        /// </summary>
        public string RelatedDevice { get; set; }

        /// <summary>
        /// 报警发生时间
        /// </summary>
        public DateTime TimeOccurred { get; set; }

        /// <summary>
        /// 报警解除时间
        /// </summary>
        public DateTime? TimeCleared { get; set; }
    }

    /// <summary>
    /// 报警事件参数类
    /// </summary>
    public class AlarmEventArgs : EventArgs
    {
        /// <summary>
        /// 报警信息
        /// </summary>
        public AlarmInfo AlarmInfo { get; }

        /// <summary>
        /// 创建一个新的报警事件参数
        /// </summary>
        /// <param name="alarmInfo">报警信息</param>
        public AlarmEventArgs(AlarmInfo alarmInfo)
        {
            AlarmInfo = alarmInfo;
        }
    }

    /// <summary>
    /// 日志消息事件参数
    /// </summary>
    public class LogMessageEventArgs : EventArgs
    {
        /// <summary>
        /// 日志类型（信息、警告、错误等）
        /// </summary>
        public string Type { get; }

        /// <summary>
        /// 日志消息内容
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 创建日志消息事件参数
        /// </summary>
        /// <param name="type">日志类型</param>
        /// <param name="message">日志消息内容</param>
        public LogMessageEventArgs(string type, string message)
        {
            Type = type;
            Message = message;
        }
    }
}
