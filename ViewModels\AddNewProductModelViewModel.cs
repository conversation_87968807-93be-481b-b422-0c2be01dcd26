using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using HandyControl.Controls;
using HandyControl.Interactivity;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;

namespace Nickel_Inspect.ViewModels
{
    /// <summary>
    /// 添加新机种视图模型
    /// </summary>
    public class AddNewProductModelViewModel : BindableBase, IDialogAware
    {
        private readonly DatabaseService _databaseService;

        #region 属性

        private string _modelName;

        /// <summary>
        /// 机种名称
        /// </summary>
        public string ModelName
        {
            get => _modelName;
            set => SetProperty(ref _modelName, value);
        }

        private string _modelCode;

        /// <summary>
        /// 机种代码
        /// </summary>
        public string ModelCode
        {
            get => _modelCode;
            set => SetProperty(ref _modelCode, value);
        }

        private string _description;

        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private bool _isBusy;

        /// <summary>
        /// 是否正在处理中
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        #endregion

        #region IDialogAware 成员

        private string _title = "新增机种";
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public event Action<IDialogResult> RequestClose;

        public bool CanCloseDialog()
        {
            return !IsBusy;
        }

        public void OnDialogClosed()
        {
            // 清理资源
        }

        public void OnDialogOpened(IDialogParameters parameters)
        {
            // 初始化
        }

        #endregion

        #region 命令

        /// <summary>
        /// 保存命令
        /// </summary>
        public DelegateCommand SaveCommand { get; private set; }

        /// <summary>
        /// 取消命令
        /// </summary>
        public DelegateCommand CancelCommand { get; private set; }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="databaseService">数据库服务</param>
        public AddNewProductModelViewModel(DatabaseService databaseService)
        {
            _databaseService =
                databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            // 初始化命令
            SaveCommand = new DelegateCommand(ExecuteSaveCommand, CanExecuteSaveCommand)
                .ObservesProperty(() => ModelName)
                .ObservesProperty(() => ModelCode)
                .ObservesProperty(() => IsBusy);

            CancelCommand = new DelegateCommand(ExecuteCancelCommand);
        }

        /// <summary>
        /// 判断是否可以执行保存命令
        /// </summary>
        private bool CanExecuteSaveCommand()
        {
            return !IsBusy
                && !string.IsNullOrWhiteSpace(ModelName)
                && !string.IsNullOrWhiteSpace(ModelCode);
        }

        /// <summary>
        /// 执行保存命令
        /// </summary>
        private async void ExecuteSaveCommand()
        {
            try
            {
                Console.WriteLine("保存命令开始执行");
                IsBusy = true;
                Mouse.OverrideCursor = Cursors.Wait;

                // 创建新机种对象
                var newModel = new ProductModel
                {
                    ModelName = ModelName.Trim(),
                    ModelCode = ModelCode.Trim(),
                    Description = Description?.Trim(),
                    CreateTime = DateTime.Now,
                    IsActive = true,
                };

                Console.WriteLine("开始保存数据到数据库");
                // 保存到数据库
                int modelId = await _databaseService.AddProductModelAsync(newModel);
                Console.WriteLine($"数据保存成功，模型ID：{modelId}");

                // 特殊处理：延迟后强制关闭对话框
                await ForceSafeClose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存操作异常：{ex.Message}");
                MessageBox.Show(
                    $"保存失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                IsBusy = false;
                Mouse.OverrideCursor = null;
                Console.WriteLine("保存命令执行完成");
            }
        }

        /// <summary>
        /// 执行取消命令
        /// </summary>
        private void ExecuteCancelCommand()
        {
            // 关闭窗口
            RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
        }

        private async Task ForceSafeClose()
        {
            try
            {
                Console.WriteLine("尝试延迟关闭对话框...");
                // 延迟一小段时间
                await Task.Delay(500);

                // 使用UI线程调用
                Application.Current.Dispatcher.Invoke(() =>
                {
                    try
                    {
                        Console.WriteLine("UI线程准备关闭对话框...");
                        var dialogResult = new DialogResult(ButtonResult.OK);
                        RequestClose?.Invoke(dialogResult);
                        Console.WriteLine("UI线程已发送关闭请求");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"关闭对话框时出错: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"强制关闭时异常: {ex.Message}");
            }
        }
    }
}
