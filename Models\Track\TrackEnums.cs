namespace Nickel_Inspect.Models.Track
{
    /// <summary>
    /// 轨道运行模式
    /// </summary>
    public enum TrackMode
    {
        /// <summary>
        /// 正常模式
        /// </summary>
        Normal,

        /// <summary>
        /// 直通模式
        /// </summary>
        PassThrough,

        /// <summary>
        /// 手动模式
        /// </summary>
        Manual,
    }

    /// <summary>
    /// 轨道运行方向
    /// </summary>
    public enum TrackDirection
    {
        /// <summary>
        /// 从左到右
        /// </summary>
        LeftToRight,

        /// <summary>
        /// 从右到左
        /// </summary>
        RightToLeft,
    }
}
