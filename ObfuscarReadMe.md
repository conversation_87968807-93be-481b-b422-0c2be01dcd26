# Obfuscar 代码混淆说明

## 概述

本项目已配置使用 Obfuscar 进行代码混淆，以保护知识产权。混淆过程已自动集成到构建流程中，当您构建 x64 Release 版本时，将自动执行混淆操作。

## 工作原理

1. 混淆仅应用于 x64 Release 配置的构建输出
2. 混淆后的文件将自动保存到 `bin\x64\Release\Obfuscated` 目录
3. 为方便部署，混淆后的文件会自动复制到 `bin\x64\Release\Obfuscated_Deploy` 目录

## 配置说明

混淆配置文件位于项目根目录下的 `obfuscar.xml`。该文件定义了哪些代码需要混淆，哪些需要排除：

- 用户界面 (UI) 相关的类不会被混淆
- 数据模型和配置类不会被混淆
- 第三方库和系统组件不会被混淆
- 服务层和业务逻辑将被混淆

## 如何使用

1. 要生成混淆后的程序，只需构建项目的 x64 Release 版本
2. 构建完成后，混淆过程将自动执行
3. 使用 `bin\x64\Release\Obfuscated_Deploy` 目录中的文件进行部署

## 自定义混淆

如需修改混淆配置，请编辑 `obfuscar.xml` 文件：

- 要排除某个类或命名空间不被混淆，使用 `<Skip match="命名空间.类名" />`
- 要排除整个程序集，使用 `<SkipAssembly name="程序集名称" />`

## 故障排除

如果混淆过程失败：

1. 确保已安装 Obfuscar 2.2.47 或更高版本
2. 检查 Visual Studio 输出窗口中的错误信息
3. 确认在 x64 Release 配置下构建项目

如有其他问题，请查阅 Obfuscar 官方文档: https://docs.obfuscar.com/