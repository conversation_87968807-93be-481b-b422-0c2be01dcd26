using System;
using System.Collections.Generic;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;

namespace Nickel_Inspect.Services.Alarms
{
    /// <summary>
    /// 数字输入报警源类，用于监控数字输入信号（如伺服报警信号）
    /// </summary>
    public class DigitalInputAlarmSource : IAlarmSignalSource
    {
        private readonly string _id;
        private readonly string _name;
        private readonly AlarmType _alarmType;
        private readonly string _relatedDevice;
        private readonly string _description;
        private readonly BkMotionCard _motionCard;
        private readonly IoConfig _ioConfig;
        private readonly ILogService _logService;
        private bool _isEnabled = true;

        // 缓存上次获取到的状态
        private bool? _lastState = null;

        // 上次查询时间
        private DateTime _lastQueryTime = DateTime.MinValue;

        // 状态缓存有效期(毫秒)，扩大到500毫秒
        private const int StateCacheDurationMs = 500;

        /// <summary>
        /// 日志消息事件
        /// </summary>
        public event EventHandler<LogMessageEventArgs> LogMessageReceived;

        public string Id => _id;
        public string Name => _name;
        public AlarmType AlarmType => _alarmType;
        public string RelatedDevice => _relatedDevice;
        public string Description => _description;

        /// <summary>
        /// 创建一个新的数字输入报警源
        /// </summary>
        /// <param name="id">报警源ID</param>
        /// <param name="ioConfig">IO配置</param>
        /// <param name="alarmType">报警类型</param>
        /// <param name="relatedDevice">相关设备</param>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        public DigitalInputAlarmSource(
            string id,
            IoConfig ioConfig,
            AlarmType alarmType,
            string relatedDevice,
            BkMotionCard motionCard,
            ILogService logService
        )
        {
            _id = id;
            _ioConfig = ioConfig ?? throw new ArgumentNullException(nameof(ioConfig));
            _name = ioConfig.Name;
            _alarmType = alarmType;
            _relatedDevice = relatedDevice;
            _description = ioConfig.Description;
            _motionCard = motionCard ?? throw new ArgumentNullException(nameof(motionCard));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));

            LogInfo($"创建数字输入报警源: {_name}");
        }

        protected virtual void LogInfo(string message)
        {
            _logService.LogInformation(message, "数字信号输入报警源");
            OnLogMessage("信息", message);
        }

        protected virtual void LogWarning(string message)
        {
            _logService.LogWarning(message, "数字信号输入报警源");
            OnLogMessage("警告", message);
        }

        protected virtual void LogError(string message, Exception ex = null)
        {
            if (ex != null)
                _logService.LogError(ex, message, "数字信号输入报警源");
            else
                _logService.LogError(message, "数字信号输入报警源");

            OnLogMessage("错误", message);
        }

        protected virtual void OnLogMessage(string type, string message)
        {
            LogMessageReceived?.Invoke(this, new LogMessageEventArgs(type, message));
        }

        /// <summary>
        /// 检查报警是否激活
        /// </summary>
        /// <returns>如果报警被触发，返回true；否则返回false</returns>
        public bool IsAlarmActive()
        {
            if (!_isEnabled)
                return false;

            // 检查是否可以使用缓存
            var now = DateTime.Now;
            var elapsed = (now - _lastQueryTime).TotalMilliseconds;

            if (_lastState.HasValue && elapsed < StateCacheDurationMs)
            {
                // 使用缓存的状态，减少IO查询
                return _lastState.Value;
            }

            try
            {
                bool inputState;

                // 根据通道组选择不同的读取方法
                if (_ioConfig.Group == "X")
                {
                    // X组IO
                    inputState = _motionCard.GetInputBitStatus(
                        _ioConfig.SubGroup,
                        _ioConfig.IoBitIndex
                    );
                }
                else if (_ioConfig.Group == "Z")
                {
                    // Z组IO
                    inputState = _motionCard.GetZ0BitStatus(_ioConfig.IoBitIndex);
                }
                else
                {
                    LogWarning($"不支持的IO组: {_ioConfig.Group}");
                    return false;
                }

                // 对于常闭信号，低电平表示报警
                bool result = _ioConfig.Type == "NC" ? !inputState : inputState;

                // 更新缓存
                _lastState = result;
                _lastQueryTime = now;

                return result;
            }
            catch (Exception ex)
            {
                LogError($"读取报警信号失败: {_name}", ex);
                return false;
            }
        }

        /// <summary>
        /// 初始化报警源
        /// </summary>
        public void Initialize()
        {
            // 无需特殊初始化
            LogInfo($"初始化报警源: {_name}");
        }

        /// <summary>
        /// 启用报警监控
        /// </summary>
        public void Enable()
        {
            _isEnabled = true;
            LogInfo($"启用报警源: {_name}");
        }

        /// <summary>
        /// 禁用报警监控
        /// </summary>
        public void Disable()
        {
            _isEnabled = false;
            LogInfo($"禁用报警源: {_name}");
        }

        /// <summary>
        /// 复位报警
        /// </summary>
        /// <returns>如果复位成功，返回true；否则返回false</returns>
        public bool ResetAlarm()
        {
            // 数字输入报警源无法直接复位，需要外部操作
            LogInfo($"尝试复位报警源: {_name}（注意：数字输入报警需要外部设备复位）");
            return true;
        }

        /// <summary>
        /// 获取附加信息
        /// </summary>
        /// <returns>包含附加信息的字典</returns>
        public Dictionary<string, object> GetAdditionalInfo()
        {
            return new Dictionary<string, object>
            {
                { "IoGroup", _ioConfig.Group },
                { "IoSubGroup", _ioConfig.SubGroup },
                { "IoBitIndex", _ioConfig.IoBitIndex },
                { "IoType", _ioConfig.Type },
            };
        }
    }
}
