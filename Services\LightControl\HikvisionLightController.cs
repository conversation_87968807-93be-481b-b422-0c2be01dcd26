using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nickel_Inspect.Models.LightControl;

namespace Nickel_Inspect.Services.LightControl
{
    /// <summary>
    /// 海康品牌光源控制器实现
    /// </summary>
    public class HikvisionLightController : LightControllerBase
    {
        private readonly Dictionary<int, int> _channelBrightness = new Dictionary<int, int>();

        // Hikvision控制器协议常量
        private const byte STX = 0x02; // 起始标记
        private const byte ETX = 0x03; // 结束标记
        private const byte CMD_SET = 0x31; // 设置命令
        private const byte CMD_GET = 0x32; // 获取命令

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">光源控制器配置</param>
        public HikvisionLightController(LightController config)
            : base(config)
        {
            // 初始化通道亮度缓存
            for (int i = 1; i <= config.ChannelCount; i++)
            {
                _channelBrightness[i] = 0;
            }
        }

        /// <summary>
        /// 设置指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <param name="brightness">亮度值</param>
        /// <returns>是否设置成功</returns>
        public override async Task<bool> SetChannelBrightnessAsync(int channel, int brightness)
        {
            if (channel < 1 || channel > _config.ChannelCount)
            {
                Console.WriteLine($"通道号无效: {channel}");
                return false;
            }

            // 限制亮度范围
            int brightnessValue = Math.Min(Math.Max(brightness, 0), _config.MaxBrightness);

            // 海康控制器使用二进制协议
            // 协议格式: STX(0x02) + CMD(0x31表示设置) + 通道号(1字节) + 亮度值(2字节) + ETX(0x03) + 校验和(1字节)
            byte channelByte = (byte)channel;
            byte[] brightnessByte = BitConverter.GetBytes((short)brightnessValue);

            // 构建命令
            byte[] command = new byte[7];
            command[0] = STX;
            command[1] = CMD_SET;
            command[2] = channelByte;
            command[3] = brightnessByte[0]; // 低字节
            command[4] = brightnessByte[1]; // 高字节
            command[5] = ETX;
            command[6] = CalculateChecksum(command, 1, 5); // 计算校验和

            bool result = await SendCommandAsync(command);
            if (result)
            {
                byte[] response = await ReceiveResponseAsync();
                if (
                    response != null
                    && response.Length >= 3
                    && response[0] == STX
                    && response[1] == CMD_SET
                )
                {
                    // 解析响应，看是否成功
                    if (response[2] == 0x00)
                    {
                        _channelBrightness[channel] = brightnessValue;
                        return true;
                    }
                }
                return false;
            }
            return result;
        }

        /// <summary>
        /// 获取指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <returns>通道亮度值</returns>
        public override async Task<int> GetChannelBrightnessAsync(int channel)
        {
            if (channel < 1 || channel > _config.ChannelCount)
            {
                Console.WriteLine($"通道号无效: {channel}");
                return -1;
            }

            // 协议格式: STX(0x02) + CMD(0x32表示获取) + 通道号(1字节) + ETX(0x03) + 校验和(1字节)
            byte channelByte = (byte)channel;

            // 构建命令
            byte[] command = new byte[5];
            command[0] = STX;
            command[1] = CMD_GET;
            command[2] = channelByte;
            command[3] = ETX;
            command[4] = CalculateChecksum(command, 1, 3); // 计算校验和

            bool sendResult = await SendCommandAsync(command);
            if (!sendResult)
            {
                return _channelBrightness.ContainsKey(channel) ? _channelBrightness[channel] : 0;
            }

            byte[] response = await ReceiveResponseAsync();
            if (response == null || response.Length < 6)
            {
                return _channelBrightness.ContainsKey(channel) ? _channelBrightness[channel] : 0;
            }

            try
            {
                // 响应格式: STX(0x02) + CMD(0x32) + 通道号(1字节) + 亮度值(2字节) + ETX(0x03) + 校验和(1字节)
                if (response[0] == STX && response[1] == CMD_GET && response[2] == channelByte)
                {
                    short brightness = BitConverter.ToInt16(
                        new byte[] { response[3], response[4] },
                        0
                    );
                    _channelBrightness[channel] = brightness;
                    return brightness;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析响应失败: {ex.Message}");
            }

            return _channelBrightness.ContainsKey(channel) ? _channelBrightness[channel] : 0;
        }

        /// <summary>
        /// 计算校验和
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="start">起始位置</param>
        /// <param name="length">长度</param>
        /// <returns>校验和</returns>
        private byte CalculateChecksum(byte[] data, int start, int length)
        {
            byte sum = 0;
            for (int i = start; i < start + length; i++)
            {
                sum += data[i];
            }
            return sum;
        }
    }
}
