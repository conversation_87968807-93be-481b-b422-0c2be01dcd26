---
description: 
globs: 
alwaysApply: false
---
# 镍片检查系统配置与调试指南

## 配置文件

### DeviceConfiguration.json
- **文件路径**：[DeviceConfiguration.json](mdc:DeviceConfiguration.json)
- **功能**：配置设备硬件参数，包括轴参数、IO点位、报警设置等
- **关键部分**：
  - `axesConfig`: XYZ轴运动参数配置
  - `ioConfig`: 输入输出点位配置
  - `alarms`: 报警配置
  - `lightControllers`: 光源控制器配置
  - `enableOfflineSimulation`: 离线模拟模式开关

### ModbusConfig.json
- **文件路径**：[ModbusConfig.json](mdc:ModbusConfig.json)
- **功能**：配置Modbus通信设置
- **关键部分**：
  - TCP/IP通信参数
  - 地址映射
  - 通信超时设置

## 调试方法

### 离线模拟模式
- 在`DeviceConfiguration.json`中设置`"enableOfflineSimulation": true`
- 设置`"offlineSimulationDelayMs": 2000`控制模拟延迟
- 无需连接实际硬件即可调试大部分功能

### IO点位监控
- 使用`IOMonitorView`界面实时监控IO点位状态
- 查看输入信号变化和输出信号控制效果
- 帮助定位硬件连接和通信问题

### 视觉测试
- 使用`VisionProTestView`界面加载视觉工具块文件
- 手动触发图像采集和处理
- 验证视觉算法是否正常工作

### 轨道控制调试
- 使用`TrackControlView`界面手动控制轨道动作
- 测试进板、定位、出板等功能
- 确认传感器和气缸工作正常

### 日志系统
- 使用`ILogService`接口记录关键操作和错误
- 日志查询界面可以过滤和查看历史日志
- 日志格式：级别、时间、模块、消息、详情

### 报警系统
- 实时显示当前活动报警
- 报警触发时自动停止相关操作
- 可手动复位和确认报警

## 常见问题与解决方案

### 硬件通信问题
- 检查设备连接和电源
- 确认IO配置与实际接线一致
- 使用IO监控工具检查信号状态

### 视觉处理问题
- 检查相机连接和曝光设置
- 确认VPP文件路径正确且能正常加载
- 调整光源亮度和相机参数

### 运动控制问题
- 检查轴参数配置是否合理
- 确认限位开关和原点开关工作正常
- 调整加速度和速度参数

### 同步问题
- 检查检测和轨道服务之间的通信
- 确认事件订阅和触发正常
- 调整等待超时参数

