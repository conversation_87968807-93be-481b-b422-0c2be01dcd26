﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nickel_Inspect.Models.LightControl;

namespace Nickel_Inspect.Services.LightControl
{
    /// <summary>
    /// 轶旭品牌光源控制器实现
    /// </summary>
    public class YixuLightController : LightControllerBase
    {
        private readonly Dictionary<int, int> _channelBrightness = new Dictionary<int, int>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config"></param>
        public YixuLightController(LightController config)
            : base(config)
        { // 初始化通道亮度缓存
            for (int i = 1; i <= config.ChannelCount; i++)
            {
                _channelBrightness[i] = 0;
            }
        }

        public override async Task<int> GetChannelBrightnessAsync(int channel)
        {
            if (channel < 1 || channel > _config.ChannelCount)
            {
                Console.WriteLine($"通道号无效: {channel}");
                return -1;
            }
            // 轶旭控制器使用命令格式: "S[通道号:A|B|C|D]#" 读取亮度
            // 例如: "SA#" 表示读取通道1的亮度  返回数据 A0222表示通道1的亮度为222
            string channel_str;
            switch (channel)
            {
                case 1:
                    channel_str = "A";
                    break;
                case 2:
                    channel_str = "B";
                    break;
                case 3:
                    channel_str = "C";
                    break;
                case 4:
                    channel_str = "D";
                    break;
                default:
                    Console.WriteLine($"通道号无效: {channel}");
                    return -1;
            }
            string command = $"S{channel_str}#";
            byte[] commandBytes = Encoding.ASCII.GetBytes(command);
            bool result = await SendCommandAsync(commandBytes);
            if (result)
            {
                byte[] responseBytes = await ReceiveResponseAsync();
                string response = Encoding.ASCII.GetString(responseBytes);
                if (response.Length == 5)
                {
                    return int.Parse(response.Substring(1));
                }
                else
                {
                    Console.WriteLine($"读取亮度失败: {response}");
                }
            }
            return -1;
        }

        /// <summary>
        /// 设置指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <param name="brightness">亮度值</param>
        /// <returns>是否设置成功</returns>
        public override async Task<bool> SetChannelBrightnessAsync(int channel, int brightness)
        {
            if (channel < 1 || channel > _config.ChannelCount)
            {
                Console.WriteLine($"通道号无效: {channel}");
                return false;
            }

            // 限制亮度范围
            int brightnessValue = Math.Min(Math.Max(brightness, 0), _config.MaxBrightness);

            // 轶旭控制器使用命令格式: "S[通道号:A|B|C|D][亮度值:xxxx]#"
            // 例如: "SA0222#" 表示设置通道1的亮度为222
            string chnanel_str;
            // 根据通道号设置对应的通道字符串
            switch (channel)
            {
                case 1:
                    chnanel_str = "A";
                    break;
                case 2:
                    chnanel_str = "B";
                    break;
                case 3:
                    chnanel_str = "C";
                    break;
                case 4:
                    chnanel_str = "D";
                    break;
                default:
                    Console.WriteLine($"通道号无效: {channel}");
                    return false;
            }
            string command = $"S{chnanel_str}{brightness.ToString().PadLeft(4, '0')}#";
            byte[] commandBytes = Encoding.ASCII.GetBytes(command);
            bool result = await SendCommandAsync(commandBytes);
            if (result)
            {
                _channelBrightness[channel] = brightnessValue;
            }
            return result;
        }
    }
}
