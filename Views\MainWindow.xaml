<hc:Window
    x:Class="Nickel_Inspect.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    Title="{Binding Title}"
    Width="1366"
    Height="768"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Icon="/aoi_icon.ico"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <TabControl Grid.Row="0">
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="SelectionChanged">
                    <i:InvokeCommandAction Command="{Binding TabSelectionChangedCommand}" PassEventArgsToCommand="True" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
            <TabItem Header="主界面">
                <Grid>
                    <local:InspectionView x:Name="InspectionViewControl" DataContext="{Binding InspectionViewModel}" />
                </Grid>
            </TabItem>
            <TabItem
                Height="28"
                VerticalAlignment="Top"
                Header="检查点配置">
                <Grid>
                    <local:ConfigPointView DataContext="{Binding ConfigPointViewModel}" />
                </Grid>
            </TabItem>
            <TabItem Header="系统设置" Tag="SystemSettings">
                <Grid>
                    <local:IOMonitorView DataContext="{Binding IOMonitorViewModel}" />
                </Grid>
            </TabItem>
            <TabItem Header="VisionPro测试" Tag="VisionProTest">
                <Grid>
                    <local:VisionProTestView DataContext="{Binding VisionProTestViewModel}" />
                </Grid>
            </TabItem>
            <TabItem Header="日志查询" Tag="LogQuery">
                <Grid>
                    <local:LogQueryView DataContext="{Binding LogQueryViewModel}" />
                </Grid>
            </TabItem>
        </TabControl>

        <!--  状态栏  -->
        <StatusBar Grid.Row="1" Height="25">
            <StatusBar.ItemsPanel>
                <ItemsPanelTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                    </Grid>
                </ItemsPanelTemplate>
            </StatusBar.ItemsPanel>

            <StatusBarItem Grid.Column="0">
                <TextBlock Margin="5,0" Text="{Binding XAxisPosition, StringFormat='X: {0:F2}'}" />
            </StatusBarItem>

            <StatusBarItem Grid.Column="1">
                <TextBlock Margin="5,0" Text="{Binding YAxisPosition, StringFormat='Y: {0:F2}'}" />
            </StatusBarItem>

            <StatusBarItem Grid.Column="2">
                <TextBlock Margin="5,0" Text="{Binding ZAxisPosition, StringFormat='Z: {0:F2}'}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</hc:Window>
