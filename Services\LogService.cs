using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Nickel_Inspect.Models;

namespace Nickel_Inspect.Services
{
    /// <summary>
    /// 日志服务实现类
    /// </summary>
    public class LogService : ILogService
    {
        private readonly DatabaseService _databaseService;
        private readonly ConcurrentQueue<LogItem> _logQueue = new ConcurrentQueue<LogItem>();
        private readonly SemaphoreSlim _dbSemaphore = new SemaphoreSlim(1, 1);
        private readonly Timer _processTimer;
        private bool _isProcessing = false;
        private bool _disposed = false;

        private class LogItem
        {
            public string LogType { get; set; }
            public string Source { get; set; }
            public string Message { get; set; }
            public string ExceptionDetails { get; set; }
            public DateTime Timestamp { get; set; }
        }

        public LogService(DatabaseService databaseService)
        {
            _databaseService =
                databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            // 启动定时处理日志队列的计时器（每500毫秒处理一次）
            _processTimer = new Timer(ProcessLogQueue, null, 0, 500);

            // 记录启动日志
            EnqueueLog("信息", "系统", "日志服务已初始化");
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public void LogInformation(string message, string source = "系统")
        {
            EnqueueLog("信息", source, message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public void LogWarning(string message, string source = "系统")
        {
            EnqueueLog("警告", source, message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public void LogError(string message, string source = "系统")
        {
            EnqueueLog("错误", source, message);
        }

        /// <summary>
        /// 记录错误日志（带异常信息）
        /// </summary>
        public void LogError(Exception exception, string message, string source = "系统")
        {
            string exceptionDetails =
                exception != null ? $"{exception.Message}\n{exception.StackTrace}" : null;

            EnqueueLog("错误", source, message, exceptionDetails);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void LogDebug(string message, string source = "系统")
        {
            EnqueueLog("调试", source, message);
        }

        /// <summary>
        /// 将日志加入队列
        /// </summary>
        private void EnqueueLog(
            string logType,
            string source,
            string message,
            string exceptionDetails = null
        )
        {
            _logQueue.Enqueue(
                new LogItem
                {
                    LogType = logType,
                    Source = source,
                    Message = message,
                    ExceptionDetails = exceptionDetails,
                    Timestamp = DateTime.Now,
                }
            );
        }

        /// <summary>
        /// 处理日志队列
        /// </summary>
        private async void ProcessLogQueue(object state)
        {
            // 避免重入
            if (_isProcessing || _logQueue.IsEmpty)
                return;

            _isProcessing = true;

            try
            {
                // 尝试获取数据库锁，最多等待100毫秒
                if (await _dbSemaphore.WaitAsync(100))
                {
                    try
                    {
                        // 批量处理队列中的日志（最多处理20条）
                        int count = 0;
                        while (_logQueue.TryDequeue(out var logItem) && count < 20)
                        {
                            try
                            {
                                await _databaseService.AddLogAsync(
                                    logItem.LogType,
                                    logItem.Source,
                                    logItem.Message,
                                    logItem.ExceptionDetails,
                                    logItem.Timestamp
                                );
                                count++;
                            }
                            catch (Exception ex)
                            {
                                // 如果数据库写入失败，记录到控制台
                                System.Diagnostics.Debug.WriteLine(
                                    $"写入数据库日志失败: {ex.Message}"
                                );
                            }
                        }
                    }
                    finally
                    {
                        // 释放数据库锁
                        _dbSemaphore.Release();
                    }
                }
            }
            finally
            {
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 获取最近的日志记录
        /// </summary>
        public async Task<IEnumerable<SystemLog>> GetRecentLogsAsync(
            int count = 1000,
            string logType = null,
            string source = null
        )
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                return await _databaseService.GetRecentLogsAsync(count, logType, source);
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// 获取指定日期范围内的日志
        /// </summary>
        public async Task<IEnumerable<SystemLog>> GetLogsByDateRangeAsync(
            DateTime startDate,
            DateTime endDate,
            string logType = null,
            string source = null
        )
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                return await _databaseService.GetLogsByDateRangeAsync(
                    startDate,
                    endDate,
                    logType,
                    source
                );
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// 按日志类型和日期范围获取日志
        /// </summary>
        public async Task<IEnumerable<SystemLog>> GetLogsByTypeAndDateRangeAsync(
            List<string> logTypes,
            DateTime startDate,
            DateTime endDate,
            string source = null
        )
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                return await _databaseService.GetLogsByTypeAndDateRangeAsync(
                    logTypes,
                    startDate,
                    endDate,
                    source
                );
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// 获取所有不同的日志来源
        /// </summary>
        public async Task<IEnumerable<string>> GetDistinctSourcesAsync()
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                return await _databaseService.GetDistinctSourcesAsync();
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        public async Task<int> CleanupLogsAsync(DateTime beforeDate)
        {
            await _dbSemaphore.WaitAsync();
            try
            {
                return await _databaseService.CleanupLogsAsync(beforeDate);
            }
            finally
            {
                _dbSemaphore.Release();
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~LogService()
        {
            Dispose(false);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
                return;

            if (disposing)
            {
                // 确保处理完队列中的所有日志
                ProcessRemainingLogs();

                // 释放托管资源
                _processTimer?.Dispose();
                _dbSemaphore?.Dispose();
            }

            _disposed = true;
        }

        /// <summary>
        /// 处理队列中剩余的日志
        /// </summary>
        private void ProcessRemainingLogs()
        {
            try
            {
                // 最多重试5次，确保尽可能多的日志被写入
                for (int attempt = 0; attempt < 5 && !_logQueue.IsEmpty; attempt++)
                {
                    if (_dbSemaphore.Wait(1000))
                    {
                        try
                        {
                            while (_logQueue.TryDequeue(out var logItem))
                            {
                                try
                                {
                                    _databaseService
                                        .AddLogAsync(
                                            logItem.LogType,
                                            logItem.Source,
                                            logItem.Message,
                                            logItem.ExceptionDetails,
                                            logItem.Timestamp
                                        )
                                        .GetAwaiter()
                                        .GetResult();
                                }
                                catch
                                {
                                    // 忽略错误，尽量完成剩余条目
                                }
                            }
                        }
                        finally
                        {
                            _dbSemaphore.Release();
                        }
                    }
                }
            }
            catch
            {
                // 忽略任何错误，以确保析构/释放过程不会抛出异常
            }
        }
    }
}
