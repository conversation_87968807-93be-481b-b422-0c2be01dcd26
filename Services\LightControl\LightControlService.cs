using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.LightControl;

namespace Nickel_Inspect.Services.LightControl
{
    /// <summary>
    /// 光源控制服务实现
    /// </summary>
    public class LightControlService : ILightControlService
    {
        private readonly Dictionary<int, LightControllerBase> _controllers =
            new Dictionary<int, LightControllerBase>();
        private readonly object _lock = new object();
        private bool _isInitialized = false;
        private readonly ILogService _logService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logService">日志服务</param>
        public LightControlService(ILogService logService)
        {
            _logService = logService;
        }

        /// <summary>
        /// 初始化光源控制服务
        /// </summary>
        /// <returns>初始化是否成功</returns>
        public async Task<bool> InitializeAsync()
        {
            if (_isInitialized)
            {
                return true;
            }

            try
            {
                // 从配置或数据库加载光源控制器配置
                var controllerConfigs = await LoadControllerConfigsAsync();

                foreach (var config in controllerConfigs)
                {
                    // 根据品牌创建对应的控制器实例
                    LightControllerBase controller = CreateControllerInstance(config);
                    if (controller != null)
                    {
                        bool initResult = await controller.InitializeAsync();
                        if (initResult)
                        {
                            lock (_lock)
                            {
                                _controllers[config.ControllerId] = controller;
                            }
                            _logService.LogInformation(
                                $"光源控制器初始化成功: {config.Name}",
                                "光源控制服务"
                            );
                        }
                        else
                        {
                            _logService.LogError(
                                $"光源控制器初始化失败: {config.Name}",
                                "光源控制服务"
                            );
                        }
                    }
                }

                _isInitialized = true;
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"初始化光源控制服务失败: {ex.Message}", "光源控制服务");
                return false;
            }
        }

        /// <summary>
        /// 关闭光源控制服务
        /// </summary>
        public async Task ShutdownAsync()
        {
            try
            {
                List<LightControllerBase> controllers;
                lock (_lock)
                {
                    controllers = _controllers.Values.ToList();
                    _controllers.Clear();
                }

                foreach (var controller in controllers)
                {
                    await controller.ShutdownAsync();
                    controller.Dispose();
                }

                _isInitialized = false;
            }
            catch (Exception ex)
            {
                _logService.LogError($"关闭光源控制服务失败: {ex.Message}", "光源控制服务");
            }
        }

        /// <summary>
        /// 应用检查点的光源设置
        /// </summary>
        /// <param name="point">检查点</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> ApplyLightSettingsAsync(InspectionPoint point)
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                LightControllerBase controller;
                lock (_lock)
                {
                    if (!_controllers.TryGetValue(point.LightControllerId, out controller))
                    {
                        _logService.LogError(
                            $"找不到指定的光源控制器: {point.LightControllerId}",
                            "光源控制服务"
                        );
                        return false;
                    }
                }

                // 检查是否有自定义通道设置
                if (!string.IsNullOrEmpty(point.CustomChannelSettings))
                {
                    bool result = true;
                    var channelSettings = ParseCustomChannelSettings(point.CustomChannelSettings);
                    foreach (var setting in channelSettings)
                    {
                        result &= await controller.SetChannelBrightnessAsync(
                            setting.Key,
                            setting.Value
                        );
                    }
                    return result;
                }
                else if (controller.Config.SupportsRgb)
                {
                    // 如果支持RGB，则设置RGB通道
                    return await controller.SetRgbBrightnessAsync(
                        point.RedChannelBrightness,
                        point.GreenChannelBrightness,
                        point.BlueChannelBrightness
                    );
                }
                else
                {
                    // 否则设置白光通道
                    return await controller.SetChannelBrightnessAsync(
                        4,
                        point.WhiteChannelBrightness
                    );
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"应用光源设置失败: {ex.Message}", "光源控制服务");
                return false;
            }
        }

        /// <summary>
        /// 设置指定控制器的指定通道亮度
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <param name="channel">通道号</param>
        /// <param name="brightness">亮度值 (0-255)</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> SetChannelBrightnessAsync(
            int controllerId,
            int channel,
            int brightness
        )
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                LightControllerBase controller;
                lock (_lock)
                {
                    if (!_controllers.TryGetValue(controllerId, out controller))
                    {
                        _logService.LogError(
                            $"找不到指定的光源控制器: {controllerId}",
                            "光源控制服务"
                        );
                        return false;
                    }
                }
                _logService.LogInformation(
                    $"设置光源通道亮度: 控制器ID={controllerId}, 通道={channel}, 亮度={brightness}",
                    "光源控制服务"
                );
                return await controller.SetChannelBrightnessAsync(channel, brightness);
            }
            catch (Exception ex)
            {
                _logService.LogError($"设置通道亮度失败: {ex.Message}", "光源控制服务");
                return false;
            }
        }

        /// <summary>
        /// 设置RGB三通道亮度
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <param name="redBrightness">红色通道亮度</param>
        /// <param name="greenBrightness">绿色通道亮度</param>
        /// <param name="blueBrightness">蓝色通道亮度</param>
        /// <returns>设置是否成功</returns>
        public async Task<bool> SetRgbBrightnessAsync(
            int controllerId,
            int redBrightness,
            int greenBrightness,
            int blueBrightness
        )
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                LightControllerBase controller;
                lock (_lock)
                {
                    if (!_controllers.TryGetValue(controllerId, out controller))
                    {
                        _logService.LogError(
                            $"找不到指定的光源控制器: {controllerId}",
                            "光源控制服务"
                        );
                        return false;
                    }
                }

                return await controller.SetRgbBrightnessAsync(
                    redBrightness,
                    greenBrightness,
                    blueBrightness
                );
            }
            catch (Exception ex)
            {
                _logService.LogError($"设置RGB亮度失败: {ex.Message}", "光源控制服务");
                return false;
            }
        }

        /// <summary>
        /// 获取指定控制器的指定通道亮度
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <param name="channel">通道号</param>
        /// <returns>通道亮度值</returns>
        public async Task<int> GetChannelBrightnessAsync(int controllerId, int channel)
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                LightControllerBase controller;
                lock (_lock)
                {
                    if (!_controllers.TryGetValue(controllerId, out controller))
                    {
                        _logService.LogError(
                            $"找不到指定的光源控制器: {controllerId}",
                            "光源控制服务"
                        );
                        return -1;
                    }
                }

                return await controller.GetChannelBrightnessAsync(channel);
            }
            catch (Exception ex)
            {
                _logService.LogError($"获取通道亮度失败: {ex.Message}", "光源控制服务");
                return -1;
            }
        }

        /// <summary>
        /// 关闭指定控制器的所有通道
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> TurnOffAllChannelsAsync(int controllerId)
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                LightControllerBase controller;
                lock (_lock)
                {
                    if (!_controllers.TryGetValue(controllerId, out controller))
                    {
                        _logService.LogError(
                            $"找不到指定的光源控制器: {controllerId}",
                            "光源控制服务"
                        );
                        return false;
                    }
                }

                return await controller.TurnOffAllChannelsAsync();
            }
            catch (Exception ex)
            {
                _logService.LogError($"关闭所有通道失败: {ex.Message}", "光源控制服务");
                return false;
            }
        }

        /// <summary>
        /// 获取所有可用的光源控制器
        /// </summary>
        /// <returns>控制器列表</returns>
        public async Task<IList<LightController>> GetAvailableControllersAsync()
        {
            try
            {
                if (!_isInitialized)
                {
                    await InitializeAsync();
                }

                // 返回所有控制器的配置信息
                List<LightController> result = new List<LightController>();
                lock (_lock)
                {
                    foreach (var controller in _controllers.Values)
                    {
                        result.Add(controller.Config);
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                _logService.LogError($"获取可用控制器失败: {ex.Message}", "光源控制服务");
                return new List<LightController>();
            }
        }

        /// <summary>
        /// 添加新的光源控制器
        /// </summary>
        /// <param name="config">控制器配置</param>
        /// <returns>新控制器的ID</returns>
        public async Task<int> AddControllerAsync(LightController config)
        {
            try
            {
                // 保存控制器配置到数据库
                await SaveControllerConfigAsync(config);

                // 创建控制器实例
                LightControllerBase controller = CreateControllerInstance(config);
                if (controller != null)
                {
                    bool initResult = await controller.InitializeAsync();
                    if (initResult)
                    {
                        lock (_lock)
                        {
                            _controllers[config.ControllerId] = controller;
                        }
                        _logService.LogInformation(
                            $"添加光源控制器成功: {config.Name}",
                            "光源控制服务"
                        );
                        return config.ControllerId;
                    }
                }

                _logService.LogError($"添加光源控制器失败: {config.Name}", "光源控制服务");
                return -1;
            }
            catch (Exception ex)
            {
                _logService.LogError($"添加光源控制器失败: {ex.Message}", "光源控制服务");
                return -1;
            }
        }

        /// <summary>
        /// 移除光源控制器
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> RemoveControllerAsync(int controllerId)
        {
            try
            {
                LightControllerBase controller;
                lock (_lock)
                {
                    if (!_controllers.TryGetValue(controllerId, out controller))
                    {
                        _logService.LogError(
                            $"找不到指定的光源控制器: {controllerId}",
                            "光源控制服务"
                        );
                        return false;
                    }

                    _controllers.Remove(controllerId);
                }

                await controller.ShutdownAsync();
                controller.Dispose();

                // 从数据库中删除控制器配置
                await DeleteControllerConfigAsync(controllerId);

                _logService.LogInformation($"移除光源控制器成功: {controllerId}", "光源控制服务");
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"移除光源控制器失败: {ex.Message}", "光源控制服务");
                return false;
            }
        }

        /// <summary>
        /// 创建控制器实例
        /// </summary>
        /// <param name="config">控制器配置</param>
        /// <returns>控制器实例</returns>
        private LightControllerBase CreateControllerInstance(LightController config)
        {
            try
            {
                switch (config.Brand)
                {
                    case LightControllerBrand.CCS:
                        return new CCSLightController(config);
                    case LightControllerBrand.Hikvision:
                        return new HikvisionLightController(config);
                    case LightControllerBrand.Yixun:
                        return new YixuLightController(config);
                    // 其他品牌的实现
                    // case LightControllerBrand.Daheng:
                    //     return new DahengLightController(config);
                    // case LightControllerBrand.Wantron:
                    //     return new WantronLightController(config);
                    default:
                        _logService.LogWarning(
                            $"不支持的光源控制器品牌: {config.Brand}",
                            "光源控制服务"
                        );
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"创建控制器实例失败: {ex.Message}", "光源控制服务");
                return null;
            }
        }

        /// <summary>
        /// 解析自定义通道设置
        /// </summary>
        /// <param name="settings">通道设置字符串</param>
        /// <returns>通道设置字典</returns>
        private Dictionary<int, int> ParseCustomChannelSettings(string settings)
        {
            Dictionary<int, int> result = new Dictionary<int, int>();
            if (string.IsNullOrEmpty(settings))
            {
                return result;
            }

            try
            {
                // 格式：通道号:亮度值，例如 "1:120,2:200,3:50"
                string[] pairs = settings.Split(',');
                foreach (string pair in pairs)
                {
                    string[] parts = pair.Trim().Split(':');
                    if (
                        parts.Length == 2
                        && int.TryParse(parts[0], out int channel)
                        && int.TryParse(parts[1], out int brightness)
                    )
                    {
                        result[channel] = brightness;
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"解析自定义通道设置失败: {ex.Message}", "光源控制服务");
            }

            return result;
        }

        /// <summary>
        /// 从数据库加载光源控制器配置
        /// </summary>
        /// <returns>控制器配置列表</returns>
        private async Task<List<LightController>> LoadControllerConfigsAsync()
        {
            try
            {
                var configs = JsonConvert.DeserializeObject<List<LightController>>(
                    File.ReadAllText("Config/LightControllerConfig.json")
                );

                return configs;
            }
            catch (Exception ex)
            {
                _logService.LogError($"加载光源控制器配置失败: {ex.Message}", "光源控制服务");
                return new List<LightController>();
            }
        }

        /// <summary>
        /// 保存控制器配置到数据库
        /// </summary>
        /// <param name="config">控制器配置</param>
        private async Task SaveControllerConfigAsync(LightController config)
        {
            // 实际应用中应该保存到数据库
            // 这里只是模拟示例
            await Task.CompletedTask;
        }

        /// <summary>
        /// 从数据库删除控制器配置
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        private async Task DeleteControllerConfigAsync(int controllerId)
        {
            // 实际应用中应该从数据库中删除
            // 这里只是模拟示例
            await Task.CompletedTask;
        }
    }
}
