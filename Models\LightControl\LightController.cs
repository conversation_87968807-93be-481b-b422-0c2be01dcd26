using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Nickel_Inspect.Models.LightControl
{
    /// <summary>
    /// 光源控制器基类
    /// </summary>
    public class LightController
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ControllerId { get; set; }

        /// <summary>
        /// 控制器名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 控制器品牌
        /// </summary>
        public LightControllerBrand Brand { get; set; }

        /// <summary>
        /// 控制器型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 通讯端口（如COM3）
        /// </summary>
        public string Port { get; set; }

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 校验位
        /// </summary>
        public Parity Parity { get; set; } = Parity.None;

        /// <summary>
        /// 停止位
        /// </summary>
        public StopBits StopBits { get; set; } = StopBits.One;

        /// <summary>
        /// 可用通道数量
        /// </summary>
        public int ChannelCount { get; set; }

        /// <summary>
        /// 是否支持RGB控制
        /// </summary>
        public bool SupportsRgb { get; set; }

        /// <summary>
        /// 最大亮度值
        /// </summary>
        public int MaxBrightness { get; set; } = 255;

        /// <summary>
        /// 控制器描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }

    /// <summary>
    /// 光源控制器品牌枚举
    /// </summary>
    public enum LightControllerBrand
    {
        /// <summary>
        /// 未知品牌
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 奥普特CCS
        /// </summary>
        CCS = 1,

        /// <summary>
        /// 海康
        /// </summary>
        Hikvision = 2,

        /// <summary>
        /// 大恒
        /// </summary>
        Daheng = 3,

        /// <summary>
        /// 万霆
        /// </summary>
        Wantron = 4,

        /// <summary>
        /// 轶旭
        /// </summary>
        Yixun = 5,

        /// <summary>
        /// 自定义
        /// </summary>
        Custom = 99,
    }

    /// <summary>
    /// 校验位枚举
    /// </summary>
    public enum Parity
    {
        None = 0,
        Odd = 1,
        Even = 2,
        Mark = 3,
        Space = 4,
    }

    /// <summary>
    /// 停止位枚举
    /// </summary>
    public enum StopBits
    {
        None = 0,
        One = 1,
        Two = 2,
        OnePointFive = 3,
    }
}
