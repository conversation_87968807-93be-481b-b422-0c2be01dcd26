﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using HandyControl.Controls;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.LightControl;
using Nickel_Inspect.Services;
using Prism.Commands;
using Prism.Events;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;

namespace Nickel_Inspect.ViewModels
{
    public class AddInspectPointViewModel : BindableBase, IDialogAware
    {
        private readonly BkMotionCard _modbusService;
        private readonly DatabaseService _databaseService;
        private readonly Services.LightControl.ILightControlService _lightControlService;

        private ProductModel _selectedModel;
        public ProductModel SelectedModel
        {
            get => _selectedModel;
            set => SetProperty(ref _selectedModel, value);
        }

        private string _pointName;
        public string PointName
        {
            get => _pointName;
            set => SetProperty(ref _pointName, value);
        }

        private double _currentX;
        public double CurrentX
        {
            get => _currentX;
            set => SetProperty(ref _currentX, value);
        }

        private double _currentY;
        public double CurrentY
        {
            get => _currentY;
            set => SetProperty(ref _currentY, value);
        }

        private double _currentZ;
        public double CurrentZ
        {
            get => _currentZ;
            set => SetProperty(ref _currentZ, value);
        }

        private string _comment;
        public string Comment
        {
            get => _comment;
            set => SetProperty(ref _comment, value);
        }

        // 光源控制器相关属性
        private ObservableCollection<Models.LightControl.LightController> _lightControllers;
        public ObservableCollection<Models.LightControl.LightController> LightControllers
        {
            get => _lightControllers;
            set => SetProperty(ref _lightControllers, value);
        }

        private Models.LightControl.LightController _selectedLightController;
        public Models.LightControl.LightController SelectedLightController
        {
            get => _selectedLightController;
            set => SetProperty(ref _selectedLightController, value);
        }

        private int _redChannelBrightness;
        public int RedChannelBrightness
        {
            get => _redChannelBrightness;
            set => SetProperty(ref _redChannelBrightness, value);
        }

        private int _greenChannelBrightness;
        public int GreenChannelBrightness
        {
            get => _greenChannelBrightness;
            set => SetProperty(ref _greenChannelBrightness, value);
        }

        private int _blueChannelBrightness;
        public int BlueChannelBrightness
        {
            get => _blueChannelBrightness;
            set => SetProperty(ref _blueChannelBrightness, value);
        }

        private int _whiteChannelBrightness;
        public int WhiteChannelBrightness
        {
            get => _whiteChannelBrightness;
            set => SetProperty(ref _whiteChannelBrightness, value);
        }

        // 命令定义
        public DelegateCommand RefreshPositionCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand CancelCommand { get; }

        // 构造函数
        public AddInspectPointViewModel(
            BkMotionCard modbusService,
            DatabaseService databaseService,
            Services.LightControl.ILightControlService lightControlService
        )
        {
            _databaseService = databaseService;
            _modbusService = modbusService;
            _lightControlService = lightControlService;

            // 初始化命令
            RefreshPositionCommand = new DelegateCommand(ExecuteRefreshPosition);
            SaveCommand = new DelegateCommand(ExecuteSave, CanExecuteSave)
                .ObservesProperty(() => PointName)
                .ObservesProperty(() => SelectedModel);
            CancelCommand = new DelegateCommand(ExecuteCancel);

            // 初始化数据
            InitializeData();
        }

        private async void InitializeData()
        {
            // 读取当前位置
            RefreshPosition();

            // 加载光源控制器
            await LoadLightControllersAsync();
        }

        private void RefreshPosition()
        {
            var axesConfigs = _modbusService.GetAllAxesConfig();
            try
            {
                foreach (var axis in axesConfigs) // 只读取XYZ三轴
                {
                    var position = _modbusService.GetCurrentPosition(axis.AxisId);
                    switch (axis.AxisId)
                    {
                        case 0:
                            CurrentX = position;
                            break;
                        case 1:
                            CurrentY = position;
                            break;
                        case 2:
                            CurrentZ = position;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取位置失败：{ex.Message}");
            }
        }

        private void ExecuteRefreshPosition()
        {
            RefreshPosition();
        }

        private bool CanExecuteSave()
        {
            //return !string.IsNullOrWhiteSpace(PointName) && SelectedModel != null;
            if (string.IsNullOrWhiteSpace(PointName) || SelectedModel == null)
                return false;
            return true;
        }

        private async void ExecuteSave()
        {
            try
            {
                // 创建新的检查点对象
                var newPoint = new InspectionPoint
                {
                    PointName = PointName,
                    PointCode = PointName,
                    SequenceNo = 0,
                    TriggerIOPort = 0,
                    TimeoutSeconds = 0,
                    ModelId = SelectedModel.ModelId,
                    XPosition = (decimal)CurrentX,
                    YPosition = (decimal)CurrentY,
                    ZPosition = (decimal)CurrentZ,
                    IsActive = true,
                    // 添加光源设置
                    LightControllerId = SelectedLightController?.ControllerId ?? 0,
                    RedChannelBrightness = RedChannelBrightness,
                    GreenChannelBrightness = GreenChannelBrightness,
                    BlueChannelBrightness = BlueChannelBrightness,
                    WhiteChannelBrightness = WhiteChannelBrightness,
                };

                await _databaseService.AddInspectionPointAsync(newPoint);

                // 关闭窗口
                RequestClose?.Invoke(new DialogResult(ButtonResult.OK));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}");
            }
        }

        private void ExecuteCancel()
        {
            RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
        }

        // IDialogAware 接口实现
        public string Title => "新增检查点";

        public event Action<IDialogResult> RequestClose;

        public bool CanCloseDialog()
        {
            return true;
        }

        public void OnDialogClosed() { }

        public void OnDialogOpened(IDialogParameters parameters)
        {
            // 如果有传入参数，可以在这里处理
            if (parameters.ContainsKey("ProductModel"))
            {
                SelectedModel = parameters.GetValue<ProductModel>("ProductModel");
            }

            // 获取位置参数
            if (parameters.ContainsKey("XPosition"))
            {
                CurrentX = parameters.GetValue<double>("XPosition");
            }

            if (parameters.ContainsKey("YPosition"))
            {
                CurrentY = parameters.GetValue<double>("YPosition");
            }

            if (parameters.ContainsKey("ZPosition"))
            {
                CurrentZ = parameters.GetValue<double>("ZPosition");
            }

            // 获取光源控制器
            if (parameters.ContainsKey("LightController"))
            {
                SelectedLightController = parameters.GetValue<Models.LightControl.LightController>(
                    "LightController"
                );
            }

            // 获取光源亮度值
            if (parameters.ContainsKey("RedChannelBrightness"))
            {
                RedChannelBrightness = parameters.GetValue<int>("RedChannelBrightness");
            }

            if (parameters.ContainsKey("GreenChannelBrightness"))
            {
                GreenChannelBrightness = parameters.GetValue<int>("GreenChannelBrightness");
            }

            if (parameters.ContainsKey("BlueChannelBrightness"))
            {
                BlueChannelBrightness = parameters.GetValue<int>("BlueChannelBrightness");
            }

            if (parameters.ContainsKey("WhiteChannelBrightness"))
            {
                WhiteChannelBrightness = parameters.GetValue<int>("WhiteChannelBrightness");
            }
        }

        private async Task LoadLightControllersAsync()
        {
            try
            {
                // 初始化光源控制服务
                await _lightControlService.InitializeAsync();

                var lightControllers = await _lightControlService.GetAvailableControllersAsync();
                LightControllers = new ObservableCollection<Models.LightControl.LightController>(
                    lightControllers
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载光源控制器数据时出错: {ex.Message}");
            }
        }
    }
}
