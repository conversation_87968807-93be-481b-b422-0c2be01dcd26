using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using Nickel_Inspect.Services;
using Prism.Commands;
using Prism.Mvvm;

namespace Nickel_Inspect.ViewModels
{
    public class VisionProTestViewModel : BindableBase
    {
        private readonly IVisionProService _visionProService;
        private readonly ILogService _logService;
        private Bitmap _lastCapturedImage;

        private BitmapImage _currentImage;
        public BitmapImage CurrentImage
        {
            get => _currentImage;
            set => SetProperty(ref _currentImage, value);
        }

        private string _statusText = "就绪";
        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        public DelegateCommand LoadVppCommand { get; }
        public DelegateCommand CaptureImageCommand { get; }
        public DelegateCommand SaveImageCommand { get; }

        public VisionProTestViewModel(IVisionProService visionProService, ILogService logService)
        {
            _visionProService = visionProService;
            _logService = logService;

            LoadVppCommand = new DelegateCommand(ExecuteLoadVpp);
            CaptureImageCommand = new DelegateCommand(ExecuteCaptureImage);
            SaveImageCommand = new DelegateCommand(ExecuteSaveImage, CanExecuteSaveImage);

            // 初始化VisionPro
            InitializeVisionPro();
        }

        private void InitializeVisionPro()
        {
            try
            {
                StatusText = "正在初始化VisionPro...";
                if (_visionProService.Initialize())
                {
                    StatusText = "VisionPro初始化成功";

                    // 默认加载VPP文件
                    string defaultVppPath = @"AcqTest.vpp";
                    if (File.Exists(defaultVppPath))
                    {
                        if (_visionProService.LoadToolBlock(defaultVppPath))
                        {
                            StatusText = $"已加载VPP文件: {defaultVppPath}";
                        }
                        else
                        {
                            StatusText = "加载默认VPP文件失败";
                        }
                    }
                }
                else
                {
                    StatusText = "VisionPro初始化失败";
                }
            }
            catch (Exception ex)
            {
                StatusText = $"初始化错误: {ex.Message}";
                _logService.LogError($"VisionPro初始化错误: {ex.Message}", "错误");
            }
        }

        private void ExecuteLoadVpp()
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "VisionPro文件 (*.vpp)|*.vpp",
                    Title = "选择VisionPro工具块文件",
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    string vppFilePath = openFileDialog.FileName;
                    StatusText = $"正在加载VPP文件: {vppFilePath}";

                    if (_visionProService.LoadToolBlock(vppFilePath))
                    {
                        StatusText = $"成功加载VPP文件: {vppFilePath}";
                    }
                    else
                    {
                        StatusText = "加载VPP文件失败";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusText = $"加载VPP文件错误: {ex.Message}";
                _logService.LogError($"加载VPP文件错误: {ex.Message}", "错误");
            }
        }

        private async void ExecuteCaptureImage()
        {
            try
            {
                StatusText = "正在获取图像...";
                var result = await _visionProService.AcquireAndProcessImageAsync();

                if (result.Success && result.ProcessedImage != null)
                {
                    _lastCapturedImage = result.ProcessedImage;
                    CurrentImage = ConvertBitmapToBitmapImage(result.ProcessedImage);
                    StatusText = "图像获取成功";
                    SaveImageCommand.RaiseCanExecuteChanged();
                }
                else
                {
                    StatusText = "图像获取失败";
                }
            }
            catch (Exception ex)
            {
                StatusText = $"获取图像错误: {ex.Message}";
                _logService.LogError($"获取图像错误: {ex.Message}", "错误");
            }
        }

        private void ExecuteSaveImage()
        {
            try
            {
                if (_lastCapturedImage == null)
                {
                    StatusText = "没有可保存的图像";
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "位图文件 (*.bmp)|*.bmp|JPEG文件 (*.jpg)|*.jpg|PNG文件 (*.png)|*.png",
                    Title = "保存图像",
                    FileName = $"Image_{DateTime.Now:yyyyMMddHHmmss}",
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    string imagePath = saveFileDialog.FileName;
                    if (
                        _visionProService.SaveImage(
                            new Cognex.VisionPro.CogImage8Grey(_lastCapturedImage),
                            imagePath
                        )
                    )
                    {
                        StatusText = $"图像已保存至: {imagePath}";
                    }
                    else
                    {
                        StatusText = "保存图像失败";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusText = $"保存图像错误: {ex.Message}";
                _logService.LogError($"保存图像错误: {ex.Message}", "错误");
            }
        }

        private bool CanExecuteSaveImage()
        {
            return _lastCapturedImage != null;
        }

        private BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using (MemoryStream memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Bmp);
                memory.Position = 0;

                BitmapImage bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze();

                return bitmapImage;
            }
        }
    }
}
