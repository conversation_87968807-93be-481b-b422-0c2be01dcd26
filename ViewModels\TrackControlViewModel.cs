using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Threading;
using Nickel_Inspect.Models.Track;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.Track;
using Nickel_Inspect.Views;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;

namespace Nickel_Inspect.ViewModels
{
    public class TrackControlViewModel : BindableBase, IDialogAware, IDisposable
    {
        private readonly ITrackService _trackService;
        private readonly ILogService _logService;
        private readonly ITrackConfigService _configService;
        private readonly DispatcherTimer _updateTimer;
        private TrackStateData _currentState;
        private bool _isRunning;
        private bool _isPaused;
        private bool _isError;
        private string _title = "轨道控制";
        private bool _isSmemaEnabled = false;
        private bool _isDebugMode = false;
        private int _beltSpeed = 40;
        private int _widthAxisSpeed = 20;
        private ObservableCollection<LogMessage> _logMessages;
        private readonly object _logLock = new object();
        private const int MAX_LOG_MESSAGES = 1000;

        public TrackControlViewModel(
            ITrackService trackService,
            ILogService logService,
            ITrackConfigService configService
        )
        {
            _trackService = trackService ?? throw new ArgumentNullException(nameof(trackService));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _configService =
                configService ?? throw new ArgumentNullException(nameof(configService));

            // 创建状态更新定时器，增加更新间隔到500ms
            _updateTimer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(500) };
            _updateTimer.Tick += (s, e) => UpdateState();

            // 初始化命令
            InitializeCommands();

            // 订阅事件
            SubscribeEvents();

            // 初始化状态
            _currentState = new TrackStateData
            {
                CurrentState = "已停止",
                CurrentMode = TrackMode.Normal,
                Direction = TrackDirection.LeftToRight,
                LastUpdateTime = DateTime.Now,
                SmemaEnabled = false,
                IsDebugMode = false,
                BeltSpeed = 40,
                WidthAxisSpeed = 20,
            };

            // 启动定时器
            _updateTimer.Start();

            // 初始化状态
            UpdateState();

            // 确保UI更新
            RaisePropertyChanged(nameof(IsDebugMode));

            // 初始化日志集合
            _logMessages = new ObservableCollection<LogMessage>();
            BindingOperations.EnableCollectionSynchronization(_logMessages, _logLock);

            // 加载配置
            LoadConfigAsync();
        }

        #region Properties

        public TrackStateData CurrentState
        {
            get => _currentState;
            private set
            {
                if (SetProperty(ref _currentState, value))
                {
                    UpdateRunningState();
                    RaisePropertyChanged(nameof(IsNormalMode));
                    RaisePropertyChanged(nameof(IsPassThroughMode));
                    RaisePropertyChanged(nameof(IsManualMode));
                    RaisePropertyChanged(nameof(IsLeftToRight));
                    RaisePropertyChanged(nameof(IsRightToLeft));
                    RaisePropertyChanged(nameof(IsSmemaEnabled));
                    RaisePropertyChanged(nameof(IsDebugMode));
                    RaisePropertyChanged(nameof(BeltSpeed));
                    RaisePropertyChanged(nameof(WidthAxisSpeed));
                }
            }
        }

        public bool IsRunning
        {
            get => _isRunning;
            private set
            {
                if (SetProperty(ref _isRunning, value))
                {
                    RaiseCommandsCanExecuteChanged();
                }
            }
        }

        public bool IsPaused
        {
            get => _isPaused;
            private set
            {
                if (SetProperty(ref _isPaused, value))
                {
                    RaiseCommandsCanExecuteChanged();
                }
            }
        }

        public bool IsError
        {
            get => _isError;
            private set
            {
                if (SetProperty(ref _isError, value))
                {
                    RaiseCommandsCanExecuteChanged();
                }
            }
        }

        public bool IsSmemaEnabled
        {
            get => _isSmemaEnabled;
            set
            {
                if (SetProperty(ref _isSmemaEnabled, value))
                {
                    SetSmemaEnabledCommand.Execute(value);
                }
            }
        }

        public bool IsDebugMode
        {
            get => _isDebugMode;
            set
            {
                if (SetProperty(ref _isDebugMode, value))
                {
                    try
                    {
                        // 直接更新UI状态，不等待异步操作完成
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            RaisePropertyChanged(nameof(IsDebugMode));
                        });

                        // 异步执行实际的模式切换
                        Task.Run(async () =>
                        {
                            try
                            {
                                await _trackService.SetDebugModeAsync(value);
                            }
                            catch (Exception ex)
                            {
                                // 在UI线程上处理错误
                                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                                {
                                    HandleError(ex.Message);
                                    // 如果设置失败，恢复UI状态
                                    _isDebugMode = !value;
                                    RaisePropertyChanged(nameof(IsDebugMode));
                                });
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        HandleError(ex.Message);
                        // 如果设置失败，恢复UI状态
                        _isDebugMode = !value;
                        RaisePropertyChanged(nameof(IsDebugMode));
                    }
                }
            }
        }

        public int BeltSpeed
        {
            get => _beltSpeed;
            set
            {
                if (SetProperty(ref _beltSpeed, value))
                {
                    SetBeltSpeedCommand.Execute(value);
                }
            }
        }

        public int WidthAxisSpeed
        {
            get => _widthAxisSpeed;
            set
            {
                if (SetProperty(ref _widthAxisSpeed, value))
                {
                    SetWidthAxisSpeedCommand.Execute(value);
                }
            }
        }

        #region Mode Properties

        public bool IsNormalMode
        {
            get => CurrentState.CurrentMode == TrackMode.Normal;
            set
            {
                if (value)
                {
                    SetModeCommand.Execute(TrackMode.Normal);
                }
            }
        }

        public bool IsPassThroughMode
        {
            get => CurrentState.CurrentMode == TrackMode.PassThrough;
            set
            {
                if (value)
                {
                    SetModeCommand.Execute(TrackMode.PassThrough);
                }
            }
        }

        public bool IsManualMode
        {
            get => CurrentState.CurrentMode == TrackMode.Manual;
            set
            {
                if (value)
                {
                    SetModeCommand.Execute(TrackMode.Manual);
                }
            }
        }

        #endregion

        #region Direction Properties

        public bool IsLeftToRight
        {
            get => CurrentState.Direction == TrackDirection.LeftToRight;
            set
            {
                if (value)
                {
                    SetDirectionCommand.Execute(TrackDirection.LeftToRight);
                }
            }
        }

        public bool IsRightToLeft
        {
            get => CurrentState.Direction == TrackDirection.RightToLeft;
            set
            {
                if (value)
                {
                    SetDirectionCommand.Execute(TrackDirection.RightToLeft);
                }
            }
        }

        #endregion

        public ObservableCollection<LogMessage> LogMessages
        {
            get => _logMessages;
            private set => SetProperty(ref _logMessages, value);
        }

        #endregion

        #region Commands

        public DelegateCommand StartCommand { get; private set; }
        public DelegateCommand StopCommand { get; private set; }
        public DelegateCommand PauseCommand { get; private set; }
        public DelegateCommand ResumeCommand { get; private set; }
        public DelegateCommand EmergencyStopCommand { get; private set; }
        public DelegateCommand ResetCommand { get; private set; }
        public DelegateCommand<TrackMode?> SetModeCommand { get; private set; }
        public DelegateCommand<TrackDirection?> SetDirectionCommand { get; private set; }
        public DelegateCommand<bool?> SetSmemaEnabledCommand { get; private set; }
        public DelegateCommand<bool?> SetDebugModeCommand { get; private set; }
        public DelegateCommand<int?> SetBeltSpeedCommand { get; private set; }
        public DelegateCommand<int?> SetWidthAxisSpeedCommand { get; private set; }
        public DelegateCommand BeltForwardCommand { get; private set; }
        public DelegateCommand BeltBackwardCommand { get; private set; }
        public DelegateCommand BeltStopCommand { get; private set; }
        public DelegateCommand WidthAxisForwardCommand { get; private set; }
        public DelegateCommand WidthAxisBackwardCommand { get; private set; }
        public DelegateCommand WidthAxisStopCommand { get; private set; }
        public DelegateCommand WidthAxisHomeCommand { get; private set; }
        public DelegateCommand ToggleBoardInSensorCommand { get; private set; }
        public DelegateCommand ToggleBoardArrivedSensorCommand { get; private set; }
        public DelegateCommand ToggleBoardOutSensorCommand { get; private set; }
        public DelegateCommand ToggleStopperCylinderCommand { get; private set; }
        public DelegateCommand ToggleClampCylinderCommand { get; private set; }
        public DelegateCommand ToggleNextMachineReadyCommand { get; private set; }
        public DelegateCommand ToggleHasBoardCommand { get; private set; }
        public DelegateCommand ToggleReadyToReceiveCommand { get; private set; }
        public DelegateCommand SaveConfigCommand { get; private set; }
        public DelegateCommand ManualBoardInCommand { get; private set; }
        public DelegateCommand ManualBoardOutCommand { get; private set; }

        private void InitializeCommands()
        {
            StartCommand = new DelegateCommand(
                async () => await ExecuteStartAsync(),
                CanExecuteStart
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            StopCommand = new DelegateCommand(async () => await ExecuteStopAsync(), CanExecuteStop)
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            PauseCommand = new DelegateCommand(
                async () => await ExecutePauseAsync(),
                CanExecutePause
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsPaused)
                .ObservesProperty(() => IsError);

            ResumeCommand = new DelegateCommand(
                async () => await ExecuteResumeAsync(),
                CanExecuteResume
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsPaused)
                .ObservesProperty(() => IsError);

            EmergencyStopCommand = new DelegateCommand(
                async () => await ExecuteEmergencyStopAsync()
            );

            ResetCommand = new DelegateCommand(
                async () => await ExecuteResetAsync(),
                CanExecuteReset
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            SetModeCommand = new DelegateCommand<TrackMode?>(
                async (mode) => await ExecuteSetModeAsync(mode),
                CanExecuteSetMode
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            SetDirectionCommand = new DelegateCommand<TrackDirection?>(
                async (direction) => await ExecuteSetDirectionAsync(direction),
                CanExecuteSetDirection
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            SetSmemaEnabledCommand = new DelegateCommand<bool?>(
                async (enabled) => await ExecuteSetSmemaEnabledAsync(enabled),
                CanExecuteSetSmemaEnabled
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            SetDebugModeCommand = new DelegateCommand<bool?>(
                async (enabled) => await ExecuteSetDebugModeAsync(enabled),
                CanExecuteSetDebugMode
            )
                .ObservesProperty(() => IsRunning)
                .ObservesProperty(() => IsError);

            SetBeltSpeedCommand = new DelegateCommand<int?>(
                async (speed) => await ExecuteSetBeltSpeedAsync(speed),
                CanExecuteSetBeltSpeed
            ).ObservesProperty(() => IsDebugMode);

            SetWidthAxisSpeedCommand = new DelegateCommand<int?>(
                async (speed) => await ExecuteSetWidthAxisSpeedAsync(speed),
                CanExecuteSetWidthAxisSpeed
            ).ObservesProperty(() => IsDebugMode);

            BeltForwardCommand = new DelegateCommand(
                async () => await ExecuteBeltForwardAsync(),
                CanExecuteBeltControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            BeltBackwardCommand = new DelegateCommand(
                async () => await ExecuteBeltBackwardAsync(),
                CanExecuteBeltControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            BeltStopCommand = new DelegateCommand(
                async () => await ExecuteBeltStopAsync(),
                CanExecuteBeltControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            WidthAxisForwardCommand = new DelegateCommand(
                async () => await ExecuteWidthAxisForwardAsync(),
                CanExecuteWidthAxisControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            WidthAxisBackwardCommand = new DelegateCommand(
                async () => await ExecuteWidthAxisBackwardAsync(),
                CanExecuteWidthAxisControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            WidthAxisStopCommand = new DelegateCommand(
                async () => await ExecuteWidthAxisStopAsync(),
                CanExecuteWidthAxisControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            WidthAxisHomeCommand = new DelegateCommand(
                async () => await ExecuteWidthAxisHomeAsync(),
                CanExecuteWidthAxisControl
            )
                .ObservesProperty(() => IsDebugMode)
                .ObservesProperty(() => IsError);

            // 初始化调试模式下的状态切换命令
            ToggleBoardInSensorCommand = new DelegateCommand(
                async () => await ExecuteToggleBoardInSensorAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleBoardArrivedSensorCommand = new DelegateCommand(
                async () => await ExecuteToggleBoardArrivedSensorAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleBoardOutSensorCommand = new DelegateCommand(
                async () => await ExecuteToggleBoardOutSensorAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleStopperCylinderCommand = new DelegateCommand(
                async () => await ExecuteToggleStopperCylinderAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleClampCylinderCommand = new DelegateCommand(
                async () => await ExecuteToggleClampCylinderAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleNextMachineReadyCommand = new DelegateCommand(
                async () => await ExecuteToggleNextMachineReadyAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleHasBoardCommand = new DelegateCommand(
                async () => await ExecuteToggleHasBoardAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);
            ToggleReadyToReceiveCommand = new DelegateCommand(
                async () => await ExecuteToggleReadyToReceiveAsync(),
                () => IsDebugMode
            ).ObservesProperty(() => IsDebugMode);

            // 添加保存配置命令
            SaveConfigCommand = new DelegateCommand(async () => await ExecuteSaveConfigAsync());

            // 添加手动进板和手动出板的命令
            ManualBoardInCommand = new DelegateCommand(
                async () => await ExecuteManualBoardInAsync(),
                CanExecuteManualBoardIn
            ).ObservesProperty(() => IsDebugMode);
            ManualBoardOutCommand = new DelegateCommand(
                async () => await ExecuteManualBoardOutAsync(),
                CanExecuteManualBoardOut
            ).ObservesProperty(() => IsDebugMode);
        }

        private bool CanExecuteStart() => !IsRunning && !IsError;

        private bool CanExecuteStop() => IsRunning && !IsError;

        private bool CanExecutePause() => IsRunning && !IsPaused && !IsError;

        private bool CanExecuteResume() => IsRunning && IsPaused && !IsError;

        private bool CanExecuteReset() => IsError || !IsRunning;

        private bool CanExecuteSetMode(TrackMode? mode) => !IsRunning && !IsError && mode.HasValue;

        private bool CanExecuteSetDirection(TrackDirection? direction) =>
            !IsRunning && !IsError && direction.HasValue;

        private bool CanExecuteSetSmemaEnabled(bool? enabled) => enabled.HasValue;

        private bool CanExecuteSetDebugMode(bool? enabled) =>
            !IsRunning && !IsError && enabled.HasValue;

        private bool CanExecuteSetBeltSpeed(int? speed) =>
            IsDebugMode && speed.HasValue && speed.Value >= 0;

        private bool CanExecuteSetWidthAxisSpeed(int? speed) =>
            IsDebugMode && speed.HasValue && speed.Value >= 0;

        private bool CanExecuteBeltControl() => IsDebugMode && !IsError;

        private bool CanExecuteWidthAxisControl() => IsDebugMode && !IsError;

        private bool CanExecuteManualBoardIn() => IsDebugMode && !IsError;

        private bool CanExecuteManualBoardOut() => IsDebugMode && !IsError;

        private async Task ExecuteStartAsync()
        {
            try
            {
                await _trackService.StartAsync();
                CurrentState.CurrentState = "运行中";
                UpdateRunningState();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteStopAsync()
        {
            try
            {
                await _trackService.StopAsync();
                CurrentState.CurrentState = "已停止";
                UpdateRunningState();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecutePauseAsync()
        {
            try
            {
                await _trackService.PauseAsync();
                CurrentState.CurrentState = "已暂停";
                UpdateRunningState();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteResumeAsync()
        {
            try
            {
                await _trackService.ResumeAsync();
                CurrentState.CurrentState = "运行中";
                UpdateRunningState();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteEmergencyStopAsync()
        {
            try
            {
                await _trackService.EmergencyStopAsync();
                CurrentState.CurrentState = "紧急停止";
                UpdateRunningState();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteResetAsync()
        {
            try
            {
                await _trackService.ResetAsync();
                CurrentState.CurrentState = "已复位";
                UpdateRunningState();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteSetModeAsync(TrackMode? mode)
        {
            if (!mode.HasValue)
                return;

            try
            {
                await _trackService.SetModeAsync(mode.Value);
                CurrentState.CurrentMode = mode.Value;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteSetDirectionAsync(TrackDirection? direction)
        {
            if (!direction.HasValue)
                return;

            try
            {
                await _trackService.SetDirectionAsync(direction.Value);
                CurrentState.Direction = direction.Value;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteSetSmemaEnabledAsync(bool? enabled)
        {
            if (!enabled.HasValue)
                return;

            try
            {
                await _trackService.SetSmemaEnabledAsync(enabled.Value);
                CurrentState.SmemaEnabled = enabled.Value;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteSetDebugModeAsync(bool? enabled)
        {
            if (!enabled.HasValue)
                return;

            try
            {
                await _trackService.SetDebugModeAsync(enabled.Value);
                CurrentState.IsDebugMode = enabled.Value;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteSetBeltSpeedAsync(int? speed)
        {
            if (!speed.HasValue || speed.Value < 0)
                return;

            try
            {
                await _trackService.SetBeltSpeedAsync(speed.Value);
                CurrentState.BeltSpeed = speed.Value;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteSetWidthAxisSpeedAsync(int? speed)
        {
            if (!speed.HasValue || speed.Value < 0)
                return;

            try
            {
                await _trackService.SetWidthAxisSpeedAsync(speed.Value);
                CurrentState.WidthAxisSpeed = speed.Value;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteBeltForwardAsync()
        {
            try
            {
                await _trackService.ManualControlBeltAsync(1, CurrentState.BeltSpeed);
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteBeltBackwardAsync()
        {
            try
            {
                await _trackService.ManualControlBeltAsync(-1, CurrentState.BeltSpeed);
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteBeltStopAsync()
        {
            try
            {
                await _trackService.ManualControlBeltAsync(0, 0);
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteWidthAxisForwardAsync()
        {
            try
            {
                await _trackService.ManualControlWidthAxisAsync(1, CurrentState.WidthAxisSpeed);
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteWidthAxisBackwardAsync()
        {
            try
            {
                await _trackService.ManualControlWidthAxisAsync(-1, CurrentState.WidthAxisSpeed);
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteWidthAxisStopAsync()
        {
            try
            {
                await _trackService.ManualControlWidthAxisAsync(0, 0);
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteWidthAxisHomeAsync()
        {
            try
            {
                await _trackService.HomeWidthAxisAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleBoardInSensorAsync()
        {
            try
            {
                await _trackService.SetBoardInSensorAsync(!CurrentState.BoardInSensor);
                CurrentState.BoardInSensor = !CurrentState.BoardInSensor;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleBoardArrivedSensorAsync()
        {
            try
            {
                await _trackService.SetBoardArrivedSensorAsync(!CurrentState.BoardArrivedSensor);
                CurrentState.BoardArrivedSensor = !CurrentState.BoardArrivedSensor;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleBoardOutSensorAsync()
        {
            try
            {
                await _trackService.SetBoardOutSensorAsync(!CurrentState.BoardOutSensor);
                CurrentState.BoardOutSensor = !CurrentState.BoardOutSensor;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleStopperCylinderAsync()
        {
            try
            {
                await _trackService.SetStopperCylinderAsync(!CurrentState.StopperCylinder);
                //CurrentState.StopperCylinder = !CurrentState.StopperCylinder;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleClampCylinderAsync()
        {
            try
            {
                await _trackService.SetClampCylinderAsync(!CurrentState.ClampCylinder);
                //CurrentState.ClampCylinder = !CurrentState.ClampCylinder;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleNextMachineReadyAsync()
        {
            try
            {
                await _trackService.SetNextMachineReadyAsync(!CurrentState.NextMachineReady);
                CurrentState.NextMachineReady = !CurrentState.NextMachineReady;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleHasBoardAsync()
        {
            try
            {
                await _trackService.SetHasBoardAsync(!CurrentState.HasBoard);
                CurrentState.HasBoard = !CurrentState.HasBoard;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteToggleReadyToReceiveAsync()
        {
            try
            {
                await _trackService.SetReadyToReceiveAsync(!CurrentState.ReadyToReceive);
                CurrentState.ReadyToReceive = !CurrentState.ReadyToReceive;
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        /// <summary>
        /// 执行保存配置命令
        /// </summary>
        private async Task ExecuteSaveConfigAsync()
        {
            try
            {
                bool result = await SaveConfigAsync();
                if (result)
                {
                    MessageBox.Show(
                        "轨道控制配置已成功保存。",
                        "保存成功",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );
                }
                else
                {
                    MessageBox.Show(
                        "保存轨道控制配置失败，请检查日志获取详细信息。",
                        "保存失败",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"保存配置时发生错误: {ex.Message}", "轨道控制");
                MessageBox.Show(
                    $"保存配置时发生错误: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async Task ExecuteManualBoardInAsync()
        {
            try
            {
                await _trackService.ManualBoardInAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private async Task ExecuteManualBoardOutAsync()
        {
            try
            {
                await _trackService.ManualBoardOutAsync();
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private void SubscribeEvents()
        {
            _trackService.StateChanged += OnTrackStateChanged;
            _trackService.ErrorOccurred += OnTrackError;
            _trackService.WarningOccurred += OnTrackWarning;
            _trackService.MessageLogged += OnTrackMessage;
        }

        private void OnTrackStateChanged(object sender, TrackStateData e)
        {
            CurrentState = e;
        }

        private void OnTrackError(object sender, string message)
        {
            HandleError(message);
            AddLogMessage("错误", message);
        }

        private void OnTrackWarning(object sender, string message)
        {
            AddLogMessage("警告", message);
        }

        private void OnTrackMessage(object sender, string message)
        {
            AddLogMessage("信息", message);
        }

        private void AddLogMessage(string type, string message)
        {
            // 添加到日志集合，用于导出
            lock (_logLock)
            {
                // 添加新日志
                _logMessages.Add(
                    new LogMessage
                    {
                        Timestamp = DateTime.Now,
                        Type = type,
                        Message = $"[轨道控制] {message}",
                    }
                );

                // 如果超过最大数量，移除最旧的日志
                while (_logMessages.Count > MAX_LOG_MESSAGES)
                {
                    _logMessages.RemoveAt(0);
                }
            }

            // 保留原有的UI日志显示功能
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    // 获取所有窗口，查找对话框窗口
                    foreach (Window window in Application.Current.Windows)
                    {
                        // 查找窗口中的TrackControlView
                        var trackControlViews = FindVisualChildren<TrackControlView>(window);
                        var trackControlView = trackControlViews.FirstOrDefault();

                        if (trackControlView != null)
                        {
                            // 添加日志到RichTextBox
                            trackControlView.AppendLogMessage(
                                DateTime.Now,
                                type,
                                $"[轨道控制] {message}"
                            );
                            return; // 找到并添加日志后退出
                        }

                        // 如果找不到TrackControlView，尝试通过窗口内容查找
                        if (window.Content is TrackControlView directView)
                        {
                            directView.AppendLogMessage(
                                DateTime.Now,
                                type,
                                $"[轨道控制] {message}"
                            );
                            return; // 找到并添加日志后退出
                        }

                        // 尝试查找ContentControl中的内容
                        if (
                            window.Content is ContentControl contentControl
                            && contentControl.Content is TrackControlView contentView
                        )
                        {
                            contentView.AppendLogMessage(
                                DateTime.Now,
                                type,
                                $"[轨道控制] {message}"
                            );
                            return; // 找到并添加日志后退出
                        }
                    }

                    // 如果没有找到TrackControlView，记录到调试输出
                    System.Diagnostics.Debug.WriteLine(
                        $"未找到TrackControlView控件，无法显示日志: [{type}] {message}"
                    );
                }
                catch (Exception ex)
                {
                    // 记录错误，但不抛出异常
                    System.Diagnostics.Debug.WriteLine($"添加日志时出错: {ex.Message}");
                }
            });
        }

        // 辅助方法：查找指定类型的子控件
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj)
            where T : DependencyObject
        {
            if (depObj == null)
                yield break;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = VisualTreeHelper.GetChild(depObj, i);

                if (child is T t)
                    yield return t;

                foreach (var childOfChild in FindVisualChildren<T>(child))
                    yield return childOfChild;
            }
        }

        private void HandleError(string message)
        {
            CurrentState.ErrorMessage = message;
            CurrentState.CurrentState = "错误";
            IsError = true;

            // 记录错误到日志文件
            _logService.LogError(message, "轨道控制");
        }

        private void UpdateState()
        {
            try
            {
                // 只在运行状态或调试模式下频繁更新状态
                if (!IsRunning && !IsDebugMode)
                {
                    return;
                }

                var state = _trackService.CurrentState;
                if (state != null)
                {
                    // 保存当前的调试模式状态
                    bool wasDebugMode = _isDebugMode;

                    // 更新状态
                    CurrentState = state;

                    // 如果调试模式状态发生变化，确保UI更新
                    if (wasDebugMode != _isDebugMode)
                    {
                        RaisePropertyChanged(nameof(IsDebugMode));
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex.Message);
            }
        }

        private void UpdateRunningState()
        {
            IsRunning = CurrentState.CurrentState == "运行中";
            IsPaused = CurrentState.CurrentState == "已暂停";
            IsError =
                CurrentState.CurrentState == "错误" || CurrentState.CurrentState == "紧急停止";

            // 更新SMEMA状态
            if (_isSmemaEnabled != CurrentState.SmemaEnabled)
            {
                _isSmemaEnabled = CurrentState.SmemaEnabled;
                RaisePropertyChanged(nameof(IsSmemaEnabled));
            }

            // 更新调试模式状态
            if (_isDebugMode != CurrentState.IsDebugMode)
            {
                _isDebugMode = CurrentState.IsDebugMode;
                RaisePropertyChanged(nameof(IsDebugMode));
            }

            // 更新速度设置
            BeltSpeed = CurrentState.BeltSpeed;
            WidthAxisSpeed = CurrentState.WidthAxisSpeed;
        }

        private void RaiseCommandsCanExecuteChanged()
        {
            StartCommand.RaiseCanExecuteChanged();
            StopCommand.RaiseCanExecuteChanged();
            PauseCommand.RaiseCanExecuteChanged();
            ResumeCommand.RaiseCanExecuteChanged();
            ResetCommand.RaiseCanExecuteChanged();
            SetModeCommand.RaiseCanExecuteChanged();
            SetDirectionCommand.RaiseCanExecuteChanged();
            SetSmemaEnabledCommand.RaiseCanExecuteChanged();
            SetDebugModeCommand.RaiseCanExecuteChanged();
            SetBeltSpeedCommand.RaiseCanExecuteChanged();
            SetWidthAxisSpeedCommand.RaiseCanExecuteChanged();
            BeltForwardCommand.RaiseCanExecuteChanged();
            BeltBackwardCommand.RaiseCanExecuteChanged();
            BeltStopCommand.RaiseCanExecuteChanged();
            WidthAxisForwardCommand.RaiseCanExecuteChanged();
            WidthAxisBackwardCommand.RaiseCanExecuteChanged();
            WidthAxisStopCommand.RaiseCanExecuteChanged();
            WidthAxisHomeCommand.RaiseCanExecuteChanged();
            ToggleBoardInSensorCommand.RaiseCanExecuteChanged();
            ToggleBoardArrivedSensorCommand.RaiseCanExecuteChanged();
            ToggleBoardOutSensorCommand.RaiseCanExecuteChanged();
            ToggleStopperCylinderCommand.RaiseCanExecuteChanged();
            ToggleClampCylinderCommand.RaiseCanExecuteChanged();
            ToggleNextMachineReadyCommand.RaiseCanExecuteChanged();
            ToggleHasBoardCommand.RaiseCanExecuteChanged();
            ToggleReadyToReceiveCommand.RaiseCanExecuteChanged();
            SaveConfigCommand.RaiseCanExecuteChanged();
            ManualBoardInCommand.RaiseCanExecuteChanged();
            ManualBoardOutCommand.RaiseCanExecuteChanged();
        }

        #endregion

        #region IDialogAware

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public event Action<IDialogResult> RequestClose;

        public bool CanCloseDialog()
        {
            // 异步方法中不能直接返回结果，所以先返回true
            // 实际询问保存在OnDialogClosed中处理
            return true;
        }

        public void OnDialogClosed()
        {
            // 检查配置是否有变更，如果有则提示用户是否保存
            CheckAndPromptSaveConfigAsync()
                .ContinueWith(t =>
                {
                    // 在对话框关闭时，将自身作为参数传递给调用者
                    var parameters = new DialogParameters();
                    parameters.Add("TrackViewModel", this);
                    RequestClose?.Invoke(new DialogResult(ButtonResult.OK, parameters));

                    Dispose();
                });
        }

        /// <summary>
        /// 检查配置是否有变更，如果有则提示用户是否保存
        /// </summary>
        private async Task CheckAndPromptSaveConfigAsync()
        {
            try
            {
                // 检查配置是否有变更
                bool hasChanged = await CheckConfigChangedAsync();
                if (!hasChanged)
                {
                    return; // 没有变更，不需要保存
                }

                // 在UI线程上弹出对话框
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    var result = MessageBox.Show(
                        "轨道控制参数已修改，是否保存这些更改？",
                        "保存配置",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (result == MessageBoxResult.Yes)
                    {
                        await SaveConfigAsync();
                    }
                });
            }
            catch (Exception ex)
            {
                _logService.LogError($"检查并提示保存配置时发生错误: {ex.Message}", "轨道控制");
            }
        }

        public void OnDialogOpened(IDialogParameters parameters)
        {
            // 处理对话框打开事件
        }

        #endregion

        #region Configuration

        /// <summary>
        /// 加载配置
        /// </summary>
        private async void LoadConfigAsync()
        {
            try
            {
                // 应用配置到当前状态
                var result = await _configService.LoadAndApplyConfigAsync(_currentState);
                if (result)
                {
                    UpdateRunningState();
                    _logService.LogInformation("已加载轨道控制配置", "轨道控制");
                }
                else
                {
                    _logService.LogWarning("加载轨道控制配置失败，使用默认值", "轨道控制");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"加载配置时发生错误: {ex.Message}", "轨道控制");
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private async Task<bool> SaveConfigAsync()
        {
            try
            {
                var result = await _configService.SaveConfigFromStateAsync(_currentState);
                if (result)
                {
                    _logService.LogInformation("已保存轨道控制配置", "轨道控制");
                    return true;
                }
                else
                {
                    _logService.LogWarning("保存轨道控制配置失败", "轨道控制");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"保存配置时发生错误: {ex.Message}", "轨道控制");
                return false;
            }
        }

        /// <summary>
        /// 检查配置是否有变更
        /// </summary>
        private async Task<bool> CheckConfigChangedAsync()
        {
            try
            {
                return await _configService.HasConfigChangedAsync(_currentState);
            }
            catch (Exception ex)
            {
                _logService.LogError($"检查配置变更时发生错误: {ex.Message}", "轨道控制");
                return false;
            }
        }

        #endregion

        public void Dispose()
        {
            _updateTimer?.Stop();
            _trackService.StateChanged -= OnTrackStateChanged;
            _trackService.ErrorOccurred -= OnTrackError;
            _trackService.WarningOccurred -= OnTrackWarning;
            _trackService.MessageLogged -= OnTrackMessage;
        }
    }

    public class LogMessage
    {
        public DateTime Timestamp { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
    }
}
