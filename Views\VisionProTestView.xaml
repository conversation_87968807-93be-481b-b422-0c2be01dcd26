<UserControl
    x:Class="Nickel_Inspect.Views.VisionProTestView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    d:DesignHeight="450"
    d:DesignWidth="800"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Margin="10">
            <TextBlock
                Margin="0,0,0,10"
                FontSize="18"
                Text="VisionPro测试" />
            <StackPanel Orientation="Horizontal">
                <Button
                    Margin="0,0,10,0"
                    Command="{Binding LoadVppCommand}"
                    Content="加载VPP文件" />
                <Button
                    Margin="0,0,10,0"
                    Command="{Binding CaptureImageCommand}"
                    Content="采集图像" />
                <Button Command="{Binding SaveImageCommand}" Content="保存图像" />
            </StackPanel>
        </StackPanel>

        <Border
            Grid.Row="1"
            Margin="10"
            BorderBrush="{DynamicResource BorderBrush}"
            BorderThickness="1">
            <Image Source="{Binding CurrentImage}" Stretch="Uniform" />
        </Border>

        <StackPanel Grid.Row="2" Margin="10">
            <TextBlock Text="{Binding StatusText}" />
        </StackPanel>
    </Grid>
</UserControl> 