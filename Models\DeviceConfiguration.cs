using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Nickel_Inspect.Models;

namespace Nickel_Inspect.Models
{
    public class RootConfig
    {
        public DeviceConfiguration DeviceConfig { get; set; }
        public List<CameraConfig> Cameras { get; set; }
        public SensorConfig Sensors { get; set; }
        public SmemaConfig Smema { get; set; }
        public IndicatorConfig Indicators { get; set; }
        public AlarmConfig Alarms { get; set; }

        // 是否启用离线模拟模式
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        [DefaultValue(false)]
        public bool EnableOfflineSimulation { get; set; } = false;

        // 离线模拟到位等待时间（毫秒）
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        [DefaultValue(2000)]
        public int OfflineSimulationDelayMs { get; set; } = 2000;
    }

    public class DeviceConfiguration
    {
        public string DeviceName { get; set; }
        public string DeviceModel { get; set; }
        public List<AxisConfig> Axes { get; set; }
        public ConveyorConfig Conveyor { get; set; }

        // 辅助方法：根据轴号获取轴配置
        public AxisConfig GetAxisByNo(int axisNo)
        {
            return Axes?.FirstOrDefault(axis => axis.AxisNo == axisNo);
        }

        // 辅助方法：根据轴名称获取轴配置
        public AxisConfig GetAxisByName(string name)
        {
            return Axes?.FirstOrDefault(axis => axis.Name == name);
        }
    }

    public class AxisConfig
    {
        public int AxisNo { get; set; }
        public string Name { get; set; }

        /// <summary>
        /// 脉冲当量
        /// </summary>
        public double PulseEquivalent { get; set; } = 0.04;
        public double StartSpeed { get; set; }
        public double RunSpeed { get; set; }

        /// <summary>
        /// 回零速度, 默认100mm/s
        /// </summary>
        public double HomeSpeed { get; set; } = 100;
        public double JogMaxSpeed { get; set; }

        /// <summary>
        /// 最大运动范围 单位mm
        /// </summary>
        public double MaxDistance { get; set; }

        // <summary>
        /// 运动加速时间 单位ms
        /// </summary>
        public double AccelerationTime { get; set; }
        public double HomeMaxDistance { get; set; }
        public int HomeDirection { get; set; }

        // BkMottionCard.cs中需要的属性
        [JsonProperty("axisId")]
        public int AxisId
        {
            get { return AxisNo; }
            set { AxisNo = value; }
        }
        public int TargetPositionRegister { get; set; }
        public int CurrentPositionRegister { get; set; }
        public int MaxSpeedRegister { get; set; }
        public int MaxRangeRegister { get; set; }
        public int HomeRangeRegister { get; set; }
        public int StartSpeedRegister { get; set; }
        public int AccelerationTimeRegister { get; set; }
        public CommandsConfig CommandsRegister { get; set; }
        public ExceptionConfig ExceptionRegister { get; set; }
    }

    public class CommandsConfig
    {
        public int Addr { get; set; }
        public List<BitConfig> Bits { get; set; }
    }

    public class ExceptionConfig
    {
        public int Addr { get; set; }
        public List<ExceptionBitConfig> Bits { get; set; }
    }

    public class BitConfig
    {
        public string Name { get; set; }
        public int BitIndex { get; set; }
    }

    public class ExceptionBitConfig
    {
        public string Name { get; set; }
        public int BitIndex { get; set; }
        public string Comment { get; set; }
    }

    public class CameraConfig
    {
        public string CameraName { get; set; }
        public int CameraIndex { get; set; }
        public CameraIoConfig TriggerIo { get; set; }
        public CameraIoConfig ReadyIo { get; set; }
    }

    public class CameraIoConfig
    {
        public int IoAddress { get; set; }
        public int IoBitIndex { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public int PulseWidth { get; set; }
        public string Description { get; set; }
    }

    public class PositionSensors
    {
        public IoConfig XHomeSwitch { get; set; }
        public IoConfig YHomeSwitch { get; set; }
        public IoConfig ZHomeSwitch { get; set; }
        public IoConfig R1HomeSwitch { get; set; }
        public IoConfig R2HomeSwitch { get; set; }
    }

    public class ControlSensors
    {
        public IoConfig StartButton { get; set; }
        public IoConfig ResetButton { get; set; }
    }

    public class SensorConfig
    {
        public SafetySensors Safety { get; set; }
        public PositionSensors Position { get; set; }
        public ControlSensors Controls { get; set; }
    }

    public class SafetySensors
    {
        public IoConfig EmergencyStop { get; set; }
        public IoConfig DoorSensor { get; set; }
    }

    public class SmemaConfig
    {
        public SmemaInterface Upstream { get; set; }
        public SmemaInterface Downstream { get; set; }
    }

    public class SmemaInterface
    {
        public SmemaIoConfig BoardAvailable { get; set; }
        public SmemaIoConfig MachineReady { get; set; }
    }

    public class SmemaIoConfig : IoConfig
    {
        // SMEMA特有的配置可以在这里添加
    }

    // 指示灯配置
    public class IndicatorConfig
    {
        public LightConfig Lights { get; set; }
        public IoConfig Buzzer { get; set; }
    }

    public class LightConfig
    {
        public IoConfig RedLight { get; set; }
        public IoConfig GreenLight { get; set; }
        public IoConfig YellowLight { get; set; }
    }

    // 报警配置类
    public class AlarmConfig
    {
        public Dictionary<string, IoConfig> ServoAlarms { get; set; }
        public Dictionary<string, IoConfig> SystemAlarms { get; set; }
    }

    public class ConveyorConfig
    {
        public ConveyorSensors Sensors { get; set; }
        public ConveyorActuators Actuators { get; set; }
    }

    public class ConveyorSensors
    {
        public IoConfig IncomingBoard { get; set; }
        public IoConfig BoardInPosition { get; set; }
        public IoConfig OutgoingBoard { get; set; }
    }

    public class ConveyorActuators
    {
        public IoConfig StopperCylinder { get; set; }
        public IoConfig ClampCylinder { get; set; }
    }

    // 配置帮助类
    public class MachineConfigurationHelper
    {
        private readonly RootConfig _config;
        private static MachineConfigurationHelper _instance;
        private static readonly object _lock = new object();

        #region 单例模式实现
        public static MachineConfigurationHelper Instance
        {
            get
            {
                if (_instance == null)
                {
                    throw new InvalidOperationException(
                        "Configuration not initialized. Call Initialize first."
                    );
                }
                return _instance;
            }
        }

        public static void Initialize(string configPath)
        {
            lock (_lock)
            {
                if (_instance == null)
                {
                    _instance = new MachineConfigurationHelper(configPath);
                }
            }
        }

        // 添加无参构造函数，自动初始化配置
        public MachineConfigurationHelper()
        {
            try
            {
                string jsonFilePath = "DeviceConfiguration.json";
                if (!File.Exists(jsonFilePath))
                {
                    throw new FileNotFoundException(
                        $"Configuration file not found: {jsonFilePath}"
                    );
                }

                string jsonString = File.ReadAllText(jsonFilePath);
                _config = JsonConvert.DeserializeObject<RootConfig>(jsonString);

                // 设置实例
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = this;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading configuration: {ex.Message}", ex);
            }
        }

        private MachineConfigurationHelper(string jsonFilePath)
        {
            if (!File.Exists(jsonFilePath))
            {
                throw new FileNotFoundException($"Configuration file not found: {jsonFilePath}");
            }

            try
            {
                string jsonString = File.ReadAllText(jsonFilePath);
                _config = JsonConvert.DeserializeObject<RootConfig>(jsonString);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading configuration: {ex.Message}", ex);
            }
        }
        #endregion

        #region 配置访问方法
        public DeviceConfiguration GetDeviceConfig()
        {
            return _config?.DeviceConfig;
        }

        public AxisConfig GetAxisConfig(int axisNo)
        {
            return _config?.DeviceConfig?.GetAxisByNo(axisNo);
        }

        public AxisConfig GetAxisConfig(string axisName)
        {
            return _config?.DeviceConfig?.GetAxisByName(axisName);
        }

        public List<AxisConfig> GetAllAxes()
        {
            return _config?.DeviceConfig?.Axes ?? new List<AxisConfig>();
        }

        public CameraConfig GetCameraByIndex(int index)
        {
            return _config?.Cameras?.FirstOrDefault(c => c.CameraIndex == index);
        }

        public CameraConfig GetCameraByName(string cameraName)
        {
            return _config?.Cameras?.FirstOrDefault(c =>
                string.Equals(c.CameraName, cameraName, StringComparison.OrdinalIgnoreCase)
            );
        }

        public List<CameraConfig> GetAllCameras()
        {
            return _config?.Cameras ?? new List<CameraConfig>();
        }

        public IoConfig GetSensorConfig(string sensorName)
        {
            // ... 传感器配置获取逻辑 ...
            return null;
        }

        public SmemaConfig GetSmemaConfig()
        {
            return _config?.Smema;
        }

        /// <summary>
        /// 获取控制按钮配置
        /// </summary>
        /// <param name="buttonName">按钮名称，如 "StartButton" 或 "ResetButton"</param>
        /// <returns>按钮配置</returns>
        public IoConfig GetControlButton(string buttonName)
        {
            if (string.IsNullOrEmpty(buttonName))
                return null;

            // 忽略大小写比较
            if (string.Equals(buttonName, "StartButton", StringComparison.OrdinalIgnoreCase))
                return _config?.Sensors?.Controls?.StartButton;
            else if (string.Equals(buttonName, "ResetButton", StringComparison.OrdinalIgnoreCase))
                return _config?.Sensors?.Controls?.ResetButton;

            return null;
        }

        /// <summary>
        /// 获取所有控制按钮配置
        /// </summary>
        /// <returns>控制按钮配置</returns>
        public ControlSensors GetControlButtons()
        {
            return _config?.Sensors?.Controls;
        }

        /// <summary>
        /// 获取指示灯和蜂鸣器配置
        /// </summary>
        /// <returns>指示灯和蜂鸣器配置</returns>
        public IndicatorConfig GetIndicatorConfig()
        {
            return _config?.Indicators;
        }

        /// <summary>
        /// 获取是否启用离线模拟
        /// </summary>
        /// <returns>是否启用离线模拟</returns>
        public bool IsOfflineSimulationEnabled()
        {
            return _config?.EnableOfflineSimulation ?? false;
        }

        /// <summary>
        /// 获取离线模拟延时时间（毫秒）
        /// </summary>
        /// <returns>延时时间（毫秒）</returns>
        public int GetOfflineSimulationDelayMs()
        {
            return _config?.OfflineSimulationDelayMs ?? 2000;
        }

        /// <summary>
        /// 设置离线模拟模式
        /// </summary>
        /// <param name="enabled">是否启用</param>
        /// <param name="delayMs">可选：延时时间（毫秒）</param>
        public void SetOfflineSimulation(bool enabled, int? delayMs = null)
        {
            if (_config != null)
            {
                _config.EnableOfflineSimulation = enabled;

                if (delayMs.HasValue && delayMs.Value > 0)
                {
                    _config.OfflineSimulationDelayMs = delayMs.Value;
                }
            }
        }

        /// <summary>
        /// 获取完整配置对象的JObject表示
        /// </summary>
        /// <returns>配置的JObject表示</returns>
        public JObject GetConfigAsJObject()
        {
            string json = JsonConvert.SerializeObject(_config);
            return JObject.Parse(json);
        }
        #endregion

        #region 配置保存方法
        public void SaveConfiguration(string filePath)
        {
            var jsonString = JsonConvert.SerializeObject(_config, Formatting.Indented);
            File.WriteAllText(filePath, jsonString);
        }
        #endregion
    }
}
