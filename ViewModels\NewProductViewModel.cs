using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;

namespace Nickel_Inspect.ViewModels
{
    public class NewProductViewModel : BindableBase, IDialogAware
    {
        private readonly DatabaseService _databaseService;
        
        #region 属性

        private string _modelName;
        public string ModelName
        {
            get => _modelName;
            set => SetProperty(ref _modelName, value);
        }

        private string _modelCode;
        public string ModelCode
        {
            get => _modelCode;
            set => SetProperty(ref _modelCode, value);
        }

        private string _description;
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        private string _title = "添加新机种";
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        #endregion

        #region 命令

        public DelegateCommand SaveCommand { get; private set; }
        public DelegateCommand CancelCommand { get; private set; }

        #endregion

        public event Action<IDialogResult> RequestClose;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="databaseService">数据库服务</param>
        public NewProductViewModel(DatabaseService databaseService)
        {
            _databaseService =
                databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            // 初始化命令
            SaveCommand = new DelegateCommand(ExecuteSaveCommand, CanExecuteSaveCommand)
                .ObservesProperty(() => ModelName)
                .ObservesProperty(() => ModelCode)
                .ObservesProperty(() => IsBusy);

            CancelCommand = new DelegateCommand(ExecuteCancelCommand);
        }

        /// <summary>
        /// 判断是否可以执行保存命令
        /// </summary>
        private bool CanExecuteSaveCommand()
        {
            return !IsBusy
                && !string.IsNullOrWhiteSpace(ModelName)
                && !string.IsNullOrWhiteSpace(ModelCode);
        }

        /// <summary>
        /// 执行保存命令
        /// </summary>
        private async void ExecuteSaveCommand()
        {
            try
            {
                IsBusy = true;
                Mouse.OverrideCursor = Cursors.Wait;

                // 创建新机种对象
                var newModel = new ProductModel
                {
                    ModelName = ModelName.Trim(),
                    ModelCode = ModelCode.Trim(),
                    Description = Description?.Trim(),
                    CreateTime = DateTime.Now,
                    IsActive = true,
                };

                // 保存到数据库
                int modelId = await _databaseService.AddProductModelAsync(newModel);

                MessageBox.Show(
                    "保存成功！",
                    "提示",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );

                // 使用DialogResult关闭对话框
                var parameters = new DialogParameters();
                parameters.Add("ModelId", modelId);
                parameters.Add("NewModel", newModel);
                RequestClose?.Invoke(new DialogResult(ButtonResult.OK, parameters));
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"保存失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                IsBusy = false;
                Mouse.OverrideCursor = null;
            }
        }

        /// <summary>
        /// 执行取消命令
        /// </summary>
        private void ExecuteCancelCommand()
        {
            // 使用ButtonResult.Cancel关闭对话框
            RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
        }

        public bool CanCloseDialog()
        {
            return !IsBusy;
        }

        public void OnDialogClosed()
        {
            // 清理资源
        }

        public void OnDialogOpened(IDialogParameters parameters)
        {
            // 获取参数
        }
    }
}
