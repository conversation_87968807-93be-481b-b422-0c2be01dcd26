using System;
using Newtonsoft.Json;

namespace Nickel_Inspect.Models.Track
{
    /// <summary>
    /// 轨道控制配置类，用于保存和加载配置
    /// </summary>
    public class TrackConfig
    {
        /// <summary>
        /// 默认运行模式
        /// </summary>
        public TrackMode DefaultMode { get; set; } = TrackMode.Normal;

        /// <summary>
        /// 默认运行方向
        /// </summary>
        public TrackDirection DefaultDirection { get; set; } = TrackDirection.LeftToRight;

        /// <summary>
        /// 是否启用SMEMA
        /// </summary>
        public bool SmemaEnabled { get; set; } = true;

        /// <summary>
        /// 皮带速度
        /// </summary>
        public int BeltSpeed { get; set; } = 40;

        /// <summary>
        /// 调宽轴速度
        /// </summary>
        public int WidthAxisSpeed { get; set; } = 20;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建一个包含默认值的新配置实例
        /// </summary>
        /// <returns>默认配置实例</returns>
        public static TrackConfig CreateDefault()
        {
            return new TrackConfig
            {
                DefaultMode = TrackMode.Normal,
                DefaultDirection = TrackDirection.LeftToRight,
                SmemaEnabled = true,
                BeltSpeed = 40,
                WidthAxisSpeed = 20,
                LastUpdated = DateTime.Now,
            };
        }

        /// <summary>
        /// 从TrackStateData创建配置
        /// </summary>
        /// <param name="state">轨道状态数据</param>
        /// <returns>新的配置实例</returns>
        public static TrackConfig FromState(TrackStateData state)
        {
            if (state == null)
                return CreateDefault();

            return new TrackConfig
            {
                DefaultMode = state.CurrentMode,
                DefaultDirection = state.Direction,
                SmemaEnabled = state.SmemaEnabled,
                BeltSpeed = state.BeltSpeed,
                WidthAxisSpeed = state.WidthAxisSpeed,
                LastUpdated = DateTime.Now,
            };
        }

        /// <summary>
        /// 应用配置到状态对象
        /// </summary>
        /// <param name="state">要更新的状态对象</param>
        public void ApplyToState(TrackStateData state)
        {
            if (state == null)
                return;

            state.CurrentMode = DefaultMode;
            state.Direction = DefaultDirection;
            state.SmemaEnabled = SmemaEnabled;
            state.BeltSpeed = BeltSpeed;
            state.WidthAxisSpeed = WidthAxisSpeed;
        }
    }
}
