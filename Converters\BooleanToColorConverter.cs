using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Nickel_Inspect.Converters
{
    /// <summary>
    /// 布尔值到颜色的转换器
    /// </summary>
    public class BooleanToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Brushes.Green : Brushes.Gray;
            }
            return Brushes.Gray;
        }

        public object ConvertBack(
            object value,
            Type targetType,
            object parameter,
            CultureInfo culture
        )
        {
            throw new NotImplementedException();
        }
    }
}
