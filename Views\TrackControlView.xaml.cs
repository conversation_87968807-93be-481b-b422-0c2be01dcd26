﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using Nickel_Inspect.ViewModels;
using Prism.Commands;

namespace Nickel_Inspect.Views
{
    /// <summary>
    /// Interaction logic for TrackControlView
    /// </summary>
    public partial class TrackControlView : UserControl
    {
        public TrackControlView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 滚动日志到底部
        /// </summary>
        public void ScrollLogToEnd()
        {
            if (LogRichTextBox != null)
            {
                LogRichTextBox.ScrollToEnd();
            }
        }

        /// <summary>
        /// 添加日志到RichTextBox
        /// </summary>
        public void AppendLogMessage(DateTime timestamp, string type, string message)
        {
            if (LogRichTextBox == null)
                return;

            // 创建新的段落
            Paragraph paragraph = new Paragraph();
            paragraph.LineHeight = 15; // 设置较小的行间距
            paragraph.Margin = new System.Windows.Thickness(0); // 移除段落边距

            // 添加时间戳
            Run timestampRun = new Run(timestamp.ToString("HH:mm:ss.fff") + " ");
            timestampRun.Foreground = System.Windows.Media.Brushes.Gray;
            paragraph.Inlines.Add(timestampRun);

            // 添加类型，根据类型设置不同颜色
            Run typeRun = new Run(type + " ");
            switch (type)
            {
                case "错误":
                    typeRun.Foreground = System.Windows.Media.Brushes.Red;
                    break;
                case "警告":
                    typeRun.Foreground = System.Windows.Media.Brushes.Orange;
                    break;
                case "信息":
                    typeRun.Foreground = System.Windows.Media.Brushes.Green;
                    break;
                default:
                    typeRun.Foreground = System.Windows.Media.Brushes.Black;
                    break;
            }
            paragraph.Inlines.Add(typeRun);

            // 添加消息
            Run messageRun = new Run(message);
            paragraph.Inlines.Add(messageRun);

            // 将段落添加到文档
            LogRichTextBox.Document.Blocks.Add(paragraph);

            // 滚动到底部
            ScrollLogToEnd();
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        public void ClearLog()
        {
            if (LogRichTextBox != null)
            {
                LogRichTextBox.Document.Blocks.Clear();
            }
        }

        private void OnBoardInSensorClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleBoardInSensorCommand.CanExecute()
            )
            {
                viewModel.ToggleBoardInSensorCommand.Execute();
            }
        }

        private void OnBoardArrivedSensorClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleBoardArrivedSensorCommand.CanExecute()
            )
            {
                viewModel.ToggleBoardArrivedSensorCommand.Execute();
            }
        }

        private void OnBoardOutSensorClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleBoardOutSensorCommand.CanExecute()
            )
            {
                viewModel.ToggleBoardOutSensorCommand.Execute();
            }
        }

        private void OnStopperCylinderClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleStopperCylinderCommand.CanExecute()
            )
            {
                viewModel.ToggleStopperCylinderCommand.Execute();
            }
        }

        private void OnClampCylinderClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleClampCylinderCommand.CanExecute()
            )
            {
                viewModel.ToggleClampCylinderCommand.Execute();
            }
        }

        private void OnNextMachineReadyClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleNextMachineReadyCommand.CanExecute()
            )
            {
                viewModel.ToggleNextMachineReadyCommand.Execute();
            }
        }

        private void OnHasBoardClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleHasBoardCommand.CanExecute()
            )
            {
                viewModel.ToggleHasBoardCommand.Execute();
            }
        }

        private void OnReadyToReceiveClick(object sender, MouseButtonEventArgs e)
        {
            if (
                DataContext is TrackControlViewModel viewModel
                && viewModel.ToggleReadyToReceiveCommand.CanExecute()
            )
            {
                viewModel.ToggleReadyToReceiveCommand.Execute();
            }
        }
    }
}
