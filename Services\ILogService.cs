using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nickel_Inspect.Models;

namespace Nickel_Inspect.Services
{
    /// <summary>
    /// 日志服务接口
    /// </summary>
    public interface ILogService : IDisposable
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="source">日志来源</param>
        void LogInformation(string message, string source = "系统");

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="source">日志来源</param>
        void LogWarning(string message, string source = "系统");

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="source">日志来源</param>
        void LogError(string message, string source = "系统");

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">日志消息</param>
        /// <param name="source">日志来源</param>
        void LogError(Exception exception, string message, string source = "系统");

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="source">日志来源</param>
        void LogDebug(string message, string source = "系统");

        /// <summary>
        /// 获取最近的日志记录
        /// </summary>
        /// <param name="count">要获取的日志数量</param>
        /// <param name="logType">日志类型筛选</param>
        /// <param name="source">日志来源筛选</param>
        /// <returns>日志记录列表</returns>
        Task<IEnumerable<SystemLog>> GetRecentLogsAsync(
            int count = 1000,
            string logType = null,
            string source = null
        );

        /// <summary>
        /// 获取指定日期范围内的日志
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="logType">日志类型筛选</param>
        /// <param name="source">日志来源筛选</param>
        /// <returns>日志记录列表</returns>
        Task<IEnumerable<SystemLog>> GetLogsByDateRangeAsync(
            DateTime startDate,
            DateTime endDate,
            string logType = null,
            string source = null
        );

        /// <summary>
        /// 按日志类型和日期范围获取日志
        /// </summary>
        /// <param name="logTypes">日志类型列表</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="source">日志来源筛选，可选</param>
        /// <returns>日志记录列表</returns>
        Task<IEnumerable<SystemLog>> GetLogsByTypeAndDateRangeAsync(
            List<string> logTypes,
            DateTime startDate,
            DateTime endDate,
            string source = null
        );

        /// <summary>
        /// 获取所有不同的日志来源
        /// </summary>
        /// <returns>不同的日志来源列表</returns>
        Task<IEnumerable<string>> GetDistinctSourcesAsync();

        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        /// <param name="beforeDate">清理此日期之前的日志</param>
        /// <returns>被清理的日志数量</returns>
        Task<int> CleanupLogsAsync(DateTime beforeDate);
    }
}
