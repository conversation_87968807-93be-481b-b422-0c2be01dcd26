---
description: 
globs: 
alwaysApply: false
---
# 镍片检查系统界面风格指南

## 界面整体架构

### 主窗口结构
- **文件路径**：[Views/MainWindow.xaml](mdc:Views/MainWindow.xaml)
- **布局方式**：采用Prism区域(Region)管理的单文档界面(SDI)
- **主要区域**：
  - 顶部菜单栏和工具栏
  - 左侧功能导航面板
  - 中央主内容区域（根据选择的功能显示不同视图）
  - 底部状态栏（显示系统状态、报警信息和日志摘要）

### 视图切换机制
- 基于Prism框架的Region机制实现视图切换
- 主要视图包括：
  - [Views/InspectionView.xaml](mdc:Views/InspectionView.xaml) - 检测主界面
  - [Views/ConfigPointView.xaml](mdc:Views/ConfigPointView.xaml) - 检测点配置
  - [Views/TrackControlView.xaml](mdc:Views/TrackControlView.xaml) - 轨道控制
  - [Views/LogQueryView.xaml](mdc:Views/LogQueryView.xaml) - 日志查询
  - [Views/IOMonitorView.xaml](mdc:Views/IOMonitorView.xaml) - IO监控
  - [Views/VisionProTestView.xaml](mdc:Views/VisionProTestView.xaml) - 视觉测试

## 设计风格

### 色彩方案
- **主色调**：以蓝色作为主色调，体现工业化和科技感
- **状态颜色**：
  - 绿色：正常、通过
  - 红色：错误、不通过
  - 黄色：警告、注意
  - 灰色：禁用、未激活

### 控件样式
- 使用HandyControl组件库提供的现代化控件
- 自定义控件样式定义在App.xaml资源字典中
- 常用控件样式：
  - 按钮：圆角矩形，有悬停效果
  - 数据表格：行交替背景色，突出当前选中行
  - 状态指示灯：圆形LED风格指示灯
  - 图表：现代简约风格，清晰的数据展示

### 布局规范
- 采用Grid网格布局作为主要布局方式
- 使用统一的边距和间距（通常为10-20像素）
- 相关控件组合使用GroupBox或Border进行分组
- 控件对齐方式保持统一

## 交互设计

### 检测界面
- **文件路径**：[Views/InspectionView.xaml](mdc:Views/InspectionView.xaml)
- **主要特点**：
  - 左侧显示实时相机图像和检测结果
  - 右侧显示检测点列表和当前检测状态
  - 顶部显示产品型号选择和控制按钮
  - 底部显示统计信息和进度条

### 配置界面
- **文件路径**：[Views/ConfigPointView.xaml](mdc:Views/ConfigPointView.xaml)
- **主要特点**：
  - 表格形式展示所有检测点
  - 支持直接编辑和批量操作
  - 右键菜单提供上下文操作
  - 表单式的添加/编辑对话框

### 轨道控制界面
- **文件路径**：[Views/TrackControlView.xaml](mdc:Views/TrackControlView.xaml)
- **主要特点**：
  - 可视化展示轨道状态和载具位置
  - 提供手动控制按钮
  - 显示传感器和气缸状态
  - 支持方向和模式切换

### IO监控界面
- **文件路径**：[Views/IOMonitorView.xaml](mdc:Views/IOMonitorView.xaml)
- **主要特点**：
  - 分组展示输入和输出点位
  - 使用指示灯显示状态
  - 提供强制置位功能（调试模式）
  - 支持筛选和搜索

## 对话框设计

### 通用对话框样式
- 使用Prism的DialogService实现对话框
- 对话框居中显示，带有半透明遮罩
- 标题栏统一样式，包含图标和关闭按钮
- 底部按钮区域对齐，通常包含"确定"和"取消"按钮

### 特定对话框
- **添加检测点**：[Views/AddInspectPoint.xaml](mdc:Views/AddInspectPoint.xaml)
- **编辑检测点**：[Views/EditInspectPoint.xaml](mdc:Views/EditInspectPoint.xaml)
- **新建产品型号**：[Views/NewProductWindow.xaml](mdc:Views/NewProductWindow.xaml)
- **启动闪屏**：[Views/Dialogs/StartupSplashDialog.xaml](mdc:Views/Dialogs/StartupSplashDialog.xaml)

## 响应式设计

### 分辨率适应
- 界面设计基于1920x1080分辨率优化
- 关键控件使用相对尺寸以适应不同分辨率
- 使用ScrollViewer确保内容在小分辨率下可访问

### 实时状态反馈
- 操作按钮有清晰的状态变化（启用/禁用/悬停）
- 长时间操作显示进度指示
- 重要状态变化伴有颜色变化和可选的声音提示

## 辅助功能

### 快捷键
- 常用操作提供键盘快捷键
- 快捷键在界面上有提示（如按钮提示）
- 常用快捷键:
  - F5: 开始检测
  - F6: 暂停检测
  - F7: 停止检测
  - Ctrl+N: 新建
  - Ctrl+S: 保存

### 错误提示
- 使用弹出式消息框显示错误
- 严重错误使用模态对话框，需要用户确认
- 非严重错误使用可自动消失的提示条
- 错误消息简明扼要，并提供可能的解决方案

