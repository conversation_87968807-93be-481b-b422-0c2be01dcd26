using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace Nickel_Inspect.Views
{
    /// <summary>
    /// ConfigPointView.xaml 的交互逻辑
    /// </summary>
    public partial class ConfigPointView : UserControl
    {
        // 是否允许键盘导航
        private bool _allowKeyboardNavigation = true;
        
        // 跟踪按键状态，防止重复触发
        private Dictionary<Key, bool> _keyStates = new Dictionary<Key, bool>
        {
            { Key.W, false },
            { Key.S, false },
            { Key.A, false },
            { Key.D, false },
            { Key.Q, false },
            { Key.E, false }
        };

        public ConfigPointView()
        {
            InitializeComponent();
            this.Loaded += ConfigPointView_Loaded;
            this.IsVisibleChanged += ConfigPointView_IsVisibleChanged;
        }

        private void ConfigPointView_IsVisibleChanged(
            object sender,
            DependencyPropertyChangedEventArgs e
        )
        {
            // 当控件变为可见时，确保其能获得焦点
            if ((bool)e.NewValue)
            {
                this.Focus();
            }
        }

        private void ConfigPointView_Loaded(object sender, RoutedEventArgs e)
        {
            if (this.DataContext is ViewModels.ConfigPointViewModel viewModel)
            {
                viewModel.OnNavigatedTo();
            }

            // 加载时设置焦点到UserControl以便接收键盘事件
            this.Focus();
        }

        /// <summary>
        /// 处理键盘按下事件，执行WSAD轴运动控制
        /// </summary>
        private void UserControl_KeyDown(object sender, KeyEventArgs e)
        {
            if (!_allowKeyboardNavigation)
                return;

            // 如果键已经处于按下状态，不重复处理
            if (_keyStates.ContainsKey(e.Key) && _keyStates[e.Key])
                return;

            if (this.DataContext is ViewModels.ConfigPointViewModel viewModel)
            {
                switch (e.Key)
                {
                    case Key.W:
                        viewModel.StartJogCommand.Execute("Y-");
                        _keyStates[e.Key] = true;
                        e.Handled = true;
                        break;
                    case Key.S:
                        viewModel.StartJogCommand.Execute("Y+");
                        _keyStates[e.Key] = true;
                        e.Handled = true;
                        break;
                    case Key.A:
                        viewModel.StartJogCommand.Execute("X-");
                        _keyStates[e.Key] = true;
                        e.Handled = true;
                        break;
                    case Key.D:
                        viewModel.StartJogCommand.Execute("X+");
                        _keyStates[e.Key] = true;
                        e.Handled = true;
                        break;
                    case Key.Q:
                        viewModel.StartJogCommand.Execute("Z-");
                        _keyStates[e.Key] = true;
                        e.Handled = true;
                        break;
                    case Key.E:
                        viewModel.StartJogCommand.Execute("Z+");
                        _keyStates[e.Key] = true;
                        e.Handled = true;
                        break;
                }
            }
        }

        /// <summary>
        /// 处理键盘抬起事件，停止轴运动
        /// </summary>
        private void UserControl_KeyUp(object sender, KeyEventArgs e)
        {
            if (!_allowKeyboardNavigation)
                return;

            if (this.DataContext is ViewModels.ConfigPointViewModel viewModel)
            {
                // 优先级最高的调度方式处理
                this.Dispatcher.Invoke(
                    DispatcherPriority.Send,
                    new Action(() =>
                    {
                        switch (e.Key)
                        {
                            case Key.W:
                            case Key.S:
                                viewModel.StopJogCommand.Execute("Y");
                                if (_keyStates.ContainsKey(e.Key))
                                    _keyStates[e.Key] = false;
                                break;
                            case Key.A:
                            case Key.D:
                                viewModel.StopJogCommand.Execute("X");
                                if (_keyStates.ContainsKey(e.Key))
                                    _keyStates[e.Key] = false;
                                break;
                            case Key.Q:
                            case Key.E:
                                viewModel.StopJogCommand.Execute("Z");
                                if (_keyStates.ContainsKey(e.Key))
                                    _keyStates[e.Key] = false;
                                break;
                        }
                    })
                );

                e.Handled = true;
            }
        }

        /// <summary>
        /// 预览键盘按下事件，检测焦点是否在文本输入控件上
        /// </summary>
        private void UserControl_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            // 获取当前焦点元素
            var focusedElement = Keyboard.FocusedElement;

            // 判断焦点是否在文本输入控件上
            bool isTextInputControl =
                focusedElement is TextBox
                || focusedElement is PasswordBox
                || focusedElement is RichTextBox
                || focusedElement is ComboBox
                || (
                    focusedElement is FrameworkElement
                    && ((FrameworkElement)focusedElement).Parent is ComboBox
                );

            // 如果焦点在文本输入控件上，禁用键盘导航
            _allowKeyboardNavigation = !isTextInputControl;

            // 如果是WSAD键，且焦点在文本输入控件上，不处理该事件
            if (
                !_allowKeyboardNavigation
                && (
                    e.Key == Key.W
                    || e.Key == Key.S
                    || e.Key == Key.A
                    || e.Key == Key.D
                    || e.Key == Key.Q
                    || e.Key == Key.E
                )
            )
            {
                // 不设置e.Handled = true，因为需要让文本输入控件接收这些键
                return;
            }
        }
    }
}
