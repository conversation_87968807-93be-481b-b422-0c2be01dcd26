using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nickel_Inspect.Models;

namespace Nickel_Inspect.Services
{
    /// <summary>
    /// 检查服务接口
    /// </summary>
    public interface IInspectionService : IDisposable
    {
        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="machineConfig">机器配置助手</param>
        void Initialize(MachineConfigurationHelper machineConfig);

        void LoadPointsToolBlock(List<InspectionPoint> inspectionPoints);

        /// <summary>
        /// 开始检查
        /// </summary>
        /// <param name="model">产品机种</param>
        /// <param name="points">检查点列表</param>
        /// <returns>检查任务</returns>
        Task<bool> StartInspectionAsync(ProductModel model, IEnumerable<InspectionPoint> points);

        /// <summary>
        /// 开始点位检查（在产品到位后调用）
        /// </summary>
        /// <returns>是否成功启动点位检查</returns>
        Task<bool> StartInspectPointsAsync();

        /// <summary>
        /// 暂停检查
        /// </summary>
        Task PauseInspectionAsync();

        /// <summary>
        /// 继续检查
        /// </summary>
        Task ResumeInspectionAsync();

        /// <summary>
        /// 停止检查
        /// </summary>
        Task StopInspectionAsync();

        /// <summary>
        /// 检查完成事件
        /// </summary>
        event EventHandler<InspectionCompletedEventArgs> InspectionCompleted;

        /// <summary>
        /// 检查点完成事件
        /// </summary>
        event EventHandler<InspectionPointCompletedEventArgs> InspectionPointCompleted;

        /// <summary>
        /// 检查错误事件
        /// </summary>
        event EventHandler<InspectionErrorEventArgs> InspectionError;

        /// <summary>
        /// 状态变更事件
        /// </summary>
        event EventHandler<InspectionStatus> StatusChanged;

        /// <summary>
        /// 获取当前检查状态
        /// </summary>
        InspectionStatus Status { get; }

        /// <summary>
        /// 是否正在检查
        /// </summary>
        bool IsInspecting { get; }
    }

    /// <summary>
    /// 检查状态
    /// </summary>
    public enum InspectionStatus
    {
        /// <summary>
        /// 空闲
        /// </summary>
        Idle,

        /// <summary>
        /// 检查中
        /// </summary>
        Inspecting,

        /// <summary>
        /// 暂停
        /// </summary>
        Paused,

        /// <summary>
        /// 完成
        /// </summary>
        Completed,

        /// <summary>
        /// 错误
        /// </summary>
        Error,
    }

    /// <summary>
    /// 检查完成事件参数
    /// </summary>
    public class InspectionCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 检查结果
        /// </summary>
        public bool IsPass { get; set; }

        /// <summary>
        /// NG数量
        /// </summary>
        public int NgCount { get; set; }

        /// <summary>
        /// 总检查点数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 检查结果列表
        /// </summary>
        public List<InspectionPointResult> Results { get; set; }
    }

    /// <summary>
    /// 检查点完成事件参数
    /// </summary>
    public class InspectionPointCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 检查点
        /// </summary>
        public InspectionPoint Point { get; set; }

        /// <summary>
        /// 检查结果
        /// </summary>
        public bool IsPass { get; set; }

        /// <summary>
        /// 检查结果详情
        /// </summary>
        public InspectionPointResult Result { get; set; }

        /// <summary>
        /// 当前检查点索引
        /// </summary>
        public int CurrentIndex { get; set; }

        /// <summary>
        /// 总检查点数量
        /// </summary>
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// 检查错误事件参数
    /// </summary>
    public class InspectionErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常
        /// </summary>
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 检查点结果
    /// </summary>
    public class InspectionPointResult
    {
        /// <summary>
        /// 检查点
        /// </summary>
        public InspectionPoint Point { get; set; }

        /// <summary>
        /// 检查结果
        /// </summary>
        public bool IsPass { get; set; }

        /// <summary>
        /// 结果描述
        /// </summary>
        public string ResultDescription { get; set; }

        /// <summary>
        /// 图像路径
        /// </summary>
        public string ImagePath { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime InspectionTime { get; set; }
    }
}
