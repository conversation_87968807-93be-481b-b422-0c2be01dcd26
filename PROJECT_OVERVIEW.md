# 景旺镍片检测系统 - 项目全貌文档

## 📋 项目概述

**景旺镍片检测系统**是一个基于工业视觉的高精度镍片缺陷检测系统，专门用于PCB板镍片质量的自动化检测。系统采用XYZ运动平台+工业相机的架构设计，结合皮带传送和调宽装置，实现PCB板的全自动传送、定位和缺陷检测。

### 🎯 核心功能

- **自动缺陷检测**：基于Cognex VisionPro SDK的高精度视觉检测
- **全自动化流程**：PCB板传入→定位→检测→传出全程自动控制
- **多模式运行**：支持正常、直通、手动三种运行模式
- **双向传输**：支持左进右出和右进左出两个方向
- **SMEMA标准**：符合SMT行业标准通信协议，可无缝集成生产线
- **实时监控**：传感器状态监控、防抖动处理、气缸控制
- **安全保护**：紧急停止、多级报警系统、错误处理
- **数据管理**：检测数据统计、图像存储、报表生成
- **产品管理**：支持多产品型号和检测点配置

### 🏗️ 系统架构

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   用户界面层     │  │   业务逻辑层     │  │   硬件控制层     │
├─────────────────┤  ├─────────────────┤  ├─────────────────┤
│ WPF + MVVM      │──│ 轨道控制服务     │──│ 运动控制卡       │
│ HandyControl    │  │ 检测服务        │  │ 视觉系统        │
│ Prism Framework │  │ 视觉服务        │  │ 光源控制器       │
│                 │  │ 状态管理器       │  │ IO设备          │
│                 │  │ 报警系统        │  │                │
└─────────────────┘  └─────────────────┘  └─────────────────┘
          │                    │                    │
          └────────────────────┼────────────────────┘
                              │
                    ┌─────────────────┐
                    │   数据存储层     │
                    ├─────────────────┤
                    │ SQLite 数据库    │
                    │ 配置文件        │
                    │ 图像存储        │
                    │ 日志文件        │
                    └─────────────────┘
```

## 🛠️ 技术栈

### 开发环境
- **开发语言**：C# (.NET Framework 4.8.1)
- **UI框架**：WPF (Windows Presentation Foundation)
- **架构模式**：MVVM + Prism Framework
- **依赖注入**：DryIoc Container
- **开发工具**：Visual Studio 2019/2022

### 核心依赖库
- **Prism.DryIoc (8.1.97)**：MVVM框架和依赖注入
- **HandyControl (3.5.1)**：现代化UI控件库
- **Cognex VisionPro SDK**：工业视觉处理（需单独安装）
- **System.Data.SQLite (1.0.119)**：本地数据库
- **Newtonsoft.Json (13.0.3)**：JSON配置处理
- **Serilog (4.0.0)**：日志框架
- **FluentModbus (5.3.1)**：Modbus通信协议
- **Dapper (2.1.66)**：数据库ORM映射

### 硬件要求
- **操作系统**：Windows 10 x64 或更高版本
- **运动控制卡**：支持ModbusRTU通信的运动控制卡
- **工业相机**：网口相机（需要eBUS SDK支持）
- **光源控制器**：支持串口通信的LED光源控制器

## 📁 项目结构详解

```
Nickel_Inspect/
├── Models/                     # 数据模型层
│   ├── Track/                  # 轨道相关模型
│   │   ├── TrackState.cs           # 轨道状态定义
│   │   ├── TrackConfig.cs          # 轨道配置模型
│   │   ├── TrackEnums.cs           # 轨道枚举类型
│   │   └── BoardArrivedEventArgs.cs # 板卡到达事件
│   ├── LightControl/           # 光源控制模型
│   │   └── LightController.cs      # 光源控制器配置
│   ├── DeviceConfiguration.cs  # 设备硬件配置
│   ├── IoConfig.cs            # IO点位配置
│   ├── InspectionPoint.cs     # 检测点模型
│   ├── ProductModel.cs        # 产品型号模型
│   └── SystemLog.cs           # 系统日志模型
├── Services/                   # 服务层（核心业务逻辑）
│   ├── Track/                  # 轨道服务
│   │   ├── ITrackService.cs        # 轨道服务接口
│   │   ├── TrackService.cs         # 轨道控制实现
│   │   ├── ITrackConfigService.cs  # 轨道配置接口
│   │   └── TrackConfigService.cs   # 轨道配置实现
│   ├── Alarms/                 # 报警系统
│   │   ├── AlarmManager.cs         # 报警管理器
│   │   ├── AlarmSystemHelper.cs    # 报警系统助手
│   │   ├── AlarmSystemInitializer.cs # 报警初始化器
│   │   ├── DigitalInputAlarmSource.cs # 数字输入报警源
│   │   ├── IAlarmSignalSource.cs   # 报警信号源接口
│   │   └── MotionAlarmSource.cs    # 运动控制报警源
│   ├── LightControl/           # 光源控制服务
│   │   ├── ILightControlService.cs # 光源控制接口
│   │   ├── LightControlService.cs  # 光源控制实现
│   │   ├── LightControllerBase.cs  # 控制器基类
│   │   ├── CCSLightController.cs   # CCS品牌控制器
│   │   ├── HikvisionLightController.cs # 海康控制器
│   │   └── YixuLightController.cs  # 忆徐控制器
│   ├── BkMotionCard.cs         # 运动控制卡服务
│   ├── DatabaseService.cs      # 数据库服务
│   ├── InspectionService.cs    # 检测服务（核心）
│   ├── VisionProService.cs     # 视觉处理服务
│   ├── StatusManager.cs        # 状态管理器
│   └── LogService.cs           # 日志服务
├── ViewModels/                 # 视图模型层
│   ├── MainWindowViewModel.cs      # 主窗口视图模型
│   ├── InspectionViewModel.cs      # 检测界面视图模型
│   ├── TrackControlViewModel.cs    # 轨道控制视图模型
│   ├── ConfigPointViewModel.cs     # 检测点配置视图模型
│   ├── IOMonitorViewModel.cs       # IO监控视图模型
│   ├── VisionProTestViewModel.cs   # 视觉测试视图模型
│   ├── NewProductViewModel.cs      # 产品管理视图模型
│   ├── AddInspectPointViewModel.cs # 添加检测点视图模型
│   └── EditInspectPointViewModel.cs # 编辑检测点视图模型
├── Views/                      # 视图层
│   ├── MainWindow.xaml             # 主窗口
│   ├── InspectionView.xaml         # 检测界面
│   ├── TrackControlView.xaml       # 轨道控制界面
│   ├── ConfigPointView.xaml        # 检测点配置界面
│   ├── IOMonitorView.xaml          # IO监控界面
│   ├── VisionProTestView.xaml      # 视觉测试界面
│   ├── NewProductWindow.xaml       # 产品管理窗口
│   ├── AddInspectPoint.xaml        # 添加检测点对话框
│   ├── EditInspectPoint.xaml       # 编辑检测点对话框
│   └── Dialogs/                    # 系统对话框
│       ├── StartupSplashDialog.xaml    # 启动闪屏
│       └── ShutdownProgressDialog.xaml # 关闭进度对话框
├── Converters/                 # 值转换器
├── Config/                     # 配置文件目录
│   └── LightControllerConfig.json  # 光源控制器配置
├── Resources/                  # 资源文件
│   └── VPP文件备份/            # VisionPro工具块备份
├── Images/                     # 图像资源
├── DeviceConfiguration.json    # 设备配置文件（核心）
├── ModbusConfig.json          # Modbus通信配置
├── App.xaml                   # 应用程序定义
└── App.xaml.cs                # 应用程序入口点
```

## 🔧 核心服务详解

### 1. TrackService - 轨道控制服务
**职责**：管理PCB板的传送流程，实现SMEMA协议
- **状态机驱动**：20+种状态的复杂状态转换
- **传感器防抖**：防止信号干扰导致的误判
- **双向传输**：左右双向灵活配置
- **离线模拟**：支持无硬件调试模式
- **安全保护**：急停、限位、超时保护

### 2. InspectionService - 检测服务
**职责**：协调整个检测流程，管理多检测点执行
- **异步流程**：支持取消、暂停、继续操作
- **多点检测**：按序号依次执行检测点
- **运动协调**：精确控制相机位置移动
- **结果管理**：OK/NG统计和详细记录
- **图像存储**：自动保存原始和处理图像

### 3. VisionProService - 视觉服务
**职责**：封装Cognex VisionPro SDK，提供视觉检测能力
- **线程安全**：STA线程处理ActiveX控件
- **工具块管理**：加载和执行VPP文件
- **图像处理**：获取处理结果和缺陷信息
- **多格式支持**：支持各种图像格式保存

### 4. BkMotionCard - 运动控制服务
**职责**：封装运动控制卡通信，提供硬件控制接口
- **ModbusRTU通信**：工业标准通信协议
- **缓存机制**：减少硬件IO，提高响应速度
- **线程安全**：所有操作加锁保护
- **错误处理**：全面的异常监控和日志记录

### 5. StatusManager - 状态管理器
**职责**：管理设备整体状态，控制指示灯和蜂鸣器
- **状态协调**：Running、Standby、Warning、Error等
- **指示灯控制**：三色灯的闪烁和常亮控制
- **报警联动**：根据报警类型自动调整状态
- **急停功能**：检测严重报警时自动急停

### 6. AlarmManager - 报警管理器
**职责**：统一管理各类报警源，实现实时监控
- **多报警源**：数字输入、运动控制、自定义报警
- **实时监控**：后台任务持续检查报警状态
- **事件分发**：线程安全的报警事件通知
- **批量操作**：支持全部报警的批量复位

## ⚙️ 配置系统

### DeviceConfiguration.json（主配置文件）
```json
{
  "DeviceConfig": {
    "DeviceName": "设备名称",
    "Axes": [
      {
        "AxisNo": 0, "Name": "X轴",
        "PulseEquivalent": 0.04,
        "StartSpeed": 200, "RunSpeed": 500,
        "MaxDistance": 1200.0
      }
    ]
  },
  "IoConfig": {
    "Inputs": [...],    // 输入点配置
    "Outputs": [...]    // 输出点配置
  },
  "Alarms": {
    "ServoAlarms": {...},    // 伺服报警配置
    "MotionAlarms": {...}    // 运动报警配置
  }
}
```

### LightControllerConfig.json（光源配置）
```json
[
  {
    "Id": 1, "Name": "光源控制器1",
    "Brand": "Yixun", "Model": "YYK-DCM2415-4",
    "Port": "COM5", "BaudRate": 19200,
    "ChannelCount": 4, "SupportsRgb": false
  }
]
```

## 🔄 业务流程

### 1. 自动检测流程
```
PCB进料 → 传感器检测 → 皮带运行 → 产品定位 → 
夹紧固定 → 相机移动 → 逐点检测 → 结果判定 → 
松开夹具 → 产品出料 → 流程结束
```

### 2. 状态机转换
- **轨道状态**：空闲 → 等待进料 → 板卡进入 → 定位完成 → 检测中 → 等待出料 → 出料中 → 空闲
- **检测状态**：空闲 → 检测中 → 暂停/完成/错误

### 3. 报警处理流程
```
传感器/硬件 → 报警源检测 → 报警管理器 → 状态管理器 → 
设备状态更新 → UI提示 → 人工处理 → 报警复位
```

## 🚀 快速上手

### 环境准备
1. **安装Visual Studio 2019/2022**
2. **安装.NET Framework 4.8.1 SDK**
3. **安装Cognex VisionPro SDK**（如需视觉功能）
4. **配置硬件设备**（运动控制卡、相机、光源等）

### 运行项目
1. **克隆代码库**到本地开发环境
2. **打开解决方案**`Nickel_Inspect.sln`
3. **恢复NuGet包**（自动或手动还原）
4. **配置设备参数**编辑`DeviceConfiguration.json`
5. **编译运行**选择x64平台编译运行

### 调试模式
- **离线模拟**：`DeviceConfiguration.json`中设置`"enableOfflineSimulation": true`
- **模拟延迟**：设置`"offlineSimulationDelayMs": 2000`控制模拟时间
- **手动控制**：使用`TrackControlView`和`IOMonitorView`手动操作

## 📊 核心特性

### 🎯 高精度检测
- 基于Cognex VisionPro专业视觉算法
- 支持多种缺陷类型识别（矩形、圆形、点状等）
- 亚像素级定位精度
- 可配置检测参数和阈值

### 🔄 灵活配置
- 支持多产品型号管理
- 可配置检测点位置和参数
- 支持多种光源控制器
- 动态加载检测算法文件(.vpp)

### 🛡️ 安全可靠
- 多级报警系统保护
- 急停功能确保人员安全
- 限位和超时保护
- 完整的错误处理和日志记录

### 📈 生产效率
- 全自动化流程，减少人工干预
- SMEMA标准接口，无缝集成产线
- 高速检测，支持大批量生产
- 实时数据统计和报表生成

## 🔧 维护指南

### 定期维护
- **清理缓存**：定期清理图像和日志文件
- **更新配置**：根据产品变化调整检测参数
- **备份数据**：定期备份配置文件和检测数据
- **硬件保养**：定期维护相机、光源等硬件设备

### 故障排除
- **查看日志**：Serilog记录详细的系统运行日志
- **IO监控**：使用IOMonitorView检查硬件信号状态
- **离线调试**：使用模拟模式排除硬件问题
- **报警复位**：通过界面或配置复位报警状态

### 性能优化
- **图像压缩**：合理设置图像保存格式和质量
- **缓存策略**：优化IO读取缓存时间
- **并发控制**：避免多线程冲突
- **内存管理**：及时释放大图像对象

## 📞 技术支持

### 文档资源
- **README.md**：项目基础说明
- **VisionProReadMe.md**：视觉系统使用指南  
- **ObfuscarReadMe.md**：代码混淆配置说明

### 开发规范
- **MVVM模式**：严格遵循视图、视图模型、模型分离
- **依赖注入**：使用Prism框架的DI容器管理依赖
- **异步编程**：耗时操作使用async/await模式
- **错误处理**：完善的异常捕获和日志记录

---

*本文档由系统自动生成，反映了项目的当前状态。如需详细的技术实现细节，请参考源代码和相关技术文档。*