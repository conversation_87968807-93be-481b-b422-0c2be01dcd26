<prism:PrismApplication
    x:Class="Nickel_Inspect.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:Nickel_Inspect.Converters"
    xmlns:local="clr-namespace:Nickel_Inspect"
    xmlns:prism="http://prismlibrary.com/">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  转换器  -->
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />
            <converters:BooleanInvertConverter x:Key="BooleanInvertConverter" />
            <converters:FilePathToNameConverter x:Key="FilePathToNameConverter" />

        </ResourceDictionary>
    </Application.Resources>
</prism:PrismApplication>