---
description: 
globs: 
alwaysApply: false
---
# 镍片检查系统数据模型与状态管理

## 核心数据模型

### 产品模型 (ProductModel)
- **文件路径**：[Models/ProductModel.cs](mdc:Models/ProductModel.cs)
- **功能**：定义产品型号的基本信息
- **主要属性**：
  - 产品ID、名称、描述
  - 创建时间和修改时间
  - 是否启用
  - 相关检测点集合

### 检测点模型 (InspectionPoint)
- **文件路径**：[Models/InspectionPoint.cs](mdc:Models/InspectionPoint.cs)
- **功能**：定义检测点的位置、参数和配置
- **主要属性**：
  - 点位ID、名称
  - XYZ坐标位置
  - 关联的视觉工具块文件(.vpp)
  - 光源设置（亮度、通道等）
  - 相机参数（曝光时间等）
  - 是否启用

### 设备配置模型 (DeviceConfiguration)
- **文件路径**：[Models/DeviceConfiguration.cs](mdc:Models/DeviceConfiguration.cs)
- **功能**：封装设备硬件配置
- **主要部分**：
  - 轴配置
  - IO点位配置
  - 光源控制器配置
  - 报警配置
  - 系统参数

### 系统日志模型 (SystemLog)
- **文件路径**：[Models/SystemLog.cs](mdc:Models/SystemLog.cs)
- **功能**：记录系统运行日志
- **主要属性**：
  - 日志ID、时间
  - 日志级别
  - 模块名称
  - 消息和详情

## 状态管理

### 设备状态
```csharp
public enum DeviceStatus
{
    Uninitialized,  // 未初始化
    Initializing,   // 初始化中
    Idle,           // 空闲
    Running,        // 运行中
    Paused,         // 暂停
    Error,          // 错误
    EmergencyStop   // 紧急停止
}
```

### 轨道状态
```csharp
public enum BoardTransportState
{
    Idle,               // 空闲等待
    WaitingForBoard,    // 等待进料
    BoardArriving,      // 板卡正在进入
    BoardInPosition,    // 板卡到位
    Processing,         // 加工中
    WaitingForNext,     // 等待下游准备好
    Outputting,         // 出料中
    Error               // 错误状态
}
```

### 检测状态
```csharp
public enum InspectionStatus
{
    Idle,       // 空闲
    Inspecting, // 检查中
    Paused,     // 暂停
    Completed,  // 完成
    Error,      // 错误
}
```

### 状态管理器 (StatusManager)
- **文件路径**：[Services/StatusManager.cs](mdc:Services/StatusManager.cs)
- **功能**：管理整个系统的状态
- **主要职责**：
  - 设备状态监控和切换
  - 状态变化事件触发
  - 错误处理和报警管理
  - 安全保护逻辑
  - 状态信息更新和显示

### 状态转换流程

#### 设备启动流程
1. `Uninitialized` → `Initializing`：系统启动，开始初始化
2. `Initializing` → `Idle`：初始化完成，等待操作
3. `Idle` → `Running`：用户点击开始，系统开始运行

#### 检测流程状态转换
1. `Idle` → `Inspecting`：开始检测
2. `Inspecting` → `Completed`：检测完成
3. `Inspecting` → `Paused`：用户暂停检测
4. `Paused` → `Inspecting`：用户继续检测
5. `Inspecting` → `Error`：检测过程中出错

#### 轨道状态转换
1. `Idle` → `WaitingForBoard`：等待板卡进入
2. `WaitingForBoard` → `BoardArriving`：板卡开始进入
3. `BoardArriving` → `BoardInPosition`：板卡到达检测位置
4. `BoardInPosition` → `Processing`：开始检测
5. `Processing` → `WaitingForNext`：检测完成，准备出料
6. `WaitingForNext` → `Outputting`：下游准备好，开始出料
7. `Outputting` → `Idle`：出料完成，回到空闲状态

#### 异常处理
- 任何状态 → `Error`：发生错误
- 任何状态 → `EmergencyStop`：紧急停止按钮被触发
- `Error`/`EmergencyStop` → `Idle`：错误解决后复位

