using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Documents;
using FluentModbus;
using Newtonsoft.Json;
using Nickel_Inspect.Models; // 添加Models命名空间

namespace Nickel_Inspect.Services
{
    // 顶级配置类
    public class ModbusConfig
    {
        public string SerialPortName { get; set; } // 串口名称
        public int BaudRate { get; set; } // 波特率
        public byte SlaveAddress { get; set; } // 从机地址
        public ControllerRegistersConfig ControllerRegisters { get; set; } // 控制器寄存器
        public List<Models.AxisConfig> Axes { get; set; } // 轴列表
    }

    // 控制器寄存器配置类
    public class ControllerRegistersConfig
    {
        public int TotalException { get; set; } // 总异常寄存器地址
        public int ResetTotalException { get; set; } //清除总异常
        public int AllInputs_X0 { get; set; } // 输入寄存器 X0
        public int AllInputs_X1 { get; set; } // 输入寄存器 X1
        public int AllOutputs_Y0 { get; set; } // 输出寄存器 Y0
        public int AllOutputs_Y1 { get; set; } // 输出寄存器 Y1
        public int AllIO_Z0 { get; set; } = 2317; // Z0组IO寄存器地址
    }

    public class BkMotionCard
    {
        private const string configFilePath = "ModbusConfig.json";
        private readonly object _lock = new object(); // 用于线程同步
        private ModbusRtuClient _client;
        private ModbusConfig _config;
        private List<int> _z0InputBits = new List<int>(); // 存储Z0组中的输入位索引

        // 添加缓存机制，减少实际IO读取次数
        private Dictionary<int, ushort> _ioCache = new Dictionary<int, ushort>();
        private Dictionary<int, DateTime> _ioCacheTimestamp = new Dictionary<int, DateTime>();
        private const int IO_CACHE_LIFETIME_MS = 50; // 缓存有效期50毫秒

        // 添加日志事件
        public event EventHandler<LogEventArgs> LogMessageReceived;
        public event EventHandler<LogEventArgs> LogErrorReceived;
        public event EventHandler<LogEventArgs> LogWarningReceived;

        public BkMotionCard()
        {
            try
            {
                // 加载配置文件
                string json = File.ReadAllText(configFilePath);
                _config = JsonConvert.DeserializeObject<ModbusConfig>(json);
                if (_config == null)
                    throw new Exception("配置文件解析失败！");

                // 加载Z0组输入位配置
                LoadZ0InputBits();
            }
            catch (Exception ex)
            {
                OnLogError(ex, "加载配置文件时发生错误", "运动控制卡");
                throw;
            }
        }

        /// <summary>
        /// 加载Z0组输入位配置
        /// </summary>
        private void LoadZ0InputBits()
        {
            try
            {
                _z0InputBits.Clear();
                string deviceConfigPath = "DeviceConfiguration.json";
                if (File.Exists(deviceConfigPath))
                {
                    string json = File.ReadAllText(deviceConfigPath);
                    dynamic deviceConfig = JsonConvert.DeserializeObject(json);

                    // 检查并添加安全传感器配置
                    if (deviceConfig?.sensors?.safety != null)
                    {
                        foreach (var sensor in deviceConfig.sensors.safety)
                        {
                            AddZ0InputIfApplicable(sensor.Value);
                        }
                    }

                    // 检查并添加位置传感器配置
                    if (deviceConfig?.sensors?.position != null)
                    {
                        foreach (var sensor in deviceConfig.sensors.position)
                        {
                            AddZ0InputIfApplicable(sensor.Value);
                        }
                    }

                    // 检查并添加报警配置
                    if (deviceConfig?.alarms?.servoAlarms != null)
                    {
                        foreach (var alarm in deviceConfig.alarms.servoAlarms)
                        {
                            AddZ0InputIfApplicable(alarm.Value);
                        }
                    }

                    // 检查并添加报警配置
                    if (deviceConfig?.alarms?.motionAlarms != null)
                    {
                        foreach (var alarm in deviceConfig.alarms.motionAlarms)
                        {
                            AddZ0InputIfApplicable(alarm.Value);
                        }
                    }

                    OnLogInformation(
                        $"已加载Z0组输入位: {string.Join(", ", _z0InputBits)}",
                        "运动控制卡"
                    );
                }
            }
            catch (Exception ex)
            {
                OnLogError(ex, "加载Z0组输入位配置时发生错误", "运动控制卡");
            }
        }

        /// <summary>
        /// 检查并添加Z0组输入位
        /// </summary>
        private void AddZ0InputIfApplicable(dynamic io)
        {
            try
            {
                if (io == null)
                    return;

                // 检查是否为Z0组
                string group = (string)io.group;
                int subGroup = (int)io.subGroup;
                int bitIndex = (int)io.bitIndex;
                string type = (string)io.type;

                if (
                    group == "Z"
                    && subGroup == 0
                    && (type == "NC" || type == "NO" || type == "Input")
                )
                {
                    if (!_z0InputBits.Contains(bitIndex))
                    {
                        _z0InputBits.Add(bitIndex);
                        OnLogInformation($"添加Z0.{bitIndex}为输入位，类型为{type}", "运动控制卡");
                    }
                }
            }
            catch (Exception ex)
            {
                OnLogError(ex, "添加Z0组输入位时发生错误", "运动控制卡");
            }
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        private void LogMessage(string type, string message)
        {
            switch (type)
            {
                case "错误":
                    OnLogError(null, message, "运动控制卡");
                    break;
                case "警告":
                    OnLogWarning(message, "运动控制卡");
                    break;
                case "信息":
                    OnLogInformation(message, "运动控制卡");
                    break;
                default:
                    OnLogInformation(message, "运动控制卡");
                    break;
            }
        }

        // 添加事件触发方法
        protected virtual void OnLogInformation(string message, string source)
        {
            LogMessageReceived?.Invoke(this, new LogEventArgs(message, source));
        }

        protected virtual void OnLogWarning(string message, string source)
        {
            LogWarningReceived?.Invoke(this, new LogEventArgs(message, source));
        }

        protected virtual void OnLogError(Exception ex, string message, string source)
        {
            LogErrorReceived?.Invoke(this, new LogEventArgs(message, source, ex));
        }

        /// <summary>
        /// 打开控制卡
        /// </summary>
        public void OpenControlCard()
        {
            lock (_lock)
            {
                if (_client != null)
                    throw new InvalidOperationException("控制卡已打开！");

                _client = new ModbusRtuClient();
                _client.BaudRate = _config.BaudRate;
                _client.Parity = System.IO.Ports.Parity.None;
                _client.Connect(_config.SerialPortName, ModbusEndianness.BigEndian);

                // 初始化Z0组输入端口
                InitializeZ0InputPorts();

                LogMessage("信息", "控制卡已成功打开！");
            }
        }

        /// <summary>
        /// 初始化Z0组输入端口，将输入位写为0
        /// </summary>
        private void InitializeZ0InputPorts()
        {
            try
            {
                if (_z0InputBits.Count == 0)
                {
                    LogMessage("信息", "未发现Z0组输入端口，无需初始化");
                    return;
                }

                // 读取当前Z0寄存器值
                ushort value = ReadIOZ0();
                ushort originalValue = value;

                // 将所有输入位置为0
                foreach (int bitIndex in _z0InputBits)
                {
                    value = ClearBit(value, bitIndex);
                }

                // 如果值有变化，写入新值
                if (value != originalValue)
                {
                    _client.WriteSingleRegister(
                        _config.SlaveAddress,
                        (ushort)_config.ControllerRegisters.AllIO_Z0,
                        value
                    );
                    LogMessage("信息", $"初始化Z0组输入端口完成，输入位已设置为0，写入值：{value}");
                }
                else
                {
                    LogMessage("信息", "Z0组输入端口已是正确状态，无需初始化");
                }
            }
            catch (Exception ex)
            {
                OnLogError(ex, "初始化Z0组输入端口时发生错误", "运动控制卡");
            }
        }

        /// <summary>
        /// 关闭控制卡
        /// </summary>
        public void CloseControlCard()
        {
            lock (_lock) // 确保线程安全
            {
                if (_client == null)
                    throw new InvalidOperationException("控制卡未打开！");

                _client.Dispose();
                _client = null;
                LogMessage("信息", "控制卡已成功关闭！");
            }
        }

        /// <summary>
        /// 设置目标位置（支持 32 位数值）
        /// </summary>
        public void SetTargetPosition(int axisId, double position)
        {
            lock (_lock) // 确保线程安全
            {
                var axis = FindAxis(axisId);
                Write32BitValue(
                    _config.SlaveAddress,
                    axis.TargetPositionRegister,
                    (int)(position / axis.PulseEquivalent)
                );
                LogMessage("信息", $"轴 {axisId} 的目标位置已设置为: {position}");
            }
        }

        /// <summary>
        /// 读取目标位置（支持 32 位数值）
        /// </summary>
        public double GetTargetPosition(int axisId)
        {
            lock (_lock) // 确保线程安全
            {
                var axis = FindAxis(axisId);
                return Read32BitValue(_config.SlaveAddress, axis.TargetPositionRegister)
                    * axis.PulseEquivalent;
            }
        }

        /// <summary>
        /// 获取当前位置
        /// </summary>
        /// <param name="axisId"></param>
        /// <returns></returns>
        public double GetCurrentPosition(int axisId)
        {
            lock (_lock) // 确保线程安全
            {
                var axis = FindAxis(axisId);
                return Read32BitValue(_config.SlaveAddress, axis.CurrentPositionRegister)
                    * axis.PulseEquivalent;
            }
        }

        /// <summary>
        /// 设置最大行程（支持 32 位数值）
        /// </summary>
        public void SetMaxRange(int axisId, int range)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                Write32BitValue(
                    _config.SlaveAddress,
                    axis.MaxRangeRegister,
                    (int)(range / axis.PulseEquivalent)
                );
                LogMessage("信息", $"轴 {axisId} 的最大行程已设置为: {range}");
            }
        }

        /// <summary>
        /// 读取最大行程（支持 32 位数值）
        /// </summary>
        public int GetMaxRange(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                return Read32BitValue(_config.SlaveAddress, axis.MaxRangeRegister);
            }
        }

        /// <summary>
        /// 设置原点行程 支持16位整形
        /// </summary>
        /// <param name="axisId"></param>
        /// <param name="range"></param>
        public void SetHomeRange(int axisId, int range)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                Write32BitValue(
                    _config.SlaveAddress,
                    axis.HomeRangeRegister,
                    (int)(range / axis.PulseEquivalent)
                );
                LogMessage("信息", $"轴 {axisId} 的原点行程已设置为: {range}");
            }
        }

        public int GetHomeRange(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                return Read32BitValue(_config.SlaveAddress, axis.HomeRangeRegister);
            }
        }

        /// <summary>
        /// 设置轴加速时间（支持 16 位数值） 单位: 毫秒
        /// </summary>
        /// <param name="axisId"></param>
        /// <param name="accelerationTime"></param>
        public void SetAccelerationTime(int axisId, int accelerationTime)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                Write32BitValue(
                    _config.SlaveAddress,
                    axis.AccelerationTimeRegister,
                    accelerationTime
                );
                LogMessage("信息", $"设置轴{axisId}的加速度为{accelerationTime}");
            }
        }

        /// <summary>
        /// 获取轴的加速时间  单位：ms
        /// </summary>
        /// <param name="axisId"></param>
        /// <returns></returns>
        public int GetAccelerationTime(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                var accelerationTime = Read32BitValue(
                    _config.SlaveAddress,
                    axis.AccelerationTimeRegister
                );
                LogMessage("信息", $"轴{axisId}的加速度为{accelerationTime}");
                return accelerationTime;
            }
        }

        /// <summary>
        /// 清除总报警
        /// </summary>
        public void ClearTotalException()
        {
            lock (_lock)
            {
                _client.WriteSingleRegister(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.ResetTotalException,
                    1
                );
                LogMessage("信息", "总报警已清除！");
            }
        }

        /// <summary>
        /// 读取总报警状态
        /// </summary>
        public ushort GetTotalException()
        {
            lock (_lock)
            {
                return _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.TotalException,
                    1
                )[0];
            }
        }

        /// <summary>
        /// 检查指定轴是否有报警
        /// </summary>
        public bool CheckAxisException(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                ushort exceptionValue = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)axis.ExceptionRegister.Addr,
                    1
                )[0];
                return exceptionValue != 0; // 如果寄存器值不为 0，则存在报警
            }
        }

        /// <summary>
        /// 设置某一位输出
        /// </summary>
        public void SetOutputBit(int address, int bitIndex, bool state)
        {
            lock (_lock)
            {
                // 检查是否为Z0组地址，并且是否为输入位
                if (
                    address == _config.ControllerRegisters.AllIO_Z0
                    && _z0InputBits.Contains(bitIndex)
                )
                {
                    LogMessage("警告", $"Z0.{bitIndex}是输入位，不能直接设置其状态！");
                    return;
                }

                ushort currentValue = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)address,
                    1
                )[0];
                ushort newValue = state
                    ? SetBit(currentValue, bitIndex)
                    : ClearBit(currentValue, bitIndex);

                // 如果是Z0组地址，确保所有输入位保持为0
                if (address == _config.ControllerRegisters.AllIO_Z0)
                {
                    foreach (int inputBitIndex in _z0InputBits)
                    {
                        newValue = ClearBit(newValue, inputBitIndex);
                    }
                }

                _client.WriteSingleRegister(_config.SlaveAddress, (ushort)address, newValue);
                LogMessage(
                    "信息",
                    $"输出地址 {address} 位 {bitIndex} 已设置为：{(state ? "ON" : "OFF")}"
                );
            }
        }

        /// <summary>
        /// 控制某个轴JOG前进
        /// </summary>
        public void MoveAxisJogForward(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                int commandAddress = axis.CommandsRegister.Addr;
                var forwardBit = axis.CommandsRegister.Bits.Find(b => b.Name == "MoveForward");
                var backwardBit = axis.CommandsRegister.Bits.Find(b => b.Name == "MoveBackward");

                if (forwardBit == null || backwardBit == null)
                {
                    LogMessage("错误", "命令寄存器中未配置前进或后退位！");
                    throw new Exception("命令寄存器中未配置前进或后退位！");
                }

                ushort currentValue = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)commandAddress,
                    1
                )[0];
                ushort newValue = SetBit(currentValue, forwardBit.BitIndex);
                newValue = ClearBit(newValue, backwardBit.BitIndex);

                _client.WriteSingleRegister(_config.SlaveAddress, (ushort)commandAddress, newValue);
                LogMessage("信息", $"[Modbus]: 往地址：{commandAddress}成功写入：{newValue}");
                LogMessage("信息", $"轴 {axisId} 已开始前进！");
            }
        }

        /// <summary>
        /// 控制某个轴JOG后退
        /// </summary>
        public void MoveAxisJogBackward(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                int commandAddress = axis.CommandsRegister.Addr;
                var forwardBit = axis.CommandsRegister.Bits.Find(b => b.Name == "MoveForward");
                var backwardBit = axis.CommandsRegister.Bits.Find(b => b.Name == "MoveBackward");

                if (forwardBit == null || backwardBit == null)
                {
                    throw new Exception("命令寄存器中未配置前进或后退位！");
                }

                ushort currentValue = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)commandAddress,
                    1
                )[0];
                ushort newValue = SetBit(currentValue, backwardBit.BitIndex); // 设置后退位
                newValue = ClearBit(newValue, forwardBit.BitIndex); // 确保前进位清零

                _client.WriteSingleRegister(_config.SlaveAddress, (ushort)commandAddress, newValue);
                LogMessage("信息", $"[Modbus]: 往地址：{commandAddress}成功写入：{newValue}");
                LogMessage("信息", $"轴 {axisId} 已开始后退！");
            }
        }

        /// <summary>
        /// 停止某个轴的JOG运动
        /// </summary>
        public void StopAxisJog(int axisId)
        {
            lock (_lock)
            {
                try
                {
                    var axis = FindAxis(axisId);
                    int commandAddress = axis.CommandsRegister.Addr;
                    var forwardBit = axis.CommandsRegister.Bits.Find(b => b.Name == "MoveForward");
                    var backwardBit = axis.CommandsRegister.Bits.Find(b =>
                        b.Name == "MoveBackward"
                    );
                    var emergencyStopBit = axis.CommandsRegister.Bits.Find(b =>
                        b.Name == "EmergencyStop"
                    );

                    if (forwardBit == null || backwardBit == null)
                    {
                        throw new Exception("命令寄存器中未配置前进或后退位！");
                    }

                    // 检查当前是否在移动中
                    ushort currentValue = _client.ReadHoldingRegisters<ushort>(
                        _config.SlaveAddress,
                        (ushort)commandAddress,
                        1
                    )[0];

                    bool isMoving =
                        ((currentValue & (1 << forwardBit.BitIndex)) != 0)
                        || ((currentValue & (1 << backwardBit.BitIndex)) != 0);

                    // 清除所有运动命令位
                    ushort newValue = ClearBit(currentValue, forwardBit.BitIndex); // 清除前进位
                    newValue = ClearBit(newValue, backwardBit.BitIndex); // 清除后退位

                    // 立即发送停止命令
                    _client.WriteSingleRegister(
                        _config.SlaveAddress,
                        (ushort)commandAddress,
                        newValue
                    );

                    // 如果轴正在移动，使用急停方式确保立即停止
                    if (isMoving && emergencyStopBit != null)
                    {
                        // 读取最新状态
                        currentValue = _client.ReadHoldingRegisters<ushort>(
                            _config.SlaveAddress,
                            (ushort)commandAddress,
                            1
                        )[0];

                        // 设置急停位
                        newValue = SetBit(currentValue, emergencyStopBit.BitIndex);
                        _client.WriteSingleRegister(
                            _config.SlaveAddress,
                            (ushort)commandAddress,
                            newValue
                        );

                        LogMessage("信息", $"轴 {axisId} 使用紧急停止!");

                        // 短暂延迟后清除急停位
                        Task.Delay(50)
                            .ContinueWith(_ =>
                            {
                                try
                                {
                                    lock (_lock)
                                    {
                                        currentValue = _client.ReadHoldingRegisters<ushort>(
                                            _config.SlaveAddress,
                                            (ushort)commandAddress,
                                            1
                                        )[0];
                                        newValue = ClearBit(
                                            currentValue,
                                            emergencyStopBit.BitIndex
                                        );
                                        _client.WriteSingleRegister(
                                            _config.SlaveAddress,
                                            (ushort)commandAddress,
                                            newValue
                                        );
                                        LogMessage("信息", $"轴 {axisId} 清除紧急停止状态");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LogMessage(
                                        "错误",
                                        $"清除轴 {axisId} 紧急停止状态失败: {ex.Message}"
                                    );
                                }
                            });
                    }

                    LogMessage("信息", $"轴 {axisId} 已停止！");
                }
                catch (Exception ex)
                {
                    LogMessage("错误", $"停止轴 {axisId} 失败: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 设置紧急停止位
        /// </summary>
        /// <param name="axisId">急停指定的轴</param>
        /// <exception cref="Exception"></exception>
        public void SetEmergencyStop(int axisId = 0)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                int commandAddress = axis.CommandsRegister.Addr;
                var emergencyStopBit = axis.CommandsRegister.Bits.Find(b =>
                    b.Name == "EmergencyStop"
                );
                if (emergencyStopBit == null)
                {
                    throw new Exception("命令寄存器中未配置紧急停止位！");
                }
                ushort currentValue = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)commandAddress,
                    1
                )[0];
                ushort newValue = SetBit(currentValue, emergencyStopBit.BitIndex);
                _client.WriteSingleRegister(_config.SlaveAddress, (ushort)commandAddress, newValue);

                LogMessage("信息", $"轴 {axisId} 已设置紧急停止！");
            }
        }

        /// <summary>
        /// 急停所有轴
        /// </summary>
        public void EmergencyStopAllAxis()
        {
            lock (_lock)
            {
                foreach (var axis in _config.Axes)
                {
                    SetEmergencyStop(axis.AxisNo);
                }
            }
        }

        /// <summary>
        /// 设置轴的启动速度
        /// </summary>
        /// <param name="axisId">轴编号</param>
        /// <param name="startSpeed">启动速度</param>
        public void SetStartSpeed(int axisId, int startSpeed)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                _client.WriteSingleRegister(
                    _config.SlaveAddress,
                    (ushort)axis.StartSpeedRegister,
                    (ushort)(startSpeed / axis.PulseEquivalent)
                );
                LogMessage("信息", $"轴 {axisId} 的启动速度已设置为: {startSpeed}");
            }
        }

        /// <summary>
        /// 设置轴的运行速度（最大速度）
        /// </summary>
        /// <param name="axisId">轴编号</param>
        /// <param name="maxSpeed">运行速度（最大速度）</param>
        public void SetMaxSpeed(int axisId, int maxSpeed)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                _client.WriteSingleRegister(
                    _config.SlaveAddress,
                    (ushort)axis.MaxSpeedRegister,
                    (ushort)(maxSpeed / axis.PulseEquivalent)
                );
                LogMessage("信息", $"轴 {axisId} 的运行速度（最大速度）已设置为: {maxSpeed}");
            }
        }

        /// <summary>
        /// 获取所有轴的配置信息
        /// </summary>
        /// <returns></returns>
        public List<Models.AxisConfig> GetAllAxesConfig()
        {
            List<Models.AxisConfig> axisConfigs = new List<Models.AxisConfig>();
            for (int i = 01; i < 5; i++)
            {
                try
                {
                    var axis = _config.Axes.Find(a => a.AxisNo == i);
                    if (axis != null)
                    {
                        axisConfigs.Add(axis);
                    }
                }
                catch (Exception ex)
                {
                    LogMessage("错误", $"轴 {i} 的配置信息获取失败：{ex.Message}");
                }
            }
            return axisConfigs;
        }

        // 辅助方法：读取 32 位值（双寄存器）
        private int Read32BitValue(int unitIdentifier, int startingAddress)
        {
            Span<ushort> registers = _client.ReadHoldingRegisters<ushort>(
                unitIdentifier,
                startingAddress,
                2
            );
            return registers[0] | (registers[1] << 16); // 合并两个 16 位寄存器为 32 位整数
        }

        // 辅助方法：写入 32 位值（双寄存器）
        private void Write32BitValue(int unitIdentifier, int startingAddress, int value)
        {
            ushort lowWord = (ushort)(value & 0xFFFF);
            ushort highWord = (ushort)((value >> 16) & 0xFFFF);

            _client.WriteMultipleRegisters(
                unitIdentifier,
                startingAddress,
                new ushort[] { lowWord, highWord }
            );
        }

        // 辅助方法：查找轴配置
        private Models.AxisConfig FindAxis(int axisId)
        {
            var axis = _config.Axes.Find(a => a.AxisNo == axisId);
            if (axis == null)
                throw new ArgumentException($"未找到轴编号为 {axisId} 的配置！");
            return axis;
        }

        // 辅助方法 - 设置指定位
        private ushort SetBit(ushort value, int bitIndex)
        {
            return (ushort)(value | (1 << bitIndex));
        }

        // 辅助方法 - 清除指定位
        private ushort ClearBit(ushort value, int bitIndex)
        {
            return (ushort)(value & ~(1 << bitIndex));
        }

        /// <summary>
        /// 控制某个轴回原点（复位）
        /// </summary>
        /// <param name="axisId">轴编号</param>
        public void HomeAxis(int axisId)
        {
            lock (_lock)
            {
                var axis = FindAxis(axisId);
                int commandAddress = axis.CommandsRegister.Addr;
                var homeBit = axis.CommandsRegister.Bits.Find(b => b.Name == "Home");

                if (homeBit == null)
                {
                    throw new Exception("命令寄存器中未配置回原点位！");
                }

                SetStartSpeed(axis.AxisNo, (int)(axis.HomeSpeed / 2));
                SetMaxSpeed(axis.AxisNo, (int)(axis.HomeSpeed));

                ushort currentValue = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)commandAddress,
                    1
                )[0];
                ushort newValue = SetBit(currentValue, homeBit.BitIndex); // 设置回原点位

                _client.WriteSingleRegister(_config.SlaveAddress, (ushort)commandAddress, newValue);
                LogMessage("信息", $"[Modbus]: 往地址：{commandAddress}成功写入：{newValue}");
                LogMessage("信息", $"轴 {axisId} 已开始回原点！");
            }
        }

        /// <summary>
        /// 读取X0组输入寄存器的值
        /// </summary>
        public ushort ReadInputsX0()
        {
            lock (_lock)
            {
                ushort value = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllInputs_X0,
                    1
                )[0];

                // 更新缓存
                _ioCache[0] = value;
                _ioCacheTimestamp[0] = DateTime.Now;

                return value;
            }
        }

        /// <summary>
        /// 读取X1组输入寄存器的值
        /// </summary>
        public ushort ReadInputsX1()
        {
            lock (_lock)
            {
                ushort value = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllInputs_X1,
                    1
                )[0];

                // 更新缓存
                _ioCache[1] = value;
                _ioCacheTimestamp[1] = DateTime.Now;

                return value;
            }
        }

        /// <summary>
        /// 读取输出寄存器Y0的值
        /// </summary>
        public ushort ReadOutputsY0()
        {
            lock (_lock)
            {
                return _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllOutputs_Y0,
                    1
                )[0];
            }
        }

        /// <summary>
        /// 读取输出寄存器Y1的值
        /// </summary>
        public ushort ReadOutputsY1()
        {
            lock (_lock)
            {
                return _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllOutputs_Y1,
                    1
                )[0];
            }
        }

        /// <summary>
        /// 设置输出寄存器Y0的值
        /// </summary>
        public void WriteOutputsY0(ushort value)
        {
            lock (_lock)
            {
                _client.WriteSingleRegister(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllOutputs_Y0,
                    value
                );
                LogMessage("信息", $"输出寄存器Y0已设置为：{value}");
            }
        }

        /// <summary>
        /// 设置输出寄存器Y1的值
        /// </summary>
        public void WriteOutputsY1(ushort value)
        {
            lock (_lock)
            {
                _client.WriteSingleRegister(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllOutputs_Y1,
                    value
                );
                LogMessage("信息", $"输出寄存器Y1已设置为：{value}");
            }
        }

        /// <summary>
        /// 获取输入位状态(新版本，带缓存)
        /// </summary>
        public bool GetInputBitStatus(int registerIndex, int bitIndex)
        {
            int address;

            // 确定寄存器地址
            if (registerIndex == 0)
                address = _config.ControllerRegisters.AllInputs_X0;
            else if (registerIndex == 1)
                address = _config.ControllerRegisters.AllInputs_X1;
            else
                throw new ArgumentException($"无效的输入寄存器索引：{registerIndex}");

            // 检查缓存
            if (
                _ioCache.TryGetValue(registerIndex, out ushort cachedValue)
                && _ioCacheTimestamp.TryGetValue(registerIndex, out DateTime cacheTimestamp)
                && (DateTime.Now - cacheTimestamp).TotalMilliseconds < IO_CACHE_LIFETIME_MS
            )
            {
                // 缓存有效，直接返回
                return (cachedValue & (1 << bitIndex)) != 0;
            }

            // 缓存无效或不存在，读取最新值
            ushort value;
            if (registerIndex == 0)
                value = ReadInputsX0(); // 这个方法会更新缓存
            else
                value = ReadInputsX1(); // 这个方法会更新缓存

            return (value & (1 << bitIndex)) != 0;
        }

        /// <summary>
        /// 获取输出位状态
        /// </summary>
        public bool GetOutputBit(int address, int bitIndex)
        {
            lock (_lock)
            {
                ushort value = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)address,
                    1
                )[0];
                return (value & (1 << bitIndex)) != 0;
            }
        }

        /// <summary>
        /// 设置输出位的状态
        /// </summary>
        public void SetOutputBitStatus(int registerIndex, int bitIndex, bool status)
        {
            ushort value;
            if (registerIndex == 0)
            {
                value = ReadOutputsY0();
            }
            else if (registerIndex == 1)
            {
                value = ReadOutputsY1();
            }
            else
            {
                throw new ArgumentException($"无效的输出寄存器索引：{registerIndex}");
            }

            if (status)
            {
                value = SetBit(value, bitIndex);
            }
            else
            {
                value = ClearBit(value, bitIndex);
            }

            if (registerIndex == 0)
            {
                WriteOutputsY0(value);
            }
            else
            {
                WriteOutputsY1(value);
            }
        }

        /// <summary>
        /// 根据IO定义设置输出状态
        /// </summary>
        public void SetIoStatus(IoConfig ioConfig, bool status)
        {
            if (ioConfig == null)
                throw new ArgumentNullException(nameof(ioConfig));

            // 使用 IoAddress 和 IoBitIndex 直接访问
            if (ioConfig.IoAddress >= 0)
            {
                SetOutputBit(ioConfig.IoAddress, ioConfig.IoBitIndex, status);
            }
            else
            {
                // 对于Z组IO的特殊处理
                SetZ0BitStatus(ioConfig.IoBitIndex, status);
            }
        }

        /// <summary>
        /// 根据IO定义获取输入状态
        /// </summary>
        public bool GetIoStatus(IoConfig ioConfig)
        {
            if (ioConfig == null)
                throw new ArgumentNullException(nameof(ioConfig));

            // 使用 IoAddress 和 IoBitIndex 直接访问
            if (ioConfig.IoAddress >= 0)
            {
                return GetInputBit(ioConfig.IoAddress, ioConfig.IoBitIndex);
            }
            else
            {
                // 对于Z组IO的特殊处理
                return GetZ0BitStatus(ioConfig.IoBitIndex);
            }
        }

        /// <summary>
        /// 发送脉冲信号
        /// </summary>
        public async Task SendPulseAsync(IoConfig ioConfig)
        {
            if (ioConfig == null)
                throw new ArgumentNullException(nameof(ioConfig));

            var delay = 100; // 默认延迟时间（毫秒）
            if (ioConfig.PulseWidth > 0)
            {
                delay = ioConfig.PulseWidth;
            }

            SetIoStatus(ioConfig, true);
            await Task.Delay(delay);
            SetIoStatus(ioConfig, false);
        }

        #region SMEMA相关IO控制
        /// <summary>
        /// 设置本机准备好信号（给上游）
        /// </summary>
        public void SetMachineReady(bool state)
        {
            SetOutputBitStatus(0, 3, state); // Y0.3
        }

        /// <summary>
        /// 设置本机板卡就绪信号（给下游）
        /// </summary>
        public void SetBoardAvailable(bool state)
        {
            SetOutputBitStatus(0, 4, state); // Y0.4
        }

        /// <summary>
        /// 获取上游板卡就绪信号
        /// </summary>
        public bool GetUpstreamBoardAvailable()
        {
            return GetInputBitStatus(0, 0); // X0.0
        }

        /// <summary>
        /// 获取下游准备好信号
        /// </summary>
        public bool GetDownstreamMachineReady()
        {
            return GetInputBitStatus(0, 1); // X0.1
        }
        #endregion

        /// <summary>
        /// 读取Z0组IO寄存器的值
        /// </summary>
        public ushort ReadIOZ0()
        {
            lock (_lock)
            {
                return _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllIO_Z0,
                    1
                )[0];
            }
        }

        /// <summary>
        /// 写入Z0组IO寄存器的值
        /// </summary>
        public void WriteIOZ0(ushort value)
        {
            lock (_lock)
            {
                // 先读取当前值，保留输入位的状态
                ushort currentValue = ReadIOZ0();

                // 对于所有标记为输入的位，使用掩码保留其当前值（设为0）
                foreach (int bitIndex in _z0InputBits)
                {
                    // 清除输入位
                    value = ClearBit(value, bitIndex);

                    // 对于特殊位在实际硬件中设为0，以避免干扰输入
                    // 这里不需要从当前值中读取输入位状态，因为已经清除了该位
                }

                _client.WriteSingleRegister(
                    _config.SlaveAddress,
                    (ushort)_config.ControllerRegisters.AllIO_Z0,
                    value
                );
                LogMessage("信息", $"Z0组IO寄存器已设置为：{value}，输入位已保留");
            }
        }

        /// <summary>
        /// 获取Z0组IO位的状态
        /// </summary>
        public bool GetZ0BitStatus(int bitIndex)
        {
            // 读取Z0IO寄存器的值，然后检查指定位
            ushort value = ReadIOZ0();
            return (value & (1 << bitIndex)) != 0;
        }

        /// <summary>
        /// 设置Z0组IO位的状态
        /// </summary>
        public void SetZ0BitStatus(int bitIndex, bool status)
        {
            ushort value = ReadIOZ0();

            // 检查是否为输入位
            if (_z0InputBits.Contains(bitIndex))
            {
                LogMessage("警告", $"Z0.{bitIndex}是输入位，不能直接设置其状态！");
                return;
            }

            if (status)
            {
                value = SetBit(value, bitIndex);
            }
            else
            {
                value = ClearBit(value, bitIndex);
            }
            WriteIOZ0(value);
        }

        /// <summary>
        /// 获取输入位状态(低级方法，直接读取，不使用缓存)
        /// </summary>
        public bool GetInputBit(int address, int bitIndex)
        {
            lock (_lock)
            {
                ushort value = _client.ReadHoldingRegisters<ushort>(
                    _config.SlaveAddress,
                    (ushort)address,
                    1
                )[0];
                return (value & (1 << bitIndex)) != 0;
            }
        }

        /// <summary>
        /// 获取输出位状态
        /// </summary>
        public bool GetOutputBitStatus(int registerIndex, int bitIndex)
        {
            return GetOutputBit(
                registerIndex == 0
                    ? _config.ControllerRegisters.AllOutputs_Y0
                    : _config.ControllerRegisters.AllOutputs_Y1,
                bitIndex
            );
        }
    }
}
