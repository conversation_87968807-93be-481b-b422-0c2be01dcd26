using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HandyControl.Controls;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.Alarms;

namespace Nickel_Inspect.Models
{
    /// <summary>
    /// 指示灯类型常量
    /// </summary>
    public static class IndicatorLightType
    {
        public const string Red = "red";
        public const string Green = "green";
        public const string Yellow = "yellow";
    }

    /// <summary>
    /// 设备状态枚举
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>
        /// 正常运行
        /// </summary>
        Running,

        /// <summary>
        /// 待机
        /// </summary>
        Standby,

        /// <summary>
        /// 暂停
        /// </summary>
        Paused,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 紧急停止
        /// </summary>
        EmergencyStop,

        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,
    }

    /// <summary>
    /// 设备状态管理器 - 管理设备整体状态和指示灯
    /// </summary>
    public class StatusManager : IDisposable
    {
        private readonly ILogService _logService;
        private readonly MachineConfigurationHelper _configHelper;
        private readonly BkMotionCard _motionCard;
        private DeviceStatus _currentStatus = DeviceStatus.Unknown;
        private AlarmManager _alarmManager;

        // 报警相关属性
        private bool _hasActiveAlarms = false;
        private bool _hasMotionAlarms = false;
        private bool _hasServoAlarms = false;
        private List<AlarmInfo> _activeAlarms = new List<AlarmInfo>();
        private readonly object _alarmLock = new object();

        private readonly Dictionary<string, CancellationTokenSource> _blinkingTasks =
            new Dictionary<string, CancellationTokenSource>();

        // 缓存上一次设置的指示灯状态
        private bool _lastRedLightState = false;
        private bool _lastGreenLightState = false;
        private bool _lastYellowLightState = false;
        private bool _lastBuzzerState = false;

        public event EventHandler<DeviceStatus> StatusChanged;
        public event EventHandler<List<AlarmInfo>> AlarmsChanged;

        /// <summary>
        /// 当前设备状态
        /// </summary>
        public DeviceStatus CurrentStatus
        {
            get => _currentStatus;
            private set
            {
                if (_currentStatus != value)
                {
                    var oldStatus = _currentStatus;
                    _currentStatus = value;
                    OnStatusChanged(oldStatus, _currentStatus);
                }
            }
        }

        /// <summary>
        /// 是否存在活动报警
        /// </summary>
        public bool HasActiveAlarms => _hasActiveAlarms;

        /// <summary>
        /// 是否存在运动报警
        /// </summary>
        public bool HasMotionAlarms => _hasMotionAlarms;

        /// <summary>
        /// 是否存在伺服报警
        /// </summary>
        public bool HasServoAlarms => _hasServoAlarms;

        /// <summary>
        /// 当前活动的报警列表
        /// </summary>
        public IReadOnlyList<AlarmInfo> ActiveAlarms => _activeAlarms.AsReadOnly();

        public StatusManager(
            ILogService logService,
            MachineConfigurationHelper configHelper,
            BkMotionCard motionCard
        )
        {
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _configHelper = configHelper ?? throw new ArgumentNullException(nameof(configHelper));
            _motionCard = motionCard ?? throw new ArgumentNullException(nameof(motionCard));

            try
            {
                _motionCard.OpenControlCard();
                foreach (var axis in _configHelper.GetAllAxes())
                {
                    _motionCard.SetHomeRange(axis.AxisNo, (int)axis.HomeMaxDistance);
                    _motionCard.SetMaxRange(axis.AxisNo, (int)axis.MaxDistance);
                    _motionCard.SetAccelerationTime(axis.AxisNo, (int)axis.AccelerationTime);
                }
            }
            catch (Exception)
            {
                MessageBox.Show("控制器连接失败, 请确认接线正确后重启软件");
                return;
            }

            // 尝试获取当前指示灯状态并初始化缓存
            try
            {
                InitializeIndicatorStates();
            }
            catch (Exception ex)
            {
                _logService.LogWarning($"初始化指示灯状态失败: {ex.Message}", "状态管理器");
            }

            _logService.LogInformation("状态管理服务已初始化", "状态管理器");
        }

        /// <summary>
        /// 初始化并获取当前指示灯状态
        /// </summary>
        private void InitializeIndicatorStates()
        {
            var indicatorConfig = _configHelper.GetIndicatorConfig();
            if (indicatorConfig != null)
            {
                // 尝试读取当前指示灯状态
                if (indicatorConfig.Lights?.RedLight != null)
                {
                    _lastRedLightState = _motionCard.GetOutputBitStatus(
                        indicatorConfig.Lights.RedLight.IoAddress,
                        indicatorConfig.Lights.RedLight.IoBitIndex
                    );
                }

                if (indicatorConfig.Lights?.GreenLight != null)
                {
                    _lastGreenLightState = _motionCard.GetOutputBitStatus(
                        indicatorConfig.Lights.GreenLight.IoAddress,
                        indicatorConfig.Lights.GreenLight.IoBitIndex
                    );
                }

                if (indicatorConfig.Lights?.YellowLight != null)
                {
                    _lastYellowLightState = _motionCard.GetOutputBitStatus(
                        indicatorConfig.Lights.YellowLight.IoAddress,
                        indicatorConfig.Lights.YellowLight.IoBitIndex
                    );
                }

                if (indicatorConfig.Buzzer != null)
                {
                    _lastBuzzerState = _motionCard.GetOutputBitStatus(
                        indicatorConfig.Buzzer.IoAddress,
                        indicatorConfig.Buzzer.IoBitIndex
                    );
                }

                _logService.LogDebug(
                    $"初始化指示灯状态: 红={_lastRedLightState}, 绿={_lastGreenLightState}, 黄={_lastYellowLightState}, 蜂鸣器={_lastBuzzerState}",
                    "状态管理器"
                );
            }
        }

        #region 基础指示灯控制

        /// <summary>
        /// 设置红色指示灯状态
        /// </summary>
        /// <param name="isOn">是否开启</param>
        public async Task SetRedLight(bool isOn)
        {
            try
            {
                // 如果状态没变化，不进行实际操作
                if (_lastRedLightState == isOn)
                {
                    _logService.LogDebug($"红色指示灯状态未变化，跳过设置", "状态管理器");
                    return;
                }

                var config = _configHelper.GetIndicatorConfig()?.Lights?.RedLight;
                if (config == null)
                {
                    _logService.LogWarning("红色指示灯配置不存在", "状态管理器");
                    return;
                }

                _motionCard.SetIoStatus(config, isOn);
                _logService.LogDebug(
                    $"红色指示灯状态设置为: {(isOn ? "开启" : "关闭")}",
                    "状态管理器"
                );

                // 更新缓存的状态
                _lastRedLightState = isOn;
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "设置红色指示灯状态失败", "状态管理器");
                throw;
            }
        }

        /// <summary>
        /// 设置绿色指示灯状态
        /// </summary>
        /// <param name="isOn">是否开启</param>
        public async Task SetGreenLight(bool isOn)
        {
            try
            {
                // 如果状态没变化，不进行实际操作
                if (_lastGreenLightState == isOn)
                {
                    _logService.LogDebug($"绿色指示灯状态未变化，跳过设置", "状态管理器");
                    return;
                }

                var config = _configHelper.GetIndicatorConfig()?.Lights?.GreenLight;
                if (config == null)
                {
                    _logService.LogWarning("绿色指示灯配置不存在", "状态管理器");
                    return;
                }

                _motionCard.SetIoStatus(config, isOn);
                _logService.LogDebug(
                    $"绿色指示灯状态设置为: {(isOn ? "开启" : "关闭")}",
                    "状态管理器"
                );

                // 更新缓存的状态
                _lastGreenLightState = isOn;
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "设置绿色指示灯状态失败", "状态管理器");
                throw;
            }
        }

        /// <summary>
        /// 设置黄色指示灯状态
        /// </summary>
        /// <param name="isOn">是否开启</param>
        public async Task SetYellowLight(bool isOn)
        {
            try
            {
                // 如果状态没变化，不进行实际操作
                if (_lastYellowLightState == isOn)
                {
                    _logService.LogDebug($"黄色指示灯状态未变化，跳过设置", "状态管理器");
                    return;
                }

                var config = _configHelper.GetIndicatorConfig()?.Lights?.YellowLight;
                if (config == null)
                {
                    _logService.LogWarning("黄色指示灯配置不存在", "状态管理器");
                    return;
                }

                _motionCard.SetIoStatus(config, isOn);
                _logService.LogDebug(
                    $"黄色指示灯状态设置为: {(isOn ? "开启" : "关闭")}",
                    "状态管理器"
                );

                // 更新缓存的状态
                _lastYellowLightState = isOn;
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "设置黄色指示灯状态失败", "状态管理器");
                throw;
            }
        }

        /// <summary>
        /// 设置蜂鸣器状态
        /// </summary>
        /// <param name="isOn">是否开启</param>
        public async Task SetBuzzer(bool isOn)
        {
            try
            {
                // 如果状态没变化，不进行实际操作
                if (_lastBuzzerState == isOn)
                {
                    _logService.LogDebug($"蜂鸣器状态未变化，跳过设置", "状态管理器");
                    return;
                }

                var config = _configHelper.GetIndicatorConfig()?.Buzzer;
                if (config == null)
                {
                    _logService.LogWarning("蜂鸣器配置不存在", "状态管理器");
                    return;
                }

                _motionCard.SetIoStatus(config, isOn);
                _logService.LogDebug($"蜂鸣器状态设置为: {(isOn ? "开启" : "关闭")}", "状态管理器");

                // 更新缓存的状态
                _lastBuzzerState = isOn;
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "设置蜂鸣器状态失败", "状态管理器");
                throw;
            }
        }

        /// <summary>
        /// 蜂鸣器响一次（自动关闭）
        /// </summary>
        /// <param name="durationMs">持续时间(毫秒)</param>
        public async Task BeepOnce(int durationMs = 500)
        {
            await SetBuzzer(true);
            await Task.Delay(durationMs);
            await SetBuzzer(false);
        }

        /// <summary>
        /// 启动指示灯闪烁
        /// </summary>
        /// <param name="light">灯类型: "red", "green", "yellow"</param>
        /// <param name="intervalMs">闪烁间隔(毫秒)</param>
        public async Task StartBlinking(string light, int intervalMs = 500)
        {
            if (string.IsNullOrWhiteSpace(light))
            {
                throw new ArgumentException("灯类型不能为空", nameof(light));
            }

            // 先停止该灯当前的闪烁任务
            await StopBlinking(light, false);

            var cts = new CancellationTokenSource();
            _blinkingTasks[light] = cts;

            // 启动闪烁任务
            _ = Task.Run(
                async () =>
                {
                    bool state = false;
                    try
                    {
                        while (!cts.Token.IsCancellationRequested)
                        {
                            state = !state;
                            switch (light.ToLower())
                            {
                                case IndicatorLightType.Red:
                                    await SetRedLight(state);
                                    break;
                                case IndicatorLightType.Green:
                                    await SetGreenLight(state);
                                    break;
                                case IndicatorLightType.Yellow:
                                    await SetYellowLight(state);
                                    break;
                                default:
                                    _logService.LogWarning($"未知的灯类型: {light}", "状态管理器");
                                    return;
                            }

                            await Task.Delay(intervalMs, cts.Token);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，忽略
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError(ex, $"闪烁任务异常: {light}", "状态管理器");
                    }
                },
                cts.Token
            );

            _logService.LogInformation($"开始{light}灯闪烁，间隔: {intervalMs}ms", "状态管理器");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 停止指示灯闪烁
        /// </summary>
        /// <param name="light">灯类型: "red", "green", "yellow"</param>
        /// <param name="turnOff">是否关闭该灯</param>
        public async Task StopBlinking(string light, bool turnOff = true)
        {
            if (string.IsNullOrWhiteSpace(light))
            {
                throw new ArgumentException("灯类型不能为空", nameof(light));
            }

            if (_blinkingTasks.TryGetValue(light, out var cts))
            {
                cts.Cancel();
                cts.Dispose();
                _blinkingTasks.Remove(light);

                // 根据需要关闭灯
                if (turnOff)
                {
                    switch (light.ToLower())
                    {
                        case IndicatorLightType.Red:
                            await SetRedLight(false);
                            break;
                        case IndicatorLightType.Green:
                            await SetGreenLight(false);
                            break;
                        case IndicatorLightType.Yellow:
                            await SetYellowLight(false);
                            break;
                    }
                }

                _logService.LogInformation($"停止{light}灯闪烁", "状态管理器");
            }
        }

        /// <summary>
        /// 停止所有指示灯闪烁
        /// </summary>
        public async Task StopAllBlinking()
        {
            foreach (
                var light in new[]
                {
                    IndicatorLightType.Red,
                    IndicatorLightType.Green,
                    IndicatorLightType.Yellow,
                }
            )
            {
                await StopBlinking(light);
            }
        }

        #endregion

        #region 状态管理

        /// <summary>
        /// 设置设备状态
        /// </summary>
        /// <param name="status">目标状态</param>
        public async Task SetStatusAsync(DeviceStatus status)
        {
            // 如果状态没有变化，直接返回
            if (_currentStatus == status)
            {
                _logService.LogDebug($"设备状态未变化，依然为: {status}", "状态管理器");
                return;
            }

            CurrentStatus = status;
            await UpdateIndicatorsAsync();
        }

        /// <summary>
        /// 设置设备为正常运行状态
        /// </summary>
        public async Task SetNormalState()
        {
            await SetStatusAsync(DeviceStatus.Running);
        }

        /// <summary>
        /// 设置设备为待机状态
        /// </summary>
        public async Task SetStandbyState()
        {
            await SetStatusAsync(DeviceStatus.Standby);
        }

        /// <summary>
        /// 设置设备为警告状态
        /// </summary>
        public async Task SetWarningState()
        {
            await SetStatusAsync(DeviceStatus.Warning);
        }

        /// <summary>
        /// 设置设备为错误状态
        /// </summary>
        /// <param name="withBuzzer">是否伴随蜂鸣器</param>
        public async Task SetErrorState(bool withBuzzer = true)
        {
            await SetStatusAsync(DeviceStatus.Error);
            // 根据需要额外控制蜂鸣器
            if (!withBuzzer)
            {
                await SetBuzzer(false);
            }
        }

        /// <summary>
        /// 设置设备为紧急停止状态
        /// </summary>
        public async Task SetEmergencyStopState()
        {
            await SetStatusAsync(DeviceStatus.EmergencyStop);
        }

        /// <summary>
        /// 设置设备为暂停状态
        /// </summary>
        public async Task SetPausedState()
        {
            await SetStatusAsync(DeviceStatus.Paused);
        }

        /// <summary>
        /// 根据当前状态更新指示灯
        /// </summary>
        private async Task UpdateIndicatorsAsync()
        {
            try
            {
                switch (_currentStatus)
                {
                    case DeviceStatus.Running:
                        await StopAllBlinking();
                        await SetRedLight(false);
                        await SetYellowLight(false);
                        await SetGreenLight(true);
                        await SetBuzzer(false);
                        _logService.LogInformation("设备状态设置为正常运行", "状态管理器");
                        break;
                    case DeviceStatus.Standby:
                    case DeviceStatus.Paused:
                        await StopAllBlinking();
                        await SetRedLight(false);
                        await SetGreenLight(false);
                        await SetYellowLight(true);
                        await SetBuzzer(false);
                        _logService.LogInformation("设备状态设置为待机/暂停", "状态管理器");
                        break;
                    case DeviceStatus.Warning:
                        await StopAllBlinking();
                        await SetRedLight(false);
                        await SetGreenLight(false);
                        await BeepOnce(300);
                        await StartBlinking(IndicatorLightType.Yellow);
                        _logService.LogWarning("设备状态设置为警告", "状态管理器");
                        break;
                    case DeviceStatus.Error:
                    case DeviceStatus.EmergencyStop:
                        await StopAllBlinking();
                        await SetGreenLight(false);
                        await SetYellowLight(false);
                        await SetRedLight(true);
                        await SetBuzzer(true); // 默认开启蜂鸣器
                        _logService.LogError("设备状态设置为故障", "状态管理器");
                        break;
                    default:
                        await StopAllBlinking();
                        await SetRedLight(false);
                        await SetGreenLight(false);
                        await SetYellowLight(true);
                        await SetBuzzer(false);
                        _logService.LogInformation("设备状态设置为默认待机", "状态管理器");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "更新设备状态指示灯失败", "状态管理器");
            }
        }

        /// <summary>
        /// 状态变更处理
        /// </summary>
        private void OnStatusChanged(DeviceStatus oldStatus, DeviceStatus newStatus)
        {
            string statusMessage = $"设备状态从 {oldStatus} 变更为 {newStatus}";

            // 记录状态变更
            switch (newStatus)
            {
                case DeviceStatus.Error:
                case DeviceStatus.EmergencyStop:
                    _logService.LogError(statusMessage, "状态管理器");
                    break;
                case DeviceStatus.Warning:
                    _logService.LogWarning(statusMessage, "状态管理器");
                    break;
                default:
                    _logService.LogInformation(statusMessage, "状态管理器");
                    break;
            }

            // 触发状态变更事件
            StatusChanged?.Invoke(this, newStatus);
        }

        /// <summary>
        /// 设置报警管理器并订阅报警事件
        /// </summary>
        /// <param name="alarmManager">报警管理器</param>
        public void SetAlarmManager(AlarmManager alarmManager)
        {
            if (alarmManager == null)
            {
                _logService.LogWarning("尝试设置空的报警管理器", "状态管理器");
                return;
            }

            _alarmManager = alarmManager;

            // 订阅报警事件
            _alarmManager.AlarmRaised += OnAlarmRaised;
            _alarmManager.AlarmCleared += OnAlarmCleared;

            _logService.LogInformation("报警管理器已设置并订阅事件", "状态管理器");
        }

        /// <summary>
        /// 报警触发事件处理
        /// </summary>
        private void OnAlarmRaised(object sender, AlarmEventArgs e)
        {
            if (e == null || e.AlarmInfo == null)
                return;

            lock (_alarmLock)
            {
                // 检查报警是否已存在于列表中
                if (!_activeAlarms.Any(a => a.Id == e.AlarmInfo.Id))
                {
                    _activeAlarms.Add(e.AlarmInfo);
                    UpdateAlarmFlags();
                    AlarmsChanged?.Invoke(this, _activeAlarms);

                    // 记录报警
                    _logService.LogWarning(
                        $"报警触发: {e.AlarmInfo.Name} ({e.AlarmInfo.AlarmType})",
                        "状态管理器"
                    );

                    // 根据报警更新设备状态
                    UpdateDeviceStatusBasedOnAlarms();

                    // 停止所有轴的运动
                    EmergencyStopAllAxes();
                }
            }
        }

        /// <summary>
        /// 急停所有轴
        /// </summary>
        private void EmergencyStopAllAxes()
        {
            try
            {
                // 调用运动控制卡的急停所有轴方法
                _motionCard.EmergencyStopAllAxis();
                _logService.LogWarning("检测到报警，已急停所有轴", "状态管理器");
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "急停所有轴时发生错误", "状态管理器");
            }
        }

        /// <summary>
        /// 报警清除事件处理
        /// </summary>
        private void OnAlarmCleared(object sender, AlarmEventArgs e)
        {
            if (e == null || e.AlarmInfo == null)
                return;

            lock (_alarmLock)
            {
                // 从列表中移除报警
                _activeAlarms.RemoveAll(a => a.Id == e.AlarmInfo.Id);
                UpdateAlarmFlags();
                AlarmsChanged?.Invoke(this, _activeAlarms);

                // 记录报警清除
                _logService.LogInformation(
                    $"报警清除: {e.AlarmInfo.Name} ({e.AlarmInfo.AlarmType})",
                    "状态管理器"
                );

                // 根据剩余报警更新设备状态
                UpdateDeviceStatusBasedOnAlarms();
            }
        }

        /// <summary>
        /// 更新报警标志
        /// </summary>
        private void UpdateAlarmFlags()
        {
            _hasActiveAlarms = _activeAlarms.Count > 0;
            _hasMotionAlarms = _activeAlarms.Any(a => a.AlarmType == AlarmType.MotionAlarm);
            _hasServoAlarms = _activeAlarms.Any(a => a.AlarmType == AlarmType.ServoAlarm);
        }

        /// <summary>
        /// 根据报警更新设备状态
        /// </summary>
        private async void UpdateDeviceStatusBasedOnAlarms()
        {
            if (_hasServoAlarms)
            {
                // 伺服报警为最高优先级，设置为紧急停止状态
                await SetEmergencyStopState();
            }
            else if (_hasMotionAlarms)
            {
                // 运动报警为次优先级，设置为错误状态
                await SetErrorState();
            }
            else if (_hasActiveAlarms)
            {
                // 其他报警，设置为警告状态
                await SetWarningState();
            }
            else if (
                _currentStatus == DeviceStatus.Error
                || _currentStatus == DeviceStatus.EmergencyStop
                || _currentStatus == DeviceStatus.Warning
            )
            {
                // 如果当前是错误或警告状态，且没有活动报警，恢复到待机状态
                await SetStandbyState();
            }
        }

        /// <summary>
        /// 重置所有报警
        /// </summary>
        public async Task ResetAlarmsAsync()
        {
            if (_alarmManager == null)
            {
                _logService.LogWarning("报警管理器未设置，无法重置报警", "状态管理器");
                return;
            }

            try
            {
                // 记录重置报警请求
                _logService.LogInformation("尝试重置所有报警", "状态管理器");

                // 重置所有报警
                bool result = _alarmManager.ResetAllAlarms();

                if (result)
                {
                    _logService.LogInformation("报警重置成功", "状态管理器");
                }
                else
                {
                    _logService.LogWarning("报警重置未成功", "状态管理器");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "重置报警时发生错误", "状态管理器");
            }
        }

        /// <summary>
        /// 获取当前报警状态的描述
        /// </summary>
        public string GetAlarmStatusDescription()
        {
            lock (_alarmLock)
            {
                if (!_hasActiveAlarms)
                    return "无报警";

                if (_hasServoAlarms && _hasMotionAlarms)
                    return "伺服与运动报警";
                else if (_hasServoAlarms)
                    return "伺服报警";
                else if (_hasMotionAlarms)
                    return "运动报警";
                else
                    return $"报警数量: {_activeAlarms.Count}";
            }
        }

        #endregion

        /// <summary>
        /// 强制刷新设备状态指示灯（即使状态未变化）
        /// </summary>
        public async Task RefreshIndicatorsAsync()
        {
            // 先重置缓存状态，确保下次设置时会执行
            ResetIndicatorStates();

            // 根据当前状态重新设置指示灯
            await UpdateIndicatorsAsync();
            _logService.LogInformation("已强制刷新指示灯状态", "状态管理器");
        }

        /// <summary>
        /// 重置所有指示灯缓存状态，强制下次设置时执行操作
        /// </summary>
        public void ResetIndicatorStates()
        {
            _lastRedLightState = false;
            _lastGreenLightState = false;
            _lastYellowLightState = false;
            _lastBuzzerState = false;
            _logService.LogDebug("已重置指示灯缓存状态", "状态管理器");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopAllBlinking();

            // 关闭所有指示灯和蜂鸣器
            try
            {
                // 使用同步方式调用异步方法，确保在Dispose中完成
                Task.Run(async () =>
                    {
                        // 关闭所有指示灯
                        await SetRedLight(false);
                        await SetGreenLight(false);
                        await SetYellowLight(false);
                        await SetBuzzer(false);

                        _logService.LogInformation("已关闭所有指示灯和蜂鸣器", "状态管理器");
                    })
                    .Wait();
            }
            catch (Exception ex)
            {
                _logService.LogWarning($"关闭指示灯和蜂鸣器时发生异常:{ex.Message}", "状态管理器");
            }

            if (_alarmManager != null)
            {
                try
                {
                    // 取消订阅报警事件
                    _alarmManager.AlarmRaised -= OnAlarmRaised;
                    _alarmManager.AlarmCleared -= OnAlarmCleared;
                }
                catch (Exception ex)
                {
                    _logService.LogWarning(
                        $"取消订阅报警事件时发生异常:{ex.Message}",
                        "状态管理器"
                    );
                }
            }

            // 停止所有闪烁任务
            foreach (var cts in _blinkingTasks.Values)
            {
                try
                {
                    cts.Cancel();
                    cts.Dispose();
                }
                catch (Exception ex)
                {
                    _logService.LogWarning($"停止闪烁任务时发生异常:{ex.Message}", "状态管理器");
                }
            }
            _blinkingTasks.Clear();

            _logService.LogInformation("状态管理器已释放", "状态管理器");
        }
    }
}
