using System;
using System.Drawing;
using System.Threading.Tasks;
using Cognex.VisionPro;
using Cognex.VisionPro.ToolBlock;

namespace Nickel_Inspect.Services
{
    /// <summary>
    /// VisionPro服务接口，提供相机图像采集和处理功能
    /// </summary>
    public interface IVisionProService
    {
        /// <summary>
        /// 初始化VisionPro服务
        /// </summary>
        /// <returns>初始化是否成功</returns>
        bool Initialize();

        /// <summary>
        /// 设置当前操作的工具块
        /// </summary>
        /// <param name="toolBlock"></param>
        void SetWorkingToolBlock(CogToolBlock toolBlock);

        /// <summary>
        /// 加载VisionPro工具块文件(.vpp)
        /// </summary>
        /// <param name="vppFilePath">VisionPro工具块文件路径</param>
        /// <returns>加载是否成功</returns>
        bool LoadToolBlock(string vppFilePath);

        /// <summary>
        /// 获取图像并执行工具块
        /// </summary>
        /// <returns>检测结果对象，包含处理结果、原始图像和处理后图像等信息</returns>
        Task<VisionProService.InspectionResult> AcquireAndProcessImageAsync();

        /// <summary>
        /// 保存图像到指定路径
        /// </summary>
        /// <param name="image">要保存的图像</param>
        /// <param name="filePath">保存路径</param>
        /// <returns>保存是否成功</returns>
        bool SaveImage(CogImage8Grey image, string filePath);

        /// <summary>
        /// 释放资源
        /// </summary>
        void Dispose();

        /// <summary>
        /// 图像采集完成事件
        /// </summary>
        event EventHandler<ImageAcquiredEventArgs> ImageAcquired;
    }

    /// <summary>
    /// 图像采集完成事件参数
    /// </summary>
    public class ImageAcquiredEventArgs : EventArgs
    {
        /// <summary>
        /// 采集到的图像
        /// </summary>
        public Bitmap Image { get; set; }

        /// <summary>
        /// 图像处理结果
        /// </summary>
        public bool ProcessResult { get; set; }

        /// <summary>
        /// 结果描述
        /// </summary>
        public string ResultDescription { get; set; }
    }
}
