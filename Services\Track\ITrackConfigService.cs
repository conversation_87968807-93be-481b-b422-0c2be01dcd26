using System.Threading.Tasks;
using Nickel_Inspect.Models.Track;

namespace Nickel_Inspect.Services.Track
{
    /// <summary>
    /// 轨道配置服务接口
    /// </summary>
    public interface ITrackConfigService
    {
        /// <summary>
        /// 加载轨道配置
        /// </summary>
        /// <returns>轨道配置对象</returns>
        Task<TrackConfig> LoadConfigAsync();

        /// <summary>
        /// 保存轨道配置
        /// </summary>
        /// <param name="config">要保存的配置</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveConfigAsync(TrackConfig config);

        /// <summary>
        /// 加载轨道状态配置并应用到状态对象
        /// </summary>
        /// <param name="state">要应用配置的状态对象</param>
        /// <returns>应用是否成功</returns>
        Task<bool> LoadAndApplyConfigAsync(TrackStateData state);

        /// <summary>
        /// 从当前状态保存配置
        /// </summary>
        /// <param name="state">当前状态对象</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveConfigFromStateAsync(TrackStateData state);

        /// <summary>
        /// 检查配置是否有变更
        /// </summary>
        /// <param name="state">当前状态</param>
        /// <returns>如果有变更返回true，否则返回false</returns>
        Task<bool> HasConfigChangedAsync(TrackStateData state);
    }
}
