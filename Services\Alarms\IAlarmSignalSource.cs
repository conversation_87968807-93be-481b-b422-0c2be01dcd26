using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Nickel_Inspect.Services.Alarms
{
    /// <summary>
    /// 报警类型枚举
    /// </summary>
    public enum AlarmType
    {
        ServoAlarm, // 伺服报警
        SystemAlarm, // 系统报警
        MotionAlarm, // 运动报警
        CommunicationAlarm, // 通信报警
        SoftwareAlarm, // 软件报警
        CustomAlarm, // 自定义报警
    }

    /// <summary>
    /// 报警信号源接口，定义报警源必须实现的方法
    /// </summary>
    public interface IAlarmSignalSource
    {
        /// <summary>
        /// 报警源标识符
        /// </summary>
        string Id { get; }

        /// <summary>
        /// 报警源名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 报警类型
        /// </summary>
        AlarmType AlarmType { get; }

        /// <summary>
        /// 相关设备
        /// </summary>
        string RelatedDevice { get; }

        /// <summary>
        /// 报警描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 检查报警是否激活
        /// </summary>
        /// <returns>如果报警被触发，返回true；否则返回false</returns>
        bool IsAlarmActive();

        /// <summary>
        /// 初始化报警源
        /// </summary>
        void Initialize();

        /// <summary>
        /// 启用报警监控
        /// </summary>
        void Enable();

        /// <summary>
        /// 禁用报警监控
        /// </summary>
        void Disable();

        /// <summary>
        /// 复位报警
        /// </summary>
        /// <returns>如果复位成功，返回true；否则返回false</returns>
        bool ResetAlarm();

        /// <summary>
        /// 获取附加信息
        /// </summary>
        /// <returns>包含附加信息的字典</returns>
        Dictionary<string, object> GetAdditionalInfo();
    }
}
