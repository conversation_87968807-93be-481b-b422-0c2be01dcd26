using System;

namespace Nickel_Inspect.Models.Track
{
    /// <summary>
    /// 轨道运行模式
    /// </summary>
    public enum TrackMode
    {
        Normal, // 正常模式
        Manual, // 手动模式
    }

    /// <summary>
    /// 轨道方向
    /// </summary>
    public enum TrackDirection
    {
        LeftToRight, // 从左到右
        RightToLeft, // 从右到左
    }

    /// <summary>
    /// 轨道状态数据
    /// </summary>
    public class TrackStateData
    {
        /// <summary>
        /// 当前状态描述
        /// </summary>
        public string CurrentState { get; set; } = "未初始化";

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 当前模式
        /// </summary>
        public TrackMode CurrentMode { get; set; } = TrackMode.Normal;

        /// <summary>
        /// 运行方向
        /// </summary>
        public TrackDirection Direction { get; set; } = TrackDirection.LeftToRight;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否启用SMEMA
        /// </summary>
        public bool SmemaEnabled { get; set; } = false;

        /// <summary>
        /// 是否处于调试模式
        /// </summary>
        public bool IsDebugMode { get; set; } = false;

        /// <summary>
        /// 当前皮带速度
        /// </summary>
        public int BeltSpeed { get; set; } = 0;

        /// <summary>
        /// 当前调宽轴速度
        /// </summary>
        public int WidthAxisSpeed { get; set; } = 0;

        /// <summary>
        /// 当前报警数量
        /// </summary>
        public int CurrentAlarmCount { get; set; } = 0;
    }
}
