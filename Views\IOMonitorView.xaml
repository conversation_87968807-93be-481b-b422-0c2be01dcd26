<UserControl
    x:Class="Nickel_Inspect.Views.IOMonitorView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    d:DesignHeight="450"
    d:DesignWidth="800"
    prism:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock
            Margin="0,0,0,20"
            Style="{StaticResource TextBlockLargeBold}"
            Text="IO监控" />

        <ScrollViewer Grid.Row="1">
            <ItemsControl ItemsSource="{Binding IOGroups}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <GroupBox Margin="0,0,0,10" Header="{Binding Name}">
                            <ItemsControl ItemsSource="{Binding Points}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <WrapPanel />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border
                                            Width="70"
                                            Height="50"
                                            Margin="2"
                                            Background="{DynamicResource RegionBrush}"
                                            BorderBrush="{DynamicResource BorderBrush}"
                                            BorderThickness="1"
                                            CornerRadius="3">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="*" />
                                                </Grid.RowDefinitions>

                                                <TextBlock
                                                    Margin="2"
                                                    HorizontalAlignment="Center"
                                                    FontSize="11"
                                                    Text="{Binding Name}" />

                                                <Button
                                                    Grid.Row="1"
                                                    Width="25"
                                                    Height="25"
                                                    Margin="2"
                                                    Command="{Binding DataContext.ToggleOutputCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    IsEnabled="{Binding CanControl}">
                                                    <Button.Style>
                                                        <Style BasedOn="{StaticResource ButtonPrimary}" TargetType="Button">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding State}" Value="True">
                                                                    <Setter Property="Background" Value="#FF4CAF50" />
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding State}" Value="False">
                                                                    <Setter Property="Background" Value="#FFE51C23" />
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Button.Style>
                                                </Button>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </GroupBox>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>