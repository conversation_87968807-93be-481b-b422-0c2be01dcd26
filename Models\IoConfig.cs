using System;
using Newtonsoft.Json;

namespace Nickel_Inspect.Models
{
    public class IoConfig
    {
        private int _pulseWidth;
        private string _group;
        private int _subGroup;
        private int _ioBitIndex;
        private string _name;
        private string _description;
        private string _type;
        private int _ioAddress;

        public string Group
        {
            get { return _group; }
            set { _group = value; }
        }

        public int SubGroup
        {
            get { return _subGroup; }
            set { _subGroup = value; }
        }

        [JsonProperty("bitIndex")]
        public int IoBitIndex
        {
            get { return _ioBitIndex; }
            set { _ioBitIndex = value; }
        }

        public string Name
        {
            get { return _name; }
            set { _name = value; }
        }

        public string Description
        {
            get { return _description; }
            set { _description = value; }
        }

        public string Type
        {
            get { return _type; }
            set { _type = value; }
        }

        public int IoAddress
        {
            get { return _ioAddress; }
            set { _ioAddress = value; }
        }

        public int PulseWidth
        {
            get { return _pulseWidth; }
            set { _pulseWidth = value; }
        }

        public string GetIoAddress()
        {
            return $"{Group}{SubGroup}.{IoBitIndex}";
        }
    }

    public enum IoGroup
    {
        X, // 输入
        Y, // 输出
        Z, // 扩展IO
    }

    public enum IoType
    {
        Input,
        Output,
        Pulse,
        NC, // 常闭
        NO, // 常开
    }
}
