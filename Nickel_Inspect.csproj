<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\Obfuscar.2.2.47\build\obfuscar.props" Condition="Exists('packages\Obfuscar.2.2.47\build\obfuscar.props')" />
  <Import Project="packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D58EF1B2-7FB4-44C1-8A43-0A002A3BF90A}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Nickel_Inspect</RootNamespace>
    <AssemblyName>Nickel_Inspect</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|ARM64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\ARM64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>ARM64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|ARM64'">
    <OutputPath>bin\ARM64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>ARM64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>aoi_icon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Cognex.Applications.ImageSave">
      <HintPath>C:\Users\<USER>\Desktop\Cognex.Applications.ImageSave.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.Vision.Color.Net">
      <HintPath>C:\Program Files\Cognex\VisionPro\bin\Cognex.Vision.Color.Net.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.VisionPro, Version=89.0.0.0, Culture=neutral, PublicKeyToken=ef0f902af9dee505, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.VisionPro.Controls, Version=89.0.0.0, Culture=neutral, PublicKeyToken=ef0f902af9dee505, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.Controls.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.VisionPro.Core, Version=89.0.0.0, Culture=neutral, PublicKeyToken=ef0f902af9dee505, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.Core.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.VisionPro.Display.Controls, Version=89.0.0.0, Culture=neutral, PublicKeyToken=ef0f902af9dee505, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.Display.Controls.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.VisionPro.ImageFile, Version=89.0.0.0, Culture=neutral, PublicKeyToken=ef0f902af9dee505, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.ImageFile.dll</HintPath>
    </Reference>
    <Reference Include="Cognex.VisionPro.ToolGroup, Version=89.0.0.0, Culture=neutral, PublicKeyToken=ef0f902af9dee505, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.ToolGroup.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Dapper.2.1.66\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="DryIoc, Version=4.7.7.0, Culture=neutral, PublicKeyToken=dfbf2bd50fcf7768, processorArchitecture=MSIL">
      <HintPath>packages\DryIoc.dll.4.7.7\lib\net45\DryIoc.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FluentModbus, Version=5.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\FluentModbus.5.3.1\lib\netstandard2.0\FluentModbus.dll</HintPath>
    </Reference>
    <Reference Include="HandyControl, Version=3.5.1.0, Culture=neutral, PublicKeyToken=45be8712787a1e5b, processorArchitecture=MSIL">
      <HintPath>packages\HandyControl.3.5.1\lib\net481\HandyControl.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.9.0.1\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=5.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.Abstractions.5.0.0\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors, Version=1.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Xaml.Behaviors.Wpf.1.1.31\lib\net45\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Prism, Version=8.1.97.5141, Culture=neutral, PublicKeyToken=40ee6c3a2184dc59, processorArchitecture=MSIL">
      <HintPath>packages\Prism.Core.8.1.97\lib\net47\Prism.dll</HintPath>
    </Reference>
    <Reference Include="Prism.DryIoc.Wpf, Version=8.1.97.5141, Culture=neutral, PublicKeyToken=40ee6c3a2184dc59, processorArchitecture=MSIL">
      <HintPath>packages\Prism.DryIoc.8.1.97\lib\net47\Prism.DryIoc.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Prism.Wpf, Version=8.1.97.5141, Culture=neutral, PublicKeyToken=40ee6c3a2184dc59, processorArchitecture=MSIL">
      <HintPath>packages\Prism.Wpf.8.1.97\lib\net47\Prism.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Serilog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.4.0.0\lib\net471\Serilog.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.File, Version=6.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>packages\Serilog.Sinks.File.6.0.0\lib\net471\Serilog.Sinks.File.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.119.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.EF6, Version=1.0.119.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SQLite.EF6.1.0.119.0\lib\net46\System.Data.SQLite.EF6.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.Linq, Version=1.0.119.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SQLite.Linq.1.0.119.0\lib\net46\System.Data.SQLite.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Ports, Version=5.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Ports.5.0.0\lib\net461\System.IO.Ports.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Channels.8.0.0\lib\net462\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Converters\BooleanInvertConverter.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Converters\BooleanToColorConverter.cs" />
    <Compile Include="Converters\BooleanToCursorConverter.cs" />
    <Compile Include="Converters\FilePathToNameConverter.cs" />
    <Compile Include="Models\LightControl\LightController.cs" />
    <Compile Include="Services\Alarms\AlarmManager.cs" />
    <Compile Include="Services\Alarms\AlarmSystemHelper.cs" />
    <Compile Include="Services\Alarms\AlarmSystemInitializer.cs" />
    <Compile Include="Services\Alarms\DigitalInputAlarmSource.cs" />
    <Compile Include="Services\Alarms\IAlarmSignalSource.cs" />
    <Compile Include="Services\Alarms\MotionAlarmSource.cs" />
    <Compile Include="Services\LightControl\CCSLightController.cs" />
    <Compile Include="Services\LightControl\HikvisionLightController.cs" />
    <Compile Include="Services\LightControl\ILightControlService.cs" />
    <Compile Include="Services\LightControl\LightControllerBase.cs" />
    <Compile Include="Services\LightControl\LightControlService.cs" />
    <Compile Include="Services\LightControl\YixuLightController.cs" />
    <Compile Include="Services\StatusManager.cs" />
    <Compile Include="Models\SystemLog.cs" />
    <Compile Include="Models\Track\BoardArrivedEventArgs.cs" />
    <Compile Include="Models\Track\TrackConfig.cs" />
    <Compile Include="Services\IInspectionService.cs" />
    <Compile Include="Services\ILogService.cs" />
    <Compile Include="Services\InspectionService.cs" />
    <Compile Include="Services\IVisionProService.cs" />
    <Compile Include="Services\LogEventArgs.cs" />
    <Compile Include="Services\LogService.cs" />
    <Compile Include="Services\Track\ITrackConfigService.cs" />
    <Compile Include="Services\Track\TrackConfigService.cs" />
    <Compile Include="Services\Track\TrackService.cs" />
    <Compile Include="Services\VisionProService.cs" />
    <Compile Include="ViewModels\AddInspectPointViewModel.cs" />
    <Compile Include="ViewModels\EditInspectPointViewModel.cs" />
    <Compile Include="ViewModels\InspectionViewModel.cs" />
    <Compile Include="ViewModels\LogQueryViewModel.cs" />
    <Compile Include="ViewModels\TrackControlViewModel.cs" />
    <Compile Include="ViewModels\VisionProTestViewModel.cs" />
    <Compile Include="ViewModels\ConfigPointViewModel.cs" />
    <Compile Include="Views\Dialogs\ShutdownProgressDialog.xaml.cs">
      <DependentUpon>ShutdownProgressDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\StartupSplashDialog.xaml.cs">
      <DependentUpon>StartupSplashDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\EditInspectPoint.xaml.cs">
      <DependentUpon>EditInspectPoint.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\InspectionView.xaml.cs">
      <DependentUpon>InspectionView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\LogQueryView.xaml.cs">
      <DependentUpon>LogQueryView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\TrackControlView.xaml.cs">
      <DependentUpon>TrackControlView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\VisionProTestView.xaml.cs">
      <DependentUpon>VisionProTestView.xaml</DependentUpon>
    </Compile>
    <Page Include="Images\startup_logo.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddInspectPoint.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\ShutdownProgressDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\Dialogs\StartupSplashDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\EditInspectPoint.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\InspectionView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\IOMonitorView.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\LogQueryView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\NewProductWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Models\InspectionPoint.cs" />
    <Compile Include="Models\DeviceConfiguration.cs" />
    <Compile Include="Models\IoConfig.cs" />
    <Compile Include="Models\ProductModel.cs" />
    <Compile Include="Models\Track\TrackEnums.cs" />
    <Compile Include="Models\Track\TrackState.cs" />
    <Compile Include="Services\BkMotionCard.cs" />
    <Compile Include="Services\DatabaseService.cs" />
    <Compile Include="Services\Track\ITrackService.cs" />
    <Compile Include="ViewModels\AddNewProductModelViewModel.cs" />
    <Compile Include="ViewModels\IOMonitorViewModel.cs" />
    <Compile Include="ViewModels\MainWindowViewModel.cs" />
    <Compile Include="ViewModels\NewProductViewModel.cs" />
    <Compile Include="Views\AddInspectPoint.xaml.cs">
      <DependentUpon>AddInspectPoint.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\IOMonitorView.xaml.cs">
      <DependentUpon>IOMonitorView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Views\TrackControlView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ConfigPointView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\VisionProTestView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="Views\ConfigPointView.xaml.cs">
      <DependentUpon>ConfigPointView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\NewProductWindow.xaml.cs">
      <DependentUpon>NewProductWindow.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="AcqTest.vpp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Config\LightControllerConfig.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="ModbusConfig.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="DeviceConfiguration.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="ObfuscarReadMe.md" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="README.md" />
    <None Include="VisionProReadMe.md" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Images\default.bmp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="C:\Program Files\Cognex\VisionPro\bin\Cognex.Vision.Core.dll" />
    <Analyzer Include="C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.Core.dll" />
    <Analyzer Include="C:\Program Files\Cognex\VisionPro\ReferencedAssemblies\Cognex.VisionPro.ToolGroup.dll" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="obfuscar.xml" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="aoi_icon.ico" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\VPP文件备份\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用"NuGet 程序包还原"可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
    <Error Condition="!Exists('packages\Obfuscar.2.2.47\build\obfuscar.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Obfuscar.2.2.47\build\obfuscar.props'))" />
  </Target>
  <Import Project="packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <!-- 自动混淆配置 -->
  <PropertyGroup>
    <ObfuscateTargetCondition>$(Configuration.Contains('Release'))</ObfuscateTargetCondition>
    <ObfuscateTargetFramework>x64</ObfuscateTargetFramework>
  </PropertyGroup>
  <Target Name="RunObfuscator" AfterTargets="AfterBuild" Condition="'$(Configuration)' == 'Release' And '$(Platform)' == 'x64'">
    <Message Text="开始混淆代码，配置: $(Configuration), 平台: $(Platform)" Importance="high" />
    <Exec Command="$(SolutionDir)packages\Obfuscar.2.2.47\tools\Obfuscar.Console.exe $(ProjectDir)obfuscar.xml" WorkingDirectory="$(ProjectDir)" />
    <!-- 复制混淆后的文件到输出目录 -->
    <ItemGroup>
      <ObfuscatedFiles Include="$(ProjectDir)bin\x64\Release\Obfuscated\*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(ObfuscatedFiles)" DestinationFolder="$(ProjectDir)bin\x64\Release\Obfuscated_Deploy" />
    <Message Text="混淆完成，文件已复制到: $(ProjectDir)bin\x64\Release\Obfuscated_Deploy" Importance="high" />
  </Target>
</Project>