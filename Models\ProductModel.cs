using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Nickel_Inspect.Models
{
    /// <summary>
    /// 机种表
    /// </summary>
    public class ProductModel
    {
        /// <summary>
        /// 机种ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ModelId { get; set; }

        /// <summary>
        /// 机种名称
        /// </summary>
        public string ModelName { get; set; }

        /// <summary>
        /// 机种代码
        /// </summary>
        public string ModelCode { get; set; }

        /// <summary>
        /// 机种描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
    }
}
