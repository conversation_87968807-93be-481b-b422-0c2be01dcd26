using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Threading;
using Nickel_Inspect.Services;
using Prism.Commands;
using Prism.Mvvm;

namespace Nickel_Inspect.ViewModels
{
    public class IOPoint : BindableBase
    {
        private string _name;
        private bool _state;
        private bool _canControl;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public bool State
        {
            get => _state;
            set => SetProperty(ref _state, value);
        }

        public bool CanControl
        {
            get => _canControl;
            set => SetProperty(ref _canControl, value);
        }
    }

    public class IOGroup : BindableBase
    {
        private string _name;
        private ObservableCollection<IOPoint> _points;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public ObservableCollection<IOPoint> Points
        {
            get => _points;
            set => SetProperty(ref _points, value);
        }
    }

    public class IOMonitorViewModel : BindableBase, IDisposable
    {
        private readonly BkMotionCard _bkMottion;
        private readonly DispatcherTimer _timer;
        private readonly ModbusConfig _modbusConfig;
        private bool _isActive;

        public ObservableCollection<IOGroup> IOGroups { get; private set; }

        public DelegateCommand<IOPoint> ToggleOutputCommand { get; private set; }

        public IOMonitorViewModel(BkMotionCard bkMottion, ModbusConfig modbusConfig)
        {
            _bkMottion = bkMottion;
            _modbusConfig = modbusConfig;

            InitializeIOGroups();

            ToggleOutputCommand = new DelegateCommand<IOPoint>(ExecuteToggleOutput);

            // 创建定时器以定期更新IO状态
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100), // 100ms刷新一次
            };
            _timer.Tick += Timer_Tick;
        }

        /// <summary>
        /// 启用IO状态刷新
        /// </summary>
        public void Enable()
        {
            if (!_isActive)
            {
                _isActive = true;
                _timer.Start();
                UpdateIOStates(); // 立即更新一次状态
            }
        }

        /// <summary>
        /// 禁用IO状态刷新
        /// </summary>
        public void Disable()
        {
            if (_isActive)
            {
                _isActive = false;
                _timer.Stop();
            }
        }

        private void InitializeIOGroups()
        {
            IOGroups = new ObservableCollection<IOGroup>();

            // 添加X0组
            var x0Group = new IOGroup
            {
                Name = "X0组",
                Points = new ObservableCollection<IOPoint>(
                    Enumerable
                        .Range(0, 8)
                        .Select(i => new IOPoint { Name = $"X0.{i}", CanControl = false })
                ),
            };
            IOGroups.Add(x0Group);

            // 添加X1组
            var x1Group = new IOGroup
            {
                Name = "X1组",
                Points = new ObservableCollection<IOPoint>(
                    Enumerable
                        .Range(0, 8)
                        .Select(i => new IOPoint { Name = $"X1.{i}", CanControl = false })
                ),
            };
            IOGroups.Add(x1Group);

            // 添加Y0组
            var y0Group = new IOGroup
            {
                Name = "Y0组",
                Points = new ObservableCollection<IOPoint>(
                    Enumerable
                        .Range(0, 8)
                        .Select(i => new IOPoint { Name = $"Y0.{i}", CanControl = true })
                ),
            };
            IOGroups.Add(y0Group);

            // 添加Y1组
            var y1Group = new IOGroup
            {
                Name = "Y1组",
                Points = new ObservableCollection<IOPoint>(
                    Enumerable
                        .Range(0, 8)
                        .Select(i => new IOPoint { Name = $"Y1.{i}", CanControl = true })
                ),
            };
            IOGroups.Add(y1Group);

            // 添加Z0组
            var z0Group = new IOGroup
            {
                Name = "Z0组",
                Points = new ObservableCollection<IOPoint>(
                    Enumerable
                        .Range(0, 8)
                        .Select(i => new IOPoint { Name = $"Z0.{i}", CanControl = true })
                ),
            };
            IOGroups.Add(z0Group);
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            UpdateIOStates();
        }

        private void UpdateIOStates()
        {
            try
            {
                // 更新X0组状态
                var x0Value = _bkMottion.ReadInputsX0();
                UpdateGroupStates(IOGroups[0], x0Value);

                // 更新X1组状态
                var x1Value = _bkMottion.ReadInputsX1();
                UpdateGroupStates(IOGroups[1], x1Value);

                // 更新Y0组状态
                var y0Value = _bkMottion.ReadOutputsY0();
                UpdateGroupStates(IOGroups[2], y0Value);

                // 更新Y1组状态
                var y1Value = _bkMottion.ReadOutputsY1();
                UpdateGroupStates(IOGroups[3], y1Value);

                // 更新Z0组状态
                var z0Value = _bkMottion.ReadIOZ0();
                UpdateGroupStates(IOGroups[4], z0Value);
            }
            catch (Exception)
            {
                // 处理读取异常
            }
        }

        private void UpdateGroupStates(IOGroup group, int value)
        {
            for (int i = 0; i < 8; i++)
            {
                group.Points[i].State = ((value >> i) & 1) == 1;
            }
        }

        private void ExecuteToggleOutput(IOPoint point)
        {
            if (!point.CanControl)
                return;

            try
            {
                var groupIndex = IOGroups.IndexOf(IOGroups.First(g => g.Points.Contains(point)));
                var pointIndex = IOGroups[groupIndex].Points.IndexOf(point);

                switch (groupIndex)
                {
                    case 2: // Y0组
                        _bkMottion.SetOutputBitStatus(0, pointIndex, !point.State);
                        break;
                    case 3: // Y1组
                        _bkMottion.SetOutputBitStatus(1, pointIndex, !point.State);
                        break;
                    case 4: // Z0组
                        _bkMottion.SetZ0BitStatus(pointIndex, !point.State);
                        break;
                }
            }
            catch (Exception)
            {
                // 处理设置异常
            }
        }

        public void Dispose()
        {
            Disable();
            _timer?.Stop();
        }
    }
}
