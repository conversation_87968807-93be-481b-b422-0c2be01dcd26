using System;
using Prism.Mvvm;

namespace Nickel_Inspect.Models.Track
{
    /// <summary>
    /// 轨道状态数据类
    /// </summary>
    public class TrackStateData : BindableBase
    {
        private string _currentState;
        private TrackMode _currentMode;
        private TrackDirection _direction;
        private bool _boardInSensor;
        private bool _boardArrivedSensor;
        private bool _boardOutSensor;
        private bool _stopperCylinder;
        private bool _clampCylinder;
        private bool _nextMachineReady;
        private bool _hasBoard;
        private bool _readyToReceive;
        private string _errorMessage;
        private DateTime _lastUpdateTime;
        private string _transportState;
        private bool _smemaEnabled = true;
        private int _beltSpeed = 40;
        private int _widthAxisSpeed = 20;
        private bool _isDebugMode = false;
        private bool _isOfflineSimulation = false;

        /// <summary>
        /// 当前状态描述
        /// </summary>
        public string CurrentState
        {
            get => _currentState;
            set => SetProperty(ref _currentState, value);
        }

        /// <summary>
        /// 当前运行模式
        /// </summary>
        public TrackMode CurrentMode
        {
            get => _currentMode;
            set => SetProperty(ref _currentMode, value);
        }

        /// <summary>
        /// 传送方向
        /// </summary>
        public TrackDirection Direction
        {
            get => _direction;
            set => SetProperty(ref _direction, value);
        }

        /// <summary>
        /// 入料感应
        /// </summary>
        public bool BoardInSensor
        {
            get => _boardInSensor;
            set => SetProperty(ref _boardInSensor, value);
        }

        /// <summary>
        /// 到位感应
        /// </summary>
        public bool BoardArrivedSensor
        {
            get => _boardArrivedSensor;
            set => SetProperty(ref _boardArrivedSensor, value);
        }

        /// <summary>
        /// 出料感应
        /// </summary>
        public bool BoardOutSensor
        {
            get => _boardOutSensor;
            set => SetProperty(ref _boardOutSensor, value);
        }

        /// <summary>
        /// 阻挡气缸状态
        /// </summary>
        public bool StopperCylinder
        {
            get => _stopperCylinder;
            set => SetProperty(ref _stopperCylinder, value);
        }

        /// <summary>
        /// 夹紧气缸状态
        /// </summary>
        public bool ClampCylinder
        {
            get => _clampCylinder;
            set => SetProperty(ref _clampCylinder, value);
        }

        /// <summary>
        /// 下游设备准备好
        /// </summary>
        public bool NextMachineReady
        {
            get => _nextMachineReady;
            set => SetProperty(ref _nextMachineReady, value);
        }

        /// <summary>
        /// 本机有板
        /// </summary>
        public bool HasBoard
        {
            get => _hasBoard;
            set => SetProperty(ref _hasBoard, value);
        }

        /// <summary>
        /// 本机准备好接收
        /// </summary>
        public bool ReadyToReceive
        {
            get => _readyToReceive;
            set => SetProperty(ref _readyToReceive, value);
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set => SetProperty(ref _lastUpdateTime, value);
        }

        /// <summary>
        /// 传送状态
        /// </summary>
        public string TransportState
        {
            get => _transportState;
            set => SetProperty(ref _transportState, value);
        }

        /// <summary>
        /// SMEMA功能是否启用
        /// </summary>
        public bool SmemaEnabled
        {
            get => _smemaEnabled;
            set => SetProperty(ref _smemaEnabled, value);
        }

        /// <summary>
        /// 皮带速度（调试模式下使用）
        /// </summary>
        public int BeltSpeed
        {
            get => _beltSpeed;
            set => SetProperty(ref _beltSpeed, value);
        }

        /// <summary>
        /// 调宽轴速度（调试模式下使用）
        /// </summary>
        public int WidthAxisSpeed
        {
            get => _widthAxisSpeed;
            set => SetProperty(ref _widthAxisSpeed, value);
        }

        /// <summary>
        /// 是否处于调试模式
        /// </summary>
        public bool IsDebugMode
        {
            get => _isDebugMode;
            set => SetProperty(ref _isDebugMode, value);
        }

        /// <summary>
        /// 是否处于离线模拟模式
        /// </summary>
        public bool IsOfflineSimulation
        {
            get => _isOfflineSimulation;
            set => SetProperty(ref _isOfflineSimulation, value);
        }
    }
}
