using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using HandyControl.Controls;
using MessageBox = HandyControl.Controls.MessageBox;

namespace Nickel_Inspect.Views.Dialogs
{
    /// <summary>
    /// ShutdownProgressDialog.xaml 的交互逻辑
    /// </summary>
    public partial class ShutdownProgressDialog : HandyControl.Controls.Window
    {
        private CancellationTokenSource _cts = new CancellationTokenSource();

        public ShutdownProgressDialog()
        {
            InitializeComponent();
            this.Loaded += ShutdownProgressDialog_Loaded;
            this.Closing += ShutdownProgressDialog_Closing;
        }

        private void ShutdownProgressDialog_Loaded(object sender, RoutedEventArgs e)
        {
            // 创建关闭进度更新任务
            _ = UpdateShutdownProgressAsync(_cts.Token);
        }

        private void ShutdownProgressDialog_Closing(
            object sender,
            System.ComponentModel.CancelEventArgs e
        )
        {
            // 取消任务
            _cts.Cancel();
        }

        /// <summary>
        /// 更新关闭进度的文本显示
        /// </summary>
        public void UpdateStatus(string statusText)
        {
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(() => StatusTextBlock.Text = statusText);
            }
            else
            {
                StatusTextBlock.Text = statusText;
            }
        }

        /// <summary>
        /// 模拟关机进度的异步方法
        /// </summary>
        private async Task UpdateShutdownProgressAsync(CancellationToken cancellationToken)
        {
            string[] shutdownMessages = new string[]
            {
                "正在释放系统资源...",
                "正在关闭硬件连接...",
                "正在保存系统配置...",
                "正在关闭视觉服务...",
                "正在关闭运动控制系统...",
                "正在关闭指示灯...",
                "正在完成最终清理...",
            };

            try
            {
                for (
                    int i = 0;
                    i < shutdownMessages.Length && !cancellationToken.IsCancellationRequested;
                    i++
                )
                {
                    UpdateStatus(shutdownMessages[i]);
                    await Task.Delay(700, cancellationToken); // 每个消息显示700毫秒
                }
            }
            catch (OperationCanceledException)
            {
                // 任务被取消，正常退出
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"关闭过程中发生错误: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }
    }
}
