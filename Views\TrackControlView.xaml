<UserControl
    x:Class="Nickel_Inspect.Views.TrackControlView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:Nickel_Inspect.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:track="clr-namespace:Nickel_Inspect.Models.Track"
    Width="800"
    Height="900"
    MinWidth="800"
    MinHeight="900"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="{DynamicResource RegionBrush}"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <BooleanToVisibilityConverter x:Key="BoolToVis" />
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />
            <converters:BooleanToCursorConverter x:Key="BooleanToCursorConverter" />
            <Style
                x:Key="BaseButtonStyle"
                BasedOn="{StaticResource ButtonPrimary}"
                TargetType="Button">
                <Setter Property="Margin" Value="0,5" />
                <Setter Property="Height" Value="30" />
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="{DynamicResource RegionBrush}" CornerRadius="5">
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  状态和控制区域  -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  状态显示  -->
                <StackPanel
                    Grid.Column="0"
                    Margin="0,0,10,0"
                    Orientation="Horizontal">
                    <TextBlock
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="当前状态:" />
                    <TextBlock
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="{Binding CurrentState.CurrentState}" />
                    <TextBlock
                        Margin="20,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="运行模式:" />
                    <TextBlock
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="{Binding CurrentState.CurrentMode}" />
                    <TextBlock
                        Margin="20,0,0,0"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="传送方向:" />
                    <TextBlock
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="{Binding CurrentState.Direction}" />
                </StackPanel>

                <!--  紧急停止按钮  -->
                <Button
                    Grid.Column="1"
                    Width="100"
                    Height="30"
                    Background="Red"
                    Command="{Binding EmergencyStopCommand}"
                    Content="紧急停止"
                    Foreground="White"
                    Style="{StaticResource BaseButtonStyle}" />
            </Grid>

            <!--  主控制区域  -->
            <Grid Grid.Row="1" Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  左侧控制面板  -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <!--  基本控制按钮  -->
                    <GroupBox
                        Padding="5"
                        Header="基本控制"
                        Style="{StaticResource GroupBoxBaseStyle}">
                        <StackPanel Orientation="Vertical">
                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <Button
                                    Margin="0,0,10,0"
                                    Command="{Binding StartCommand}"
                                    Content="启动"
                                    Style="{StaticResource BaseButtonStyle}" />
                                <Button
                                    Background="#dc3545"
                                    Command="{Binding StopCommand}"
                                    Content="停止"
                                    Style="{StaticResource BaseButtonStyle}" />
                            </StackPanel>
                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <Button
                                    Margin="0,0,10,0"
                                    Background="#6c757d"
                                    Command="{Binding PauseCommand}"
                                    Content="暂停"
                                    Style="{StaticResource BaseButtonStyle}" />
                                <Button
                                    Background="#17a2b8"
                                    Command="{Binding ResumeCommand}"
                                    Content="继续"
                                    Style="{StaticResource BaseButtonStyle}" />
                            </StackPanel>

                            <Button
                                Background="#ffc107"
                                Command="{Binding ResetCommand}"
                                Content="复位"
                                Style="{StaticResource BaseButtonStyle}" />
                        </StackPanel>
                    </GroupBox>

                    <!--  运行模式选择  -->
                    <GroupBox
                        Margin="0,10"
                        Padding="5"
                        Header="运行模式"
                        Style="{StaticResource GroupBoxBaseStyle}">
                        <StackPanel>
                            <RadioButton
                                Margin="0,5"
                                Command="{Binding SetModeCommand}"
                                CommandParameter="{x:Static track:TrackMode.Normal}"
                                Content="常规模式"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                IsChecked="{Binding IsNormalMode}" />
                            <RadioButton
                                Margin="0,5"
                                Command="{Binding SetModeCommand}"
                                CommandParameter="{x:Static track:TrackMode.PassThrough}"
                                Content="过板模式"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                IsChecked="{Binding IsPassThroughMode}" />
                            <RadioButton
                                Margin="0,5"
                                Command="{Binding SetModeCommand}"
                                CommandParameter="{x:Static track:TrackMode.Manual}"
                                Content="手动模式"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                IsChecked="{Binding IsManualMode}" />
                        </StackPanel>
                    </GroupBox>

                    <!--  传送方向选择  -->
                    <GroupBox
                        Margin="0,10"
                        Padding="5"
                        Header="传送方向"
                        Style="{StaticResource GroupBoxBaseStyle}">
                        <StackPanel>
                            <RadioButton
                                Margin="0,5"
                                Command="{Binding SetDirectionCommand}"
                                CommandParameter="{x:Static track:TrackDirection.LeftToRight}"
                                Content="左进右出"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                IsChecked="{Binding IsLeftToRight}" />
                            <RadioButton
                                Margin="0,5"
                                Command="{Binding SetDirectionCommand}"
                                CommandParameter="{x:Static track:TrackDirection.RightToLeft}"
                                Content="右进左出"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                IsChecked="{Binding IsRightToLeft}" />
                        </StackPanel>
                    </GroupBox>

                    <!--  SMEMA启用/禁用  -->
                    <GroupBox
                        Margin="0,10"
                        Padding="5"
                        Header="SMEMA设置"
                        Style="{StaticResource GroupBoxBaseStyle}">
                        <StackPanel>
                            <CheckBox
                                Margin="0,5"
                                Content="启用SMEMA通信"
                                Foreground="{DynamicResource PrimaryTextBrush}"
                                IsChecked="{Binding IsSmemaEnabled}" />
                        </StackPanel>
                    </GroupBox>

                    <!--  调试模式  -->
                    <GroupBox
                        Margin="0,10"
                        Padding="5"
                        Header="调试模式">
                        <StackPanel>
                            <CheckBox
                                Margin="0,5"
                                Content="启用调试模式"
                                IsChecked="{Binding IsDebugMode}" />
                            <TextBlock
                                Margin="0,5,0,0"
                                Foreground="Blue"
                                Text="{Binding IsDebugMode, StringFormat=当前调试模式状态: {0}}" />
                        </StackPanel>
                    </GroupBox>

                    <!--  配置管理  -->
                    <GroupBox
                        Margin="0,10"
                        Padding="5"
                        Header="配置管理">
                        <Button
                            Command="{Binding SaveConfigCommand}"
                            Content="保存当前配置"
                            Style="{StaticResource BaseButtonStyle}" />
                    </GroupBox>
                </StackPanel>

                <!--  右侧状态显示区域  -->
                <Grid Grid.Column="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  传感器状态  -->
                    <GroupBox
                        Grid.Row="0"
                        Margin="0,0,0,10"
                        Header="传感器状态">
                        <UniformGrid Columns="3" Rows="2">
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.BoardInSensor, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleBoardInSensorCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="入料感应" />
                            </StackPanel>
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.BoardArrivedSensor, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleBoardArrivedSensorCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="到位感应" />
                            </StackPanel>
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.BoardOutSensor, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleBoardOutSensorCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="出料感应" />
                            </StackPanel>
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.StopperCylinder, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleStopperCylinderCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="阻挡气缸" />
                            </StackPanel>
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.ClampCylinder, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleClampCylinderCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="夹紧气缸" />
                            </StackPanel>
                        </UniformGrid>
                    </GroupBox>

                    <!--  SMEMA状态  -->
                    <GroupBox
                        Grid.Row="1"
                        Margin="0,0,0,10"
                        Header="SMEMA状态">
                        <UniformGrid Columns="2" Rows="2">
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.NextMachineReady, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleNextMachineReadyCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="下游准备好" />
                            </StackPanel>
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.HasBoard, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleHasBoardCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="本机有板" />
                            </StackPanel>
                            <StackPanel Margin="5" Orientation="Horizontal">
                                <Ellipse
                                    Width="16"
                                    Height="16"
                                    Margin="0,0,5,0"
                                    Cursor="{Binding IsDebugMode, Converter={StaticResource BooleanToCursorConverter}}"
                                    Fill="{Binding CurrentState.ReadyToReceive, Converter={StaticResource BooleanToColorConverter}}"
                                    IsEnabled="{Binding IsDebugMode}">
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="MouseLeftButtonDown">
                                            <i:InvokeCommandAction Command="{Binding ToggleReadyToReceiveCommand}" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDebugMode}" Value="True">
                                                    <Setter Property="ToolTip" Value="点击切换状态" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="本机准备好" />
                            </StackPanel>
                        </UniformGrid>
                    </GroupBox>

                    <!--  调试控制面板  -->
                    <GroupBox
                        Grid.Row="2"
                        Header="调试控制"
                        Visibility="{Binding IsDebugMode, Converter={StaticResource BoolToVis}}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  皮带速度设置  -->
                            <StackPanel Grid.Row="0" Margin="0,5">
                                <TextBlock
                                    Margin="0,0,0,5"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    Text="皮带速度设置" />
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Slider
                                        Grid.Column="0"
                                        IsSnapToTickEnabled="True"
                                        Maximum="100"
                                        Minimum="1"
                                        TickFrequency="5"
                                        TickPlacement="BottomRight"
                                        Value="{Binding BeltSpeed}" />
                                    <TextBox
                                        Grid.Column="1"
                                        Width="50"
                                        Margin="5,0,0,0"
                                        Text="{Binding BeltSpeed, StringFormat=F1}" />
                                </Grid>
                            </StackPanel>

                            <!--  皮带控制按钮  -->
                            <StackPanel
                                Grid.Row="1"
                                Margin="0,10"
                                Orientation="Horizontal">
                                <Button
                                    Width="100"
                                    Margin="0,0,10,0"
                                    Command="{Binding BeltForwardCommand}"
                                    Content="皮带正转" />
                                <Button
                                    Width="100"
                                    Margin="0,0,10,0"
                                    Command="{Binding BeltBackwardCommand}"
                                    Content="皮带反转" />
                                <Button
                                    Width="100"
                                    Command="{Binding BeltStopCommand}"
                                    Content="皮带停止" />
                            </StackPanel>

                            <!--  调宽轴速度设置  -->
                            <StackPanel Grid.Row="2" Margin="0,10">
                                <TextBlock
                                    Margin="0,0,0,5"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    Text="调宽轴速度设置" />
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <Slider
                                        Grid.Column="0"
                                        IsSnapToTickEnabled="True"
                                        Maximum="50"
                                        Minimum="1"
                                        TickFrequency="5"
                                        TickPlacement="BottomRight"
                                        Value="{Binding WidthAxisSpeed}" />
                                    <TextBox
                                        Grid.Column="1"
                                        Width="50"
                                        Margin="5,0,0,0"
                                        Text="{Binding WidthAxisSpeed, StringFormat=F1}" />
                                </Grid>
                            </StackPanel>

                            <!--  调宽轴控制按钮  -->
                            <StackPanel
                                Grid.Row="3"
                                Margin="0,10"
                                Orientation="Horizontal">
                                <Button
                                    Width="100"
                                    Margin="0,0,10,0"
                                    Command="{Binding WidthAxisForwardCommand}"
                                    Content="调宽轴正转" />
                                <Button
                                    Width="100"
                                    Margin="0,0,10,0"
                                    Command="{Binding WidthAxisBackwardCommand}"
                                    Content="调宽轴反转" />
                                <Button
                                    Width="100"
                                    Margin="0,0,10,0"
                                    Command="{Binding WidthAxisStopCommand}"
                                    Content="调宽轴停止" />
                                <Button
                                    Width="100"
                                    Command="{Binding WidthAxisHomeCommand}"
                                    Content="调宽轴回零" />
                            </StackPanel>

                            <!--  手动进出板控制  -->
                            <StackPanel
                                Grid.Row="4"
                                Margin="0,10"
                                Orientation="Horizontal">
                                <Button
                                    Width="100"
                                    Margin="0,0,10,0"
                                    Command="{Binding ManualBoardInCommand}"
                                    Content="手动进板" />
                                <Button
                                    Width="100"
                                    Command="{Binding ManualBoardOutCommand}"
                                    Content="手动出板" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!--  日志显示区域  -->
                    <GroupBox
                        Grid.Row="3"
                        Margin="0,10,0,0"
                        Header="系统日志">
                        <RichTextBox
                            x:Name="LogRichTextBox"
                            Height="auto"
                            FontFamily="Consolas"
                            HorizontalScrollBarVisibility="Auto"
                            IsReadOnly="True"
                            VerticalScrollBarVisibility="Auto">
                            <RichTextBox.Document>
                                <FlowDocument>
                                    <Paragraph>
                                        <Run Foreground="Gray" Text="系统日志将显示在这里..." />
                                    </Paragraph>
                                </FlowDocument>
                            </RichTextBox.Document>
                        </RichTextBox>
                    </GroupBox>
                </Grid>
            </Grid>

            <!--  底部状态栏  -->
            <StatusBar Grid.Row="2">
                <StatusBarItem>
                    <TextBlock
                        Foreground="Red"
                        Text="{Binding CurrentState.ErrorMessage}"
                        Visibility="{Binding IsError, Converter={StaticResource BoolToVis}}" />
                </StatusBarItem>
                <StatusBarItem HorizontalAlignment="Right">
                    <TextBlock Text="{Binding CurrentState.LastUpdateTime, StringFormat=最后更新时间: {0:HH:mm:ss}}" />
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </Border>
</UserControl>
