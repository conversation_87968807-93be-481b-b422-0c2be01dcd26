using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using HandyControl.Controls;
using Microsoft.Win32;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Prism.Commands;
using Prism.Mvvm;

namespace Nickel_Inspect.ViewModels
{
    public class LogQueryViewModel : BindableBase
    {
        private readonly ILogService _logService;
        
        // 日志列表
        private ObservableCollection<SystemLog> _logs = new ObservableCollection<SystemLog>();
        public ObservableCollection<SystemLog> Logs
        {
            get => _logs;
            set => SetProperty(ref _logs, value);
        }

        // 过滤条件
        private DateTime _startTime = DateTime.Today.AddDays(-7);
        public DateTime StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        private DateTime _endTime = DateTime.Now;
        public DateTime EndTime
        {
            get => _endTime;
            set => SetProperty(ref _endTime, value);
        }

        // 日志类型过滤
        private bool _showInfo = true;
        public bool ShowInfo
        {
            get => _showInfo;
            set => SetProperty(ref _showInfo, value);
        }

        private bool _showWarning = true;
        public bool ShowWarning
        {
            get => _showWarning;
            set => SetProperty(ref _showWarning, value);
        }

        private bool _showError = true;
        public bool ShowError
        {
            get => _showError;
            set => SetProperty(ref _showError, value);
        }

        private bool _showDebug = false;
        public bool ShowDebug
        {
            get => _showDebug;
            set => SetProperty(ref _showDebug, value);
        }

        // 日志来源过滤
        private ObservableCollection<SourceFilterItem> _sourcesFilter = new ObservableCollection<SourceFilterItem>();
        public ObservableCollection<SourceFilterItem> SourcesFilter
        {
            get => _sourcesFilter;
            set => SetProperty(ref _sourcesFilter, value);
        }

        private string _selectedSource = "全部";
        public string SelectedSource
        {
            get => _selectedSource;
            set => SetProperty(ref _selectedSource, value);
        }

        // 查询状态
        private string _statusMessage = "就绪";
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        private bool _isLoading = false;
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        // 命令
        public DelegateCommand QueryCommand { get; private set; }
        public DelegateCommand ExportCsvCommand { get; private set; }
        public DelegateCommand ClearCommand { get; private set; }
        public DelegateCommand SelectAllTypesCommand { get; private set; }
        public DelegateCommand UnselectAllTypesCommand { get; private set; }
        public DelegateCommand RefreshSourcesCommand { get; private set; }

        public LogQueryViewModel(ILogService logService)
        {
            _logService = logService;

            // 初始化命令
            QueryCommand = new DelegateCommand(ExecuteQuery);
            ExportCsvCommand = new DelegateCommand(ExecuteExportCsv, CanExportCsv)
                .ObservesProperty(() => Logs);
            ClearCommand = new DelegateCommand(ExecuteClear);
            SelectAllTypesCommand = new DelegateCommand(ExecuteSelectAllTypes);
            UnselectAllTypesCommand = new DelegateCommand(ExecuteUnselectAllTypes);
            RefreshSourcesCommand = new DelegateCommand(ExecuteRefreshSources);

            // 加载日志来源列表
            LoadSourcesAsync();

            // 初始加载最近7天的日志
            ExecuteQuery();
        }

        private async void LoadSourcesAsync()
        {
            try
            {
                IsLoading = true;
                
                // 获取所有日志来源
                var sources = await _logService.GetDistinctSourcesAsync();
                
                // 更新UI
                Application.Current.Dispatcher.Invoke(() =>
                {
                    SourcesFilter.Clear();
                    
                    // 添加"全部"选项
                    SourcesFilter.Add(new SourceFilterItem { Name = "全部", IsSelected = true });
                    
                    // 添加其他来源
                    foreach (var source in sources)
                    {
                        SourcesFilter.Add(new SourceFilterItem { Name = source, IsSelected = false });
                    }
                });
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "加载日志来源失败", "系统");
                Growl.Error("加载日志来源失败：" + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExecuteRefreshSources()
        {
            LoadSourcesAsync();
        }

        private async void ExecuteQuery()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在查询...";

                // 获取选中的日志类型
                List<string> logTypes = new List<string>();
                if (ShowInfo) logTypes.Add("信息");
                if (ShowWarning) logTypes.Add("警告");
                if (ShowError) logTypes.Add("错误");
                if (ShowDebug) logTypes.Add("调试");

                // 如果没有选择任何日志类型，显示提示信息
                if (logTypes.Count == 0)
                {
                    Growl.Warning("请至少选择一种日志类型");
                    StatusMessage = "请至少选择一种日志类型";
                    IsLoading = false;
                    return;
                }

                // 获取选中的日志来源
                string sourceFilter = SelectedSource != "全部" ? SelectedSource : null;

                // 查询指定时间范围、类型和来源的日志
                var logs = await _logService.GetLogsByTypeAndDateRangeAsync(
                    logTypes, 
                    StartTime, 
                    EndTime.AddDays(1).AddSeconds(-1),  // 包含结束日期的全天
                    sourceFilter
                );

                // 更新UI
                Application.Current.Dispatcher.Invoke(() =>
                {
                    Logs.Clear();
                    foreach (var log in logs)
                    {
                        Logs.Add(log);
                    }
                    StatusMessage = $"查询完成，共 {Logs.Count} 条记录";
                });
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "查询日志失败", "系统");
                StatusMessage = "查询失败：" + ex.Message;
                Growl.Error("查询日志失败：" + ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanExportCsv()
        {
            return Logs != null && Logs.Count > 0;
        }

        private void ExecuteExportCsv()
        {
            try
            {
                if (Logs.Count == 0)
                {
                    Growl.Warning("没有数据可导出");
                    return;
                }

                // 创建保存文件对话框
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV文件|*.csv",
                    Title = "导出日志",
                    FileName = $"日志_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 创建CSV内容
                    StringBuilder csv = new StringBuilder();
                    
                    // 添加CSV标题行
                    csv.AppendLine("时间,类型,来源,消息");

                    // 添加数据行
                    foreach (var log in Logs)
                    {
                        string timestamp = log.Timestamp.ToString("yyyy-MM-dd HH:mm:ss");
                        string level = log.LogType;
                        string source = log.Source?.Replace(",", "，") ?? ""; // 替换逗号，避免CSV格式错误
                        string message = log.Message?.Replace(",", "，").Replace("\r", " ").Replace("\n", " ") ?? ""; // 替换逗号和换行符

                        csv.AppendLine($"{timestamp},{level},{source},{message}");
                    }

                    // 写入文件
                    File.WriteAllText(saveFileDialog.FileName, csv.ToString(), Encoding.UTF8);

                    StatusMessage = $"成功导出 {Logs.Count} 条记录到 {saveFileDialog.FileName}";
                    Growl.Success("日志导出成功");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "导出日志失败", "系统");
                StatusMessage = "导出失败：" + ex.Message;
                Growl.Error("导出日志失败：" + ex.Message);
            }
        }

        private void ExecuteClear()
        {
            Logs.Clear();
            StatusMessage = "已清空查询结果";
        }

        private void ExecuteSelectAllTypes()
        {
            ShowInfo = true;
            ShowWarning = true;
            ShowError = true;
            ShowDebug = true;
        }

        private void ExecuteUnselectAllTypes()
        {
            ShowInfo = false;
            ShowWarning = false;
            ShowError = false;
            ShowDebug = false;
        }
    }

    public class SourceFilterItem : BindableBase
    {
        private string _name;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        private bool _isSelected;
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }
    }
} 