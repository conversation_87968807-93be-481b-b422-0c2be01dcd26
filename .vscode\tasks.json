{"version": "2.0.0", "tasks": [{"label": "build", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Current\\Bin\\MSBuild.exe", "type": "shell", "args": ["${workspaceFolder}/Nickel_Inspect.csproj", "/p:Configuration=Debug", "/p:Platform=x64"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$msCompile"}, {"label": "run", "command": "${workspaceFolder}/bin/x64/Debug/Nickel_Inspect.exe", "type": "shell", "dependsOn": "build", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": false, "clear": true}}]}