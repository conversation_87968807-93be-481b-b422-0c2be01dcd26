using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using HandyControl.Controls;
using Microsoft.Win32;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.LightControl;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.LightControl;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;

namespace Nickel_Inspect.ViewModels
{
    public class EditInspectPointViewModel : BindableBase, IDialogAware
    {
        private readonly BkMotionCard _modbusService;
        private readonly DatabaseService _databaseService;
        private readonly LightControlService _lightControlService;

        private ProductModel _selectedModel;
        public ProductModel SelectedModel
        {
            get => _selectedModel;
            set => SetProperty(ref _selectedModel, value);
        }

        private int _pointId;
        public int PointId
        {
            get => _pointId;
            set => SetProperty(ref _pointId, value);
        }

        private string _pointName;
        public string PointName
        {
            get => _pointName;
            set => SetProperty(ref _pointName, value);
        }

        private double _currentX;
        public double CurrentX
        {
            get => _currentX;
            set => SetProperty(ref _currentX, value);
        }

        private double _currentY;
        public double CurrentY
        {
            get => _currentY;
            set => SetProperty(ref _currentY, value);
        }

        private double _currentZ;
        public double CurrentZ
        {
            get => _currentZ;
            set => SetProperty(ref _currentZ, value);
        }

        private string _inspectionFilePath;
        public string InspectionFilePath
        {
            get => _inspectionFilePath;
            set => SetProperty(ref _inspectionFilePath, value);
        }

        private bool _isActive;
        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        // 光源控制器ID
        private int _lightControllerId;
        public int LightControllerId
        {
            get => _lightControllerId;
            set => SetProperty(ref _lightControllerId, value);
        }

        // 红色通道亮度值
        private int _redChannelBrightness;
        public int RedChannelBrightness
        {
            get => _redChannelBrightness;
            set => SetProperty(ref _redChannelBrightness, value);
        }

        // 绿色通道亮度值
        private int _greenChannelBrightness;
        public int GreenChannelBrightness
        {
            get => _greenChannelBrightness;
            set => SetProperty(ref _greenChannelBrightness, value);
        }

        // 蓝色通道亮度值
        private int _blueChannelBrightness;
        public int BlueChannelBrightness
        {
            get => _blueChannelBrightness;
            set => SetProperty(ref _blueChannelBrightness, value);
        }

        // 白光通道亮度值
        private int _whiteChannelBrightness;
        public int WhiteChannelBrightness
        {
            get => _whiteChannelBrightness;
            set => SetProperty(ref _whiteChannelBrightness, value);
        }

        // 自定义通道亮度设置
        private string _customChannelSettings;
        public string CustomChannelSettings
        {
            get => _customChannelSettings;
            set => SetProperty(ref _customChannelSettings, value);
        }

        // 光源控制器列表
        private ObservableCollection<LightController> _lightControllers;
        public ObservableCollection<LightController> LightControllers
        {
            get => _lightControllers;
            set => SetProperty(ref _lightControllers, value);
        }

        // 命令定义
        public DelegateCommand RefreshPositionCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand CancelCommand { get; }
        public DelegateCommand BrowseFileCommand { get; }

        // 构造函数
        public EditInspectPointViewModel(
            BkMotionCard modbusService,
            DatabaseService databaseService,
            LightControlService lightControlService
        )
        {
            _databaseService = databaseService;
            _modbusService = modbusService;
            _lightControlService = lightControlService;

            // 初始化命令
            RefreshPositionCommand = new DelegateCommand(ExecuteRefreshPosition);
            SaveCommand = new DelegateCommand(ExecuteSave, CanExecuteSave);
            CancelCommand = new DelegateCommand(ExecuteCancel);
            BrowseFileCommand = new DelegateCommand(ExecuteBrowseFile);

            // 初始化光源相关属性
            LightControllers = new ObservableCollection<LightController>();
            RedChannelBrightness = 0;
            GreenChannelBrightness = 0;
            BlueChannelBrightness = 0;
            WhiteChannelBrightness = 0;
            CustomChannelSettings = "";
        }

        private void RefreshPosition()
        {
            try
            {
                CurrentX = _modbusService.GetCurrentPosition(0);
                CurrentY = _modbusService.GetCurrentPosition(1);
                CurrentZ = _modbusService.GetCurrentPosition(2);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取位置失败：{ex.Message}");
            }
        }

        private void ExecuteRefreshPosition()
        {
            RefreshPosition();
        }

        private bool CanExecuteSave()
        {
            return true;
        }

        private void ExecuteBrowseFile()
        {
            var openFileDialog = new OpenFileDialog();

            openFileDialog.Filter = "VPP Files (*.vpp)|*.vpp|All Files (*.*)|*.*";
            openFileDialog.RestoreDirectory = true;

            if (openFileDialog.ShowDialog() == true)
            {
                InspectionFilePath = openFileDialog.FileName;
            }
        }

        private async void ExecuteSave()
        {
            try
            {
                // 更新检查点对象
                var updatedPoint = new InspectionPoint
                {
                    PointId = PointId,
                    PointName = PointName,
                    PointCode = PointName,
                    ModelId = SelectedModel.ModelId,
                    XPosition = (decimal)CurrentX,
                    YPosition = (decimal)CurrentY,
                    ZPosition = (decimal)CurrentZ,
                    IsActive = IsActive,
                    InspectionFilePath = InspectionFilePath,
                    // 添加光源相关属性
                    LightControllerId = LightControllerId,
                    RedChannelBrightness = RedChannelBrightness,
                    GreenChannelBrightness = GreenChannelBrightness,
                    BlueChannelBrightness = BlueChannelBrightness,
                    WhiteChannelBrightness = WhiteChannelBrightness,
                    CustomChannelSettings = CustomChannelSettings,
                };

                // 调用数据库服务更新
                await _databaseService.UpdateInspectionPointAsync(updatedPoint);

                // 关闭窗口
                RequestClose?.Invoke(new DialogResult(ButtonResult.OK));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}");
            }
        }

        private void ExecuteCancel()
        {
            RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
        }

        // IDialogAware 接口实现
        public string Title => "编辑检查点";

        public event Action<IDialogResult> RequestClose;

        public bool CanCloseDialog()
        {
            return true;
        }

        public void OnDialogClosed() { }

        public void OnDialogOpened(IDialogParameters parameters)
        {
            // 初始化光源控制器列表
            LoadLightControllers();

            if (parameters.ContainsKey("Point"))
            {
                var point = parameters.GetValue<InspectionPoint>("Point");
                PointId = point.PointId;
                PointName = point.PointName;
                CurrentX = (double)point.XPosition;
                CurrentY = (double)point.YPosition;
                CurrentZ = (double)point.ZPosition;
                IsActive = point.IsActive;
                InspectionFilePath = point.InspectionFilePath;

                // 设置光源相关属性
                LightControllerId = point.LightControllerId;
                RedChannelBrightness = point.RedChannelBrightness;
                GreenChannelBrightness = point.GreenChannelBrightness;
                BlueChannelBrightness = point.BlueChannelBrightness;
                WhiteChannelBrightness = point.WhiteChannelBrightness;
                CustomChannelSettings = point.CustomChannelSettings;
            }

            if (parameters.ContainsKey("Model"))
            {
                SelectedModel = parameters.GetValue<ProductModel>("Model");
            }
        }

        private void LoadLightControllers()
        {
            try
            {
                // 临时创建一些光源控制器供选择
                // 这里可以替换为实际从数据库或服务获取的控制器列表
                //var controllers = new List<LightController>
                //{
                //    new LightController { Id = 1, Name = "控制器1" },
                //    new LightController { Id = 2, Name = "控制器2" },
                //    new LightController { Id = 3, Name = "控制器3" }
                //};
                var controllers = _lightControlService.GetAvailableControllersAsync().Result;

                LightControllers = new ObservableCollection<LightController>(controllers);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载光源控制器失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }
    }
}
