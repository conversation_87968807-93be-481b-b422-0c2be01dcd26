using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using HandyControl.Controls;
using HandyControl.Tools;
using Newtonsoft.Json;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.Alarms;
using Nickel_Inspect.Services.Track;
using Nickel_Inspect.ViewModels;
using Nickel_Inspect.Views;
using Nickel_Inspect.Views.Dialogs;
using Prism.Commands;
using Prism.Ioc;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;

namespace Nickel_Inspect.ViewModels
{
    public class MainWindowViewModel : BindableBase
    {
        MachineConfigurationHelper machineConfigurationHelper; //硬件配置参数  记录轴和IO配置

        private string title = "镍片检查";
        public string Title
        {
            get { return title; }
            set { SetProperty(ref title, value); }
        }

        private readonly BkMotionCard _bkMottion;
        private readonly ModbusConfig _modbusConfig;
        private readonly DatabaseService _databaseService;
        private ITrackService _trackService;
        private ObservableCollection<ProductModel> _productModels;
        private ProductModel _selectedModel;
        private ObservableCollection<InspectionPoint> _inspectionPoints;
        private readonly IDialogService _dialogService;
        private DispatcherTimer _positionUpdateTimer;
        private readonly ILogService _logService;
        private readonly IInspectionService _inspectionService;
        private readonly IVisionProService _visionProService;
        private readonly VisionProTestViewModel _visionProTestViewModel;
        private ConfigPointViewModel _configPointViewModel;
        private LogQueryViewModel _logQueryViewModel;
        private readonly StatusManager _statusManager; // 替换IIndicatorService为StatusManager
        private readonly DispatcherTimer _statusTimer;
        private string _statusText = "系统就绪";
        private bool _isRunning = false;
        private bool _isBusy = false;
        private LogEventArgs _latestLog;
        private ObservableCollection<SystemLog> _logItems = new ObservableCollection<SystemLog>();

        // 三轴位置属性
        private double _xAxisPosition;
        public double XAxisPosition
        {
            get => _xAxisPosition;
            set => SetProperty(ref _xAxisPosition, value);
        }

        private double _yAxisPosition;
        public double YAxisPosition
        {
            get => _yAxisPosition;
            set => SetProperty(ref _yAxisPosition, value);
        }

        private double _zAxisPosition;
        public double ZAxisPosition
        {
            get => _zAxisPosition;
            set => SetProperty(ref _zAxisPosition, value);
        }

        public MainWindowViewModel(
            BkMotionCard bkMottion,
            IDialogService dialogService,
            ITrackService trackService,
            InspectionViewModel inspectionViewModel,
            ILogService logService,
            IInspectionService inspectionService,
            IVisionProService visionProService,
            VisionProTestViewModel visionProTestViewModel,
            ConfigPointViewModel configPointViewModel,
            LogQueryViewModel logQueryViewModel,
            ModbusConfig modbusConfig,
            StatusManager statusManager
        )
        {
            _bkMottion = bkMottion;
            _dialogService = dialogService;
            _trackService = trackService;
            InspectionViewModel = inspectionViewModel;
            _logService = logService;
            _inspectionService = inspectionService;
            _visionProService = visionProService;
            VisionProTestViewModel = visionProTestViewModel;
            ConfigPointViewModel = configPointViewModel;
            LogQueryViewModel = logQueryViewModel;
            _statusManager = statusManager;

            // ModbusConfig通过依赖注入获取
            _modbusConfig = modbusConfig;

            _databaseService = new DatabaseService();

            // 初始化IO监控视图模型
            IOMonitorViewModel = new IOMonitorViewModel(_bkMottion, _modbusConfig);

            LoadProductModelsCommand = new DelegateCommand(async () => await LoadProductModels());
            AddProductModelCommand = new DelegateCommand(AddProductModel);

            StartJogCommand = new DelegateCommand<string>(ExecuteStartJog);
            StopJogCommand = new DelegateCommand<string>(ExecuteStopJog);
            HomeAxisCommand = new DelegateCommand<string>(ExecuteHomeAxis);

            ShowAddPointDialogCommand = new DelegateCommand(
                ExecuteShowAddPointDialog,
                CanExcuteShow
            ).ObservesProperty(() => SelectedModel);

            // 添加Tab切换命令
            TabSelectionChangedCommand =
                new DelegateCommand<System.Windows.Controls.SelectionChangedEventArgs>(
                    OnTabSelectionChanged
                );

            InitializeCommands();

            // 初始化位置更新定时器
            InitializePositionUpdateTimer();

            // 初始化机器配置
            InitializeMachineConfiguration();

            JogSpeedToggled = 10;

            // 初始化VisionPro服务
            InitializeVisionPro();

            // 设置系统初始状态指示灯（待机状态）
            Task.Run(async () => await _statusManager.SetStandbyState());

            // 初始化状态更新计时器
            _statusTimer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(1) };
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();

            // 加载最近的日志记录
            Task.Run(async () =>
            {
                try
                {
                    var logs = await _logService.GetRecentLogsAsync(100);
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _logItems.Clear();
                        foreach (var log in logs)
                        {
                            _logItems.Add(log);
                        }
                    });
                }
                catch (Exception ex)
                {
                    _logService.LogError(ex, "加载日志记录失败", "系统");
                }
            });
        }

        private async void InitializeMachineConfiguration()
        {
            try
            {
                // 从配置文件加载机器配置
                MachineConfigurationHelper.Initialize("DeviceConfiguration.json");
                machineConfigurationHelper = MachineConfigurationHelper.Instance;

                // 初始化检查服务
                _inspectionService.Initialize(machineConfigurationHelper);

                // 设置InspectionViewModel的MachineConfigurationHelper
                if (InspectionViewModel != null)
                {
                    // 使用反射调用SetMachineConfigurationHelper方法
                    var method = InspectionViewModel
                        .GetType()
                        .GetMethod("SetMachineConfigurationHelper");
                    if (method != null)
                    {
                        method.Invoke(
                            InspectionViewModel,
                            new object[] { machineConfigurationHelper }
                        );
                    }
                }

                // 设置轨道服务的MachineConfigurationHelper
                if (_trackService != null && _trackService is TrackService trackService)
                {
                    // 使用反射调用SetMachineConfiguration方法
                    var method = trackService.GetType().GetMethod("SetMachineConfiguration");
                    if (method != null)
                    {
                        method.Invoke(trackService, new object[] { machineConfigurationHelper });
                    }
                }

                // 记录配置加载完成日志
                _logService.LogInformation("机器配置初始化完成", "主窗口");
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "加载机器配置失败", "主窗口");
                MessageBox.Show(
                    $"加载机器配置失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        public IOMonitorViewModel IOMonitorViewModel { get; }
        public InspectionViewModel InspectionViewModel { get; private set; }
        public VisionProTestViewModel VisionProTestViewModel { get; }
        public ConfigPointViewModel ConfigPointViewModel { get; }
        public LogQueryViewModel LogQueryViewModel { get; }

        public ObservableCollection<ProductModel> ProductModels
        {
            get => _productModels;
            set => SetProperty(ref _productModels, value);
        }

        public ProductModel SelectedModel
        {
            get => _selectedModel;
            set
            {
                if (SetProperty(ref _selectedModel, value))
                {
                    LoadInspectionPoints();
                }
            }
        }

        public ObservableCollection<InspectionPoint> InspectionPoints
        {
            get => _inspectionPoints;
            set => SetProperty(ref _inspectionPoints, value);
        }

        public DelegateCommand LoadProductModelsCommand { get; }
        public DelegateCommand AddProductModelCommand { get; }

        private async Task LoadProductModels()
        {
            try
            {
                var models = await _databaseService.GetProductModelsAsync();
                ProductModels = new ObservableCollection<ProductModel>(models);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载机种失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async void LoadInspectionPoints()
        {
            if (SelectedModel == null)
                return;

            try
            {
                var points = await _databaseService.GetInspectionPointsAsync(SelectedModel.ModelId);
                InspectionPoints = new ObservableCollection<InspectionPoint>(points);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载检查点失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private void AddProductModel()
        {
            _dialogService.ShowDialog(
                "NewProductWindow",
                null,
                r =>
                {
                    if (r.Result == ButtonResult.OK)
                    {
                        // 成功添加后刷新列表
                        LoadProductModelsCommand.Execute();
                    }
                }
            );
        }

        #region JOG运动相关

        private int _jogSpeedToggled;
        public int JogSpeedToggled
        {
            get { return _jogSpeedToggled; }
            set
            {
                SetProperty(ref _jogSpeedToggled, value);
                AxisConfig axisConfig = machineConfigurationHelper.GetAxisConfig("X");
                JogSpeed = axisConfig.JogMaxSpeed * JogSpeedToggled / 100.0;
            }
        }

        private double jogSpeed;
        public double JogSpeed
        {
            get { return jogSpeed; }
            set { SetProperty(ref jogSpeed, value); }
        }

        public DelegateCommand<string> StartJogCommand { get; }
        public DelegateCommand<string> StopJogCommand { get; }
        public DelegateCommand<string> HomeAxisCommand { get; }

        /// <summary>
        /// Jog启动命令执行方法
        /// </summary>
        /// <param name="parameter">参数在xmal中定义， 有X+  X-  Y+  Y-  Z+  Z- 6种可选项</param>
        private void ExecuteStartJog(string parameter)
        {
            AxisConfig axisConfig = machineConfigurationHelper.GetAxisConfig(
                parameter[0].ToString()
            );
            UpdateJogSpeed(axisConfig);
            switch (parameter)
            {
                case "X+":
                    _bkMottion.MoveAxisJogForward(axisConfig.AxisNo);
                    break;
                case "X-":
                    _bkMottion.MoveAxisJogBackward(axisConfig.AxisNo);
                    break;
                case "Y+":
                    _bkMottion.MoveAxisJogForward(axisConfig.AxisNo);
                    break;
                case "Y-":
                    _bkMottion.MoveAxisJogBackward(axisConfig.AxisNo);
                    break;
                case "Z+":
                    _bkMottion.MoveAxisJogForward(axisConfig.AxisNo);
                    break;
                case "Z-":
                    _bkMottion.MoveAxisJogBackward(axisConfig.AxisNo);
                    break;
            }
        }

        // Jog停止命令执行方法
        private void ExecuteStopJog(string axis)
        {
            //此函数传参在xaml中定义， 只有 X Y Z 三种参数， 单个字符
            AxisConfig axisConfig = machineConfigurationHelper.GetAxisConfig(axis);
            _bkMottion.StopAxisJog(axisConfig.AxisNo);
        }

        /// <summary>
        /// 执行轴复位命令
        /// </summary>
        /// <param name="axis">轴标识（X、Y、Z）</param>
        private void ExecuteHomeAxis(string axis)
        {
            try
            {
                AxisConfig axisConfig = machineConfigurationHelper.GetAxisConfig(axis);
                _bkMottion.HomeAxis(axisConfig.AxisNo);
                MessageBox.Show(
                    $"{axis}轴复位操作已启动",
                    "提示",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"{axis}轴复位失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        /// <summary>
        /// 更新Jog速度
        /// </summary>
        /// <param name="axisConfig"></param>
        private void UpdateJogSpeed(AxisConfig axisConfig)
        {
            JogSpeed = axisConfig.JogMaxSpeed * JogSpeedToggled / 100.0;
            _bkMottion.SetStartSpeed(axisConfig.AxisNo, (int)axisConfig.StartSpeed);
            _bkMottion.SetMaxSpeed(axisConfig.AxisNo, (int)axisConfig.JogMaxSpeed);
        }

        #endregion



        #region 检查点运动相关


        private DelegateCommand<InspectionPoint> goCommand;
        public DelegateCommand<InspectionPoint> CommandGo =>
            goCommand ?? (goCommand = new DelegateCommand<InspectionPoint>(ExecuteCommandGo));

        void ExecuteCommandGo(InspectionPoint parameter)
        {
            UpdateRunSpeed();
            _bkMottion.SetTargetPosition(0, (double)parameter.XPosition);
            _bkMottion.SetTargetPosition(1, (double)parameter.YPosition);
            _bkMottion.SetTargetPosition(2, (double)parameter.ZPosition);
        }

        private void UpdateRunSpeed()
        {
            var allAxes = machineConfigurationHelper.GetAllAxes();
            foreach (var axisConfig in machineConfigurationHelper.GetAllAxes())
            {
                _bkMottion.SetStartSpeed(axisConfig.AxisNo, (int)axisConfig.StartSpeed);
                _bkMottion.SetMaxSpeed(axisConfig.AxisNo, (int)axisConfig.RunSpeed);
            }
        }
        #endregion

        #region 新增检查点相关
        public DelegateCommand ShowAddPointDialogCommand { get; }

        private void ExecuteShowAddPointDialog()
        {
            var parameters = new DialogParameters();
            parameters.Add("ProductModel", SelectedModel); // 传入当前选中的机种

            _dialogService.ShowDialog(
                "AddInspectPoint",
                parameters,
                result =>
                {
                    if (result.Result == ButtonResult.OK)
                    {
                        // 处理新增完成后的逻辑
                        LoadInspectionPoints(); // 重新加载检查点列表
                    }
                }
            );
        }

        private bool CanExcuteShow()
        {
            return SelectedModel != null;
        }
        #endregion

        public DelegateCommand<System.Windows.Controls.SelectionChangedEventArgs> TabSelectionChangedCommand { get; }

        private void OnTabSelectionChanged(System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (
                e.AddedItems.Count > 0
                && e.AddedItems[0] is System.Windows.Controls.TabItem selectedTab
            )
            {
                string header = selectedTab.Header?.ToString();

                // 处理检查点配置页面的逻辑
                if (header != "检查点配置" && ConfigPointViewModel != null)
                {
                    ConfigPointViewModel.OnNavigatedFrom();
                }

                // 处理IO监控的启用/禁用
                if (selectedTab.Tag?.ToString() == "SystemSettings")
                {
                    IOMonitorViewModel.Enable();
                }
                else
                {
                    IOMonitorViewModel.Disable();
                }

                _logService.LogInformation($"已切换到：{header}", "主窗口");
            }
        }

        #region Commands

        public DelegateCommand OpenTrackControlCommand { get; private set; }
        public DelegateCommand ExitCommand { get; private set; }

        private void InitializeCommands()
        {
            OpenTrackControlCommand = new DelegateCommand(ExecuteOpenTrackControl);
            ExitCommand = new DelegateCommand(ExecuteExit);
        }

        /// <summary>
        /// 确认是否退出应用程序
        /// </summary>
        public void ConfirmExit()
        {
            // 显示确认对话框
            var result = MessageBox.Show(
                "确定要退出应用程序吗？",
                "退出确认",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question
            );

            // 如果用户确认退出，则执行退出操作
            if (result == MessageBoxResult.Yes)
            {
                ExecuteExit();
            }
            // 否则什么都不做，程序继续运行
        }

        private void ExecuteOpenTrackControl()
        {
            _dialogService.ShowDialog(
                "TrackControlView",
                null,
                result =>
                {
                    // 如果对话框关闭，并且TrackControlViewModel有日志，则将日志传递给InspectionViewModel
                    if (
                        result.Parameters != null
                        && result.Parameters.ContainsKey("TrackViewModel")
                        && result.Parameters.GetValue<TrackControlViewModel>("TrackViewModel")
                            is TrackControlViewModel trackViewModel
                    )
                    {
                        InspectionViewModel?.ImportLogsFromTrackViewModel(trackViewModel);
                    }
                }
            );
        }

        private void ExecuteExit()
        {
            // 创建并显示关闭进度对话框
            var shutdownDialog = new Views.Dialogs.ShutdownProgressDialog();
            shutdownDialog.Owner = Application.Current.MainWindow;
            shutdownDialog.Show();

            // 使用Task执行关闭操作，避免UI卡顿
            Task.Run(async () =>
            {
                try
                {
                    _logService.LogInformation("应用程序正在退出...", "主窗口");

                    // 更新进度对话框
                    shutdownDialog.UpdateStatus("正在释放系统资源...");
                    await Task.Delay(500);

                    // 关闭视觉服务
                    shutdownDialog.UpdateStatus("正在关闭视觉服务...");
                    try
                    {
                        if (_visionProService != null)
                        {
                            _visionProService.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.LogError(ex, "关闭视觉服务时发生错误", "主窗口");
                    }
                    await Task.Delay(500);

                    // 关闭运动控制系统
                    shutdownDialog.UpdateStatus("正在关闭运动控制系统...");
                    await Task.Delay(500);

                    // 关闭所有指示灯
                    if (_statusManager != null)
                    {
                        shutdownDialog.UpdateStatus("正在关闭指示灯...");
                        // 使用强制清理方法，确保所有指示灯关闭
                        _statusManager.Dispose();
                    }
                    await Task.Delay(500);

                    // 最终清理
                    shutdownDialog.UpdateStatus("正在完成最终清理...");
                    await Task.Delay(800);

                    _logService.LogInformation("应用程序退出完成", "主窗口");

                    // 在UI线程关闭应用程序
                    // 避免在Task.Run中使用Application.Current可能为null的问题
                    var dispatcher = shutdownDialog.Dispatcher;
                    dispatcher.Invoke(() =>
                    {
                        try
                        {
                            // 先关闭对话框
                            shutdownDialog.Close();

                            // 判断Application.Current是否为null
                            if (Application.Current != null)
                            {
                                Application.Current.Shutdown();
                            }
                            else
                            {
                                // 如果Application.Current为null，使用Environment.Exit作为备选方案
                                Environment.Exit(0);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logService.LogError(ex, "关闭应用程序时发生错误", "主窗口");
                            Environment.Exit(0); // 确保程序能退出
                        }
                    });
                }
                catch (Exception ex)
                {
                    _logService.LogError(ex, "退出程序时清理资源失败", "主窗口");

                    try
                    {
                        // 使用Dialog的Dispatcher而不是Application.Current
                        shutdownDialog.Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show(
                                $"退出过程中发生错误: {ex.Message}",
                                "错误",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error
                            );
                            shutdownDialog.Close();

                            // 使用Environment.Exit作为备选方案
                            Environment.Exit(0);
                        });
                    }
                    catch
                    {
                        // 如果一切都失败了，强制退出
                        Environment.Exit(1);
                    }
                }
            });
        }

        #endregion

        #region 位置更新相关
        /// <summary>
        /// 初始化位置更新定时器
        /// </summary>
        private void InitializePositionUpdateTimer()
        {
            _positionUpdateTimer = new DispatcherTimer();
            _positionUpdateTimer.Interval = TimeSpan.FromMilliseconds(600); // 设置刷新周期为500ms
            _positionUpdateTimer.Tick += (sender, e) => UpdateAxisPositions();
            _positionUpdateTimer.Start();
        }

        /// <summary>
        /// 更新三轴位置
        /// </summary>
        private void UpdateAxisPositions()
        {
            try
            {
                // 获取X轴配置和当前位置
                AxisConfig xAxisConfig = machineConfigurationHelper.GetAxisConfig("X");
                double xPosition = _bkMottion.GetCurrentPosition(xAxisConfig.AxisNo);
                XAxisPosition = xPosition;

                // 获取Y轴配置和当前位置
                AxisConfig yAxisConfig = machineConfigurationHelper.GetAxisConfig("Y");
                double yPosition = _bkMottion.GetCurrentPosition(yAxisConfig.AxisNo);
                YAxisPosition = yPosition;

                // 获取Z轴配置和当前位置
                AxisConfig zAxisConfig = machineConfigurationHelper.GetAxisConfig("Z");
                double zPosition = _bkMottion.GetCurrentPosition(zAxisConfig.AxisNo);
                ZAxisPosition = zPosition;
            }
            catch (Exception ex)
            {
                // 发生异常时停止定时器，避免持续报错
                _positionUpdateTimer.Stop();
                MessageBox.Show(
                    $"获取轴位置失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }
        #endregion

        private void InitializeVisionPro()
        {
            try
            {
                // 初始化VisionPro服务
                if (!_visionProService.Initialize())
                {
                    MessageBox.Show(
                        "VisionPro服务初始化失败",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                    return;
                }

                // 加载VPP文件
                //string vppFilePath = @"D:\ProjestFiles\缺陷检测\VisionPro文件\AcqImageOnly.vpp";
                //if (!_visionProService.LoadToolBlock(vppFilePath))
                //{
                //    MessageBox.Show(
                //        $"加载VPP文件失败: {vppFilePath}",
                //        "错误",
                //        MessageBoxButton.OK,
                //        MessageBoxImage.Error
                //    );
                //}
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"初始化VisionPro失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        public bool IsRunning
        {
            get => _isRunning;
            set => SetProperty(ref _isRunning, value);
        }

        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        public LogEventArgs LatestLog
        {
            get => _latestLog;
            set => SetProperty(ref _latestLog, value);
        }

        public ObservableCollection<SystemLog> LogItems
        {
            get => _logItems;
            set => SetProperty(ref _logItems, value);
        }

        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            // 更新状态信息，可以在这里添加其他状态信息
            StatusText = $"系统就绪 - {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}";
        }
    }
}
