using System;
using System.Threading.Tasks;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.Track;

namespace Nickel_Inspect.Services.Track
{
    /// <summary>
    /// 轨道服务接口
    /// </summary>
    public interface ITrackService : IDisposable
    {
        /// <summary>
        /// 获取当前轨道状态
        /// </summary>
        TrackStateData CurrentState { get; }

        /// <summary>
        /// 启动轨道
        /// </summary>
        Task StartAsync();

        /// <summary>
        /// 停止轨道
        /// </summary>
        Task StopAsync();

        /// <summary>
        /// 暂停轨道
        /// </summary>
        Task PauseAsync();

        /// <summary>
        /// 继续运行
        /// </summary>
        Task ResumeAsync();

        /// <summary>
        /// 紧急停止
        /// </summary>
        Task EmergencyStopAsync();

        /// <summary>
        /// 复位
        /// </summary>
        Task ResetAsync();

        /// <summary>
        /// 设置运行模式
        /// </summary>
        Task SetModeAsync(TrackMode mode);

        /// <summary>
        /// 设置运行方向
        /// </summary>
        Task SetDirectionAsync(TrackDirection direction);

        /// <summary>
        /// 状态改变事件
        /// </summary>
        event EventHandler<TrackStateData> StateChanged;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<string> ErrorOccurred;

        /// <summary>
        /// 警告发生事件
        /// </summary>
        event EventHandler<string> WarningOccurred;

        /// <summary>
        /// 消息记录事件
        /// </summary>
        event EventHandler<string> MessageLogged;

        /// <summary>
        /// 产品就位事件，当产品到达检查位置时触发
        /// </summary>
        event EventHandler<BoardArrivedEventArgs> BoardArrived;

        /// <summary>
        /// 通知轨道服务检查已完成
        /// </summary>
        /// <param name="isPass">检查是否通过</param>
        /// <returns>操作任务</returns>
        Task NotifyInspectionCompletedAsync(bool isPass);

        /// <summary>
        /// 控制阻挡气缸
        /// </summary>
        /// <param name="extend">true表示伸出，false表示缩回</param>
        Task ControlStopperAsync(bool extend);

        /// <summary>
        /// 控制夹紧气缸
        /// </summary>
        /// <param name="clamp">true表示夹紧，false表示松开</param>
        Task ControlClampAsync(bool clamp);

        /// <summary>
        /// 设置SMEMA功能启用状态
        /// </summary>
        /// <param name="enabled">是否启用SMEMA功能</param>
        Task SetSmemaEnabledAsync(bool enabled);

        /// <summary>
        /// 设置调试模式状态
        /// </summary>
        /// <param name="enabled">是否启用调试模式</param>
        Task SetDebugModeAsync(bool enabled);

        /// <summary>
        /// 手动控制皮带运动
        /// </summary>
        /// <param name="direction">运动方向，1表示正向，-1表示反向，0表示停止</param>
        /// <param name="speed">运动速度</param>
        Task ManualControlBeltAsync(int direction, int speed);

        /// <summary>
        /// 手动控制调宽轴运动
        /// </summary>
        /// <param name="direction">运动方向，1表示正向，-1表示反向，0表示停止</param>
        /// <param name="speed">运动速度</param>
        Task ManualControlWidthAxisAsync(int direction, int speed);

        /// <summary>
        /// 调宽轴回零
        /// </summary>
        Task HomeWidthAxisAsync();

        /// <summary>
        /// 设置皮带速度
        /// </summary>
        /// <param name="speed">速度值</param>
        Task SetBeltSpeedAsync(int speed);

        /// <summary>
        /// 设置调宽轴速度
        /// </summary>
        /// <param name="speed">速度值</param>
        Task SetWidthAxisSpeedAsync(int speed);

        /// <summary>
        /// 设置入料传感器状态（仅调试模式）
        /// </summary>
        Task SetBoardInSensorAsync(bool state);

        /// <summary>
        /// 设置到位传感器状态（仅调试模式）
        /// </summary>
        Task SetBoardArrivedSensorAsync(bool state);

        /// <summary>
        /// 设置出料传感器状态（仅调试模式）
        /// </summary>
        Task SetBoardOutSensorAsync(bool state);

        /// <summary>
        /// 设置阻挡气缸状态（仅调试模式）
        /// </summary>
        Task SetStopperCylinderAsync(bool state);

        /// <summary>
        /// 设置夹紧气缸状态（仅调试模式）
        /// </summary>
        Task SetClampCylinderAsync(bool state);

        /// <summary>
        /// 设置下游准备好状态（仅调试模式）
        /// </summary>
        Task SetNextMachineReadyAsync(bool state);

        /// <summary>
        /// 设置本机有板状态（仅调试模式）
        /// </summary>
        Task SetHasBoardAsync(bool state);

        /// <summary>
        /// 设置本机准备好状态（仅调试模式）
        /// </summary>
        Task SetReadyToReceiveAsync(bool state);

        /// <summary>
        /// 设置机器配置助手
        /// </summary>
        /// <param name="machineConfig">机器配置助手实例</param>
        void SetMachineConfiguration(MachineConfigurationHelper machineConfig);

        /// <summary>
        /// 设置离线模拟模式
        /// </summary>
        /// <param name="enabled">是否启用离线模拟模式</param>
        void SetOfflineSimulationMode(bool enabled);

        /// <summary>
        /// 手动入料
        /// </summary>
        /// <returns></returns>
        Task ManualBoardInAsync();

        /// <summary>
        /// 手动出料
        /// </summary>
        /// <returns></returns>
        Task ManualBoardOutAsync();
    }
}
