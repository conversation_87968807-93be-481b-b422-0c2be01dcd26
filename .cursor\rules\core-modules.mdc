---
description: 
globs: 
alwaysApply: false
---
# 景旺镍片检查系统核心模块

## 核心模块说明

### 1. 轨道控制模块
- **功能**：负责PCB板的传入、定位、传出
- **关键文件**：
  - [Services/Track/TrackService.cs](mdc:Services/Track/TrackService.cs) - 轨道控制服务实现
  - [Services/Track/ITrackService.cs](mdc:Services/Track/ITrackService.cs) - 轨道控制服务接口
  - [ViewModels/TrackControlViewModel.cs](mdc:ViewModels/TrackControlViewModel.cs) - 轨道控制视图模型

### 2. 视觉检测模块
- **功能**：负责图像采集和缺陷检测
- **关键文件**：
  - [Services/VisionProService.cs](mdc:Services/VisionProService.cs) - 视觉服务实现
  - [Services/IVisionProService.cs](mdc:Services/IVisionProService.cs) - 视觉服务接口

### 3. 运动控制模块
- **功能**：负责XYZ轴的控制和相机定位
- **关键文件**：
  - [Services/BkMotionCard.cs](mdc:Services/BkMotionCard.cs) - 运动控制卡服务

### 4. 状态管理模块
- **功能**：负责设备状态监控和管理
- **关键文件**：
  - [Services/StatusManager.cs](mdc:Services/StatusManager.cs) - 状态管理器

### 5. 检测服务模块
- **功能**：协调相机移动、图像采集和缺陷检测流程
- **关键文件**：
  - [Services/InspectionService.cs](mdc:Services/InspectionService.cs) - 检测服务实现
  - [Services/IInspectionService.cs](mdc:Services/IInspectionService.cs) - 检测服务接口
  - [ViewModels/InspectionViewModel.cs](mdc:ViewModels/InspectionViewModel.cs) - 检测视图模型

### 6. 光源控制模块
- **功能**：管理和控制光源设备
- **关键文件**：
  - [Services/LightControl/LightControlService.cs](mdc:Services/LightControl/LightControlService.cs) - 光源控制服务实现
  - [Services/LightControl/ILightControlService.cs](mdc:Services/LightControl/ILightControlService.cs) - 光源控制服务接口
  - [Services/LightControl/LightControllerBase.cs](mdc:Services/LightControl/LightControllerBase.cs) - 光源控制器基类

### 7. 数据存储模块
- **功能**：负责检测数据和图像的存储
- **关键文件**：
  - [Services/DatabaseService.cs](mdc:Services/DatabaseService.cs) - 数据库服务

### 8. 报警系统模块
- **功能**：负责监控、处理和管理各类报警信号
- **关键文件**：
  - [Services/Alarms/AlarmManager.cs](mdc:Services/Alarms/AlarmManager.cs) - 报警管理器

## 主要工作流程

### 检测流程
1. 用户通过UI选择产品型号并点击开始
2. 轨道控制模块控制载具进入检测区域
3. 当载具到位后，检测服务从数据库读取该产品型号下的所有激活点位
4. 对每个检测点：
   - 运动控制模块控制XYZ轴移动相机到指定点位
   - 光源控制模块根据点位设置调整光源
   - 视觉检测模块采集图像并进行分析
   - 检测结果实时显示并记录
5. 所有点位检测完成后，生成综合判定结果
6. 检测服务通知轨道控制模块检测完成
7. 载具出料，等待下一块PCB板

### 配置流程
1. 用户通过配置界面添加/编辑产品型号
2. 针对特定产品型号，用户可添加/编辑多个检测点
3. 对每个检测点，用户可配置：
   - XYZ坐标位置
   - 关联的视觉工具块文件(.vpp)
   - 光源设置（亮度、通道等）
   - 其他参数（曝光时间等）
4. 配置完成后通过数据库服务保存

