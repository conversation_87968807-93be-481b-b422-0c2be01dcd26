<UserControl
    x:Class="Nickel_Inspect.Views.AddInspectPoint"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:prism="http://prismlibrary.com/"
    Width="800"
    Height="700"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="{DynamicResource RegionBrush}"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDark.xaml" />
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="{DynamicResource RegionBrush}" CornerRadius="5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题  -->
            <TextBlock
                Margin="0,0,0,20"
                Foreground="{DynamicResource PrimaryTextBrush}"
                Style="{StaticResource TextBlockLargeBold}"
                Text="新增检查点" />

            <!--  表单内容  -->
            <StackPanel Grid.Row="1" Margin="0,0,0,20">
                <!--  机种选择  -->
                <DockPanel Margin="0,5">
                    <TextBlock
                        Width="100"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="机种名称：" />
                    <TextBlock Foreground="{DynamicResource PrimaryTextBrush}" Text="{Binding SelectedModel.ModelName}" />
                </DockPanel>

                <!--  点位名称  -->
                <DockPanel Margin="0,5">
                    <TextBlock
                        Width="100"
                        VerticalAlignment="Center"
                        Foreground="{DynamicResource PrimaryTextBrush}"
                        Text="点位名称：" />
                    <TextBox Style="{StaticResource TextBoxBaseStyle}" Text="{Binding PointName}" />
                </DockPanel>

                <!--  当前坐标  -->
                <GroupBox
                    Margin="0,10"
                    Header="当前坐标"
                    Style="{StaticResource GroupBoxBaseStyle}">
                    <Grid Margin="0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            Margin="0,0,5,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="X:" />
                        <TextBlock
                            Grid.Column="1"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Style="{StaticResource TextBlockBaseStyle}"
                            Text="{Binding CurrentX}" />

                        <TextBlock
                            Grid.Column="2"
                            Margin="10,0,5,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="Y:" />
                        <TextBlock
                            Grid.Column="3"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Style="{StaticResource TextBlockBaseStyle}"
                            Text="{Binding CurrentY}" />

                        <TextBlock
                            Grid.Column="4"
                            Margin="10,0,5,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Text="Z:" />
                        <TextBlock
                            Grid.Column="5"
                            Foreground="{DynamicResource PrimaryTextBrush}"
                            Style="{StaticResource TextBlockBaseStyle}"
                            Text="{Binding CurrentZ}" />
                    </Grid>
                </GroupBox>

                <!--  光源设置  -->
                <GroupBox Margin="0,10" Header="光源设置">
                    <StackPanel Margin="0,5">
                        <!--  光源控制器选择  -->
                        <DockPanel Margin="0,5">
                            <TextBlock
                                Width="100"
                                VerticalAlignment="Center"
                                Text="光源控制器：" />
                            <ComboBox
                                DisplayMemberPath="Name"
                                ItemsSource="{Binding LightControllers}"
                                SelectedItem="{Binding SelectedLightController}" />
                            <!--<TextBlock Text="{Binding SelectedLightController.Name}" />-->
                        </DockPanel>

                        <!--  RGB亮度设置  -->
                        <GroupBox Margin="0,5" Header="RGB和白光亮度">
                            <StackPanel>
                                <!--  红色通道亮度  -->
                                <DockPanel Margin="0,5">
                                    <TextBlock
                                        Width="80"
                                        VerticalAlignment="Center"
                                        Text="红色通道：" />
                                    <TextBlock
                                        Width="40"
                                        Margin="0,0,5,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding RedChannelBrightness}" />
                                    <Slider
                                        Maximum="255"
                                        Minimum="0"
                                        Style="{StaticResource SliderBaseStyle}"
                                        Value="{Binding RedChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DockPanel>

                                <!--  绿色通道亮度  -->
                                <DockPanel Margin="0,5">
                                    <TextBlock
                                        Width="80"
                                        VerticalAlignment="Center"
                                        Text="绿色通道：" />
                                    <TextBlock
                                        Width="40"
                                        Margin="0,0,5,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding GreenChannelBrightness}" />
                                    <Slider
                                        Maximum="255"
                                        Minimum="0"
                                        Style="{StaticResource SliderBaseStyle}"
                                        Value="{Binding GreenChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DockPanel>

                                <!--  蓝色通道亮度  -->
                                <DockPanel Margin="0,5">
                                    <TextBlock
                                        Width="80"
                                        VerticalAlignment="Center"
                                        Text="蓝色通道：" />
                                    <TextBlock
                                        Width="40"
                                        Margin="0,0,5,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding BlueChannelBrightness}" />
                                    <Slider
                                        Maximum="255"
                                        Minimum="0"
                                        Style="{StaticResource SliderBaseStyle}"
                                        Value="{Binding BlueChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DockPanel>

                                <!--  白光通道亮度  -->
                                <DockPanel Margin="0,5">
                                    <TextBlock
                                        Width="80"
                                        VerticalAlignment="Center"
                                        Text="白光通道：" />
                                    <TextBlock
                                        Width="40"
                                        Margin="0,0,5,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding WhiteChannelBrightness}" />
                                    <Slider
                                        Maximum="255"
                                        Minimum="0"
                                        Style="{StaticResource SliderBaseStyle}"
                                        Value="{Binding WhiteChannelBrightness, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                </DockPanel>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </GroupBox>

                <!--  备注信息  -->
                <DockPanel Margin="0,5">
                    <TextBlock
                        Width="100"
                        VerticalAlignment="Top"
                        Text="备注信息：" 
                        Style="{StaticResource TextBlockDefault}"/>
                    <TextBox
                        Height="60"
                        VerticalContentAlignment="Top"
                        AcceptsReturn="True"
                        Style="{StaticResource TextBoxBaseStyle}"
                        Text="{Binding Comment}"
                        TextWrapping="Wrap" />
                </DockPanel>
            </StackPanel>

            <!--  按钮区域  -->
            <StackPanel
                Grid.Row="2"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    Width="100"
                    Height="35"
                    Margin="0,0,10,0"
                    Command="{Binding RefreshPositionCommand}"
                    Content="刷新坐标"
                    FontSize="18"
                    Style="{StaticResource ButtonInfo}" />

                <Button
                    Width="80"
                    Height="35"
                    Margin="0,0,10,0"
                    Command="{Binding SaveCommand}"
                    Content="保存"
                    FontSize="18"
                    Style="{StaticResource ButtonPrimary}" />

                <Button
                    Width="80"
                    Height="35"
                    Command="{Binding CancelCommand}"
                    Content="取消"
                    FontSize="18"
                    Style="{StaticResource ButtonDefault}" />
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
