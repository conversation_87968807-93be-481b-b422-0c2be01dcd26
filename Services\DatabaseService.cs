using System;
using System.Collections.Generic;
using System.Data.SQLite; // 使用这个命名空间
using System.IO;
using System.Threading.Tasks;
using Dapper;
using Nickel_Inspect.Models;

namespace Nickel_Inspect.Services
{
    public class DatabaseService
    {
        private readonly string _connectionString;

        public DatabaseService()
        {
            var dbPath = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Data",
                "inspection.db"
            );
            var folder = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);

            // 添加连接池、超时和日志模式设置
            _connectionString =
                $"Data Source={dbPath};Version=3;Pooling=True;Max Pool Size=100;Default Timeout=30;Journal Mode=WAL;Synchronous=Normal;Cache Size=10000;";

            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            // 确保数据库文件存在
            if (
                !File.Exists(
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "inspection.db")
                )
            )
            {
                SQLiteConnection.CreateFile(
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "inspection.db")
                );
            }

            using (var connection = new SQLiteConnection(_connectionString))
            {
                connection.Open();

                // 由于连接字符串中已经包含了大部分PRAGMA设置，这里只设置busy_timeout
                using (var pragmaCommand = connection.CreateCommand())
                {
                    // 设置较短的锁定超时（1000毫秒）
                    pragmaCommand.CommandText = "PRAGMA busy_timeout = 1000;";
                    pragmaCommand.ExecuteNonQuery();
                }

                using (var cmd = connection.CreateCommand())
                {
                    // 创建机种表
                    cmd.CommandText =
                        @"
                        CREATE TABLE IF NOT EXISTS ProductModel (
                            ModelId INTEGER PRIMARY KEY AUTOINCREMENT,
                            ModelName TEXT NOT NULL,
                            ModelCode TEXT NOT NULL,
                            Description TEXT,
                            CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                            IsActive INTEGER DEFAULT 1
                        )";
                    cmd.ExecuteNonQuery();

                    // 创建检查点位表
                    cmd.CommandText =
                        @"
                        CREATE TABLE IF NOT EXISTS InspectionPoint (
                            PointId INTEGER PRIMARY KEY AUTOINCREMENT,
                            ModelId INTEGER,
                            PointName TEXT NOT NULL,
                            PointCode TEXT NOT NULL,
                            SequenceNo INTEGER NOT NULL,
                            XPosition REAL,
                            YPosition REAL,
                            ZPosition REAL,
                            TriggerIOPort INTEGER,
                            TimeoutSeconds INTEGER DEFAULT 30,
                            IsActive INTEGER DEFAULT 1,
                            InspectionFilePath TEXT,
                            LightControllerId INTEGER,
                            RedChannelBrightness INTEGER,
                            GreenChannelBrightness INTEGER,
                            BlueChannelBrightness INTEGER,
                            WhiteChannelBrightness INTEGER,
                            CustomChannelSettings TEXT
                        )";
                    cmd.ExecuteNonQuery();

                    // 创建日志表
                    cmd.CommandText =
                        @"
                        CREATE TABLE IF NOT EXISTS SystemLog (
                            LogId INTEGER PRIMARY KEY AUTOINCREMENT,
                            Timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            LogType TEXT NOT NULL,
                            Source TEXT NOT NULL,
                            Message TEXT NOT NULL,
                            ExceptionDetails TEXT
                        )";
                    cmd.ExecuteNonQuery();

                    // 检查InspectionFilePath列是否存在，如果不存在则添加
                    cmd.CommandText =
                        @"
                        PRAGMA table_info(InspectionPoint);
                    ";
                    bool columnExists = false;
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string columnName = reader["name"].ToString();
                            if (columnName == "InspectionFilePath")
                            {
                                columnExists = true;
                                break;
                            }
                        }
                    }

                    // 如果列不存在，添加它
                    if (!columnExists)
                    {
                        cmd.CommandText =
                            @"
                            ALTER TABLE InspectionPoint ADD COLUMN InspectionFilePath TEXT;
                        ";
                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        public async Task<IEnumerable<ProductModel>> GetProductModelsAsync()
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = "SELECT * FROM ProductModel WHERE IsActive = 1";
                            cmd.CommandTimeout = 5; // 设置超时为5秒

                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                var result = new List<ProductModel>();
                                while (await reader.ReadAsync())
                                {
                                    result.Add(
                                        new ProductModel
                                        {
                                            ModelId = reader.GetInt32(reader.GetOrdinal("ModelId")),
                                            ModelName = reader.GetString(
                                                reader.GetOrdinal("ModelName")
                                            ),
                                            ModelCode = reader.GetString(
                                                reader.GetOrdinal("ModelCode")
                                            ),
                                            Description = reader.IsDBNull(
                                                reader.GetOrdinal("Description")
                                            )
                                                ? null
                                                : reader.GetString(
                                                    reader.GetOrdinal("Description")
                                                ),
                                            CreateTime = reader.GetDateTime(
                                                reader.GetOrdinal("CreateTime")
                                            ),
                                            IsActive =
                                                reader.GetInt32(reader.GetOrdinal("IsActive")) == 1,
                                        }
                                    );
                                }
                                return result;
                            }
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取产品模型错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，返回空列表
            System.Diagnostics.Debug.WriteLine("多次尝试获取产品模型失败，返回空列表");
            return new List<ProductModel>();
        }

        public async Task<int> AddProductModelAsync(ProductModel model)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql =
                            @"
                            INSERT INTO ProductModel (ModelName, ModelCode, Description, IsActive) 
                            VALUES (@ModelName, @ModelCode, @Description, @IsActive);
                            SELECT last_insert_rowid();";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒
                            cmd.Parameters.AddWithValue("@ModelName", model.ModelName);
                            cmd.Parameters.AddWithValue("@ModelCode", model.ModelCode);
                            cmd.Parameters.AddWithValue(
                                "@Description",
                                (object)model.Description ?? DBNull.Value
                            );
                            cmd.Parameters.AddWithValue("@IsActive", model.IsActive ? 1 : 0);

                            var result = await cmd.ExecuteScalarAsync();
                            return Convert.ToInt32(result);
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"添加产品模型错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，抛出一个自定义异常
            throw new InvalidOperationException("多次尝试添加产品模型失败，数据库可能被锁定");
        }

        public async Task<IEnumerable<InspectionPoint>> GetInspectionPointsAsync(int modelId)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql =
                            "SELECT * FROM InspectionPoint WHERE ModelId = @ModelId ORDER BY SequenceNo";
                        return await connection.QueryAsync<InspectionPoint>(
                            sql,
                            new { ModelId = modelId }
                        );
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取检测点位错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，返回空列表
            System.Diagnostics.Debug.WriteLine("多次尝试获取检测点位失败，返回空列表");
            return new List<InspectionPoint>();
        }

        public async Task<int> AddInspectionPointAsync(InspectionPoint point)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql =
                            @"
                            INSERT INTO InspectionPoint 
                            (ModelId, PointName, PointCode, SequenceNo, XPosition, YPosition, ZPosition, 
                             TriggerIOPort, TimeoutSeconds, IsActive, InspectionFilePath, LightControllerId, RedChannelBrightness, GreenChannelBrightness, BlueChannelBrightness, WhiteChannelBrightness, CustomChannelSettings)
                            VALUES 
                            (@ModelId, @PointName, @PointCode, @SequenceNo, @XPosition, @YPosition, @ZPosition,
                             @TriggerIOPort, @TimeoutSeconds, @IsActive, @InspectionFilePath, @LightControllerId, @RedChannelBrightness, @GreenChannelBrightness,@BlueChannelBrightness,@WhiteChannelBrightness,@CustomChannelSettings );
                            SELECT last_insert_rowid();";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒
                            cmd.Parameters.AddWithValue("@ModelId", point.ModelId);
                            cmd.Parameters.AddWithValue("@PointName", point.PointName);
                            cmd.Parameters.AddWithValue("@PointCode", point.PointCode);
                            cmd.Parameters.AddWithValue("@SequenceNo", point.SequenceNo);
                            cmd.Parameters.AddWithValue("@XPosition", point.XPosition);
                            cmd.Parameters.AddWithValue("@YPosition", point.YPosition);
                            cmd.Parameters.AddWithValue("@ZPosition", point.ZPosition);
                            cmd.Parameters.AddWithValue("@TriggerIOPort", point.TriggerIOPort);
                            cmd.Parameters.AddWithValue("@TimeoutSeconds", point.TimeoutSeconds);
                            cmd.Parameters.AddWithValue("@IsActive", point.IsActive ? 1 : 0);
                            cmd.Parameters.AddWithValue(
                                "@InspectionFilePath",
                                (object)point.InspectionFilePath ?? DBNull.Value
                            );
                            cmd.Parameters.AddWithValue(
                                "@LightControllerId",
                                point.LightControllerId
                            );
                            cmd.Parameters.AddWithValue(
                                "@RedChannelBrightness",
                                point.RedChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@GreenChannelBrightness",
                                point.GreenChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@BlueChannelBrightness",
                                point.BlueChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@WhiteChannelBrightness",
                                point.WhiteChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@CustomChannelSettings",
                                point.CustomChannelSettings
                            );

                            var result = await cmd.ExecuteScalarAsync();
                            return Convert.ToInt32(result);
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"添加检测点位错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，抛出一个自定义异常
            throw new InvalidOperationException("多次尝试添加检测点位失败，数据库可能被锁定");
        }

        public async Task<bool> UpdateInspectionPointAsync(InspectionPoint point)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql =
                            @"
                            UPDATE InspectionPoint 
                            SET XPosition = @XPosition, 
                                YPosition = @YPosition, 
                                ZPosition = @ZPosition, 
                                IsActive = @IsActive, 
                                InspectionFilePath = @InspectionFilePath,
                                LightControllerId = @LightControllerId,
                                RedChannelBrightness = @RedChannelBrightness,
                                GreenChannelBrightness = @GreenChannelBrightness,
                                BlueChannelBrightness = @BlueChannelBrightness,
                                WhiteChannelBrightness = @WhiteChannelBrightness,
                                CustomChannelSettings = @CustomChannelSettings
                            WHERE PointId = @PointId";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒
                            cmd.Parameters.AddWithValue("@PointId", point.PointId);
                            cmd.Parameters.AddWithValue("@XPosition", point.XPosition);
                            cmd.Parameters.AddWithValue("@YPosition", point.YPosition);
                            cmd.Parameters.AddWithValue("@ZPosition", point.ZPosition);
                            cmd.Parameters.AddWithValue("@IsActive", point.IsActive ? 1 : 0);
                            cmd.Parameters.AddWithValue(
                                "@InspectionFilePath",
                                (object)point.InspectionFilePath ?? DBNull.Value
                            );
                            cmd.Parameters.AddWithValue(
                                "@LightControllerId",
                                point.LightControllerId
                            );
                            cmd.Parameters.AddWithValue(
                                "@RedChannelBrightness",
                                point.RedChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@GreenChannelBrightness",
                                point.GreenChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@BlueChannelBrightness",
                                point.BlueChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@WhiteChannelBrightness",
                                point.WhiteChannelBrightness
                            );
                            cmd.Parameters.AddWithValue(
                                "@CustomChannelSettings",
                                point.CustomChannelSettings
                            );

                            int rowsAffected = await cmd.ExecuteNonQueryAsync();
                            return rowsAffected > 0;
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"更新检测点位错误: {ex.Message}");
                    throw;
                }
            }

            return false;
        }

        public async Task<bool> DeleteInspectionPointAsync(int pointId)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql = "DELETE FROM InspectionPoint WHERE PointId = @PointId";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒
                            cmd.Parameters.AddWithValue("@PointId", pointId);

                            int rowsAffected = await cmd.ExecuteNonQueryAsync();
                            return rowsAffected > 0;
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"删除检测点位错误: {ex.Message}");
                    throw;
                }
            }

            return false;
        }

        public async Task<bool> DeleteProductModelAsync(int modelId)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql = "DELETE FROM ProductModel WHERE ModelId = @ModelId";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒
                            cmd.Parameters.AddWithValue("@ModelId", modelId);

                            int rowsAffected = await cmd.ExecuteNonQueryAsync();
                            return rowsAffected > 0;
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"删除机种错误: {ex.Message}");
                    throw;
                }
            }

            return false;
        }

        // 添加日志相关方法

        /// <summary>
        /// 添加日志记录
        /// </summary>
        public async Task<int> AddLogAsync(
            string logType,
            string source,
            string message,
            string exceptionDetails = null,
            DateTime timestamp = default
        )
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();

                        // 使用参数化查询直接传入DateTime对象，SQLite会自动处理转换
                        const string sql =
                            @"
                            INSERT INTO SystemLog (Timestamp, LogType, Source, Message, ExceptionDetails) 
                            VALUES (@Timestamp, @LogType, @Source, @Message, @ExceptionDetails);
                            SELECT last_insert_rowid();";

                        // 设置命令超时为2秒
                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 2;
                            // 直接传入DateTime.Now，保留毫秒精度
                            cmd.Parameters.AddWithValue("@Timestamp", timestamp);
                            cmd.Parameters.AddWithValue("@LogType", logType);
                            cmd.Parameters.AddWithValue("@Source", source);
                            cmd.Parameters.AddWithValue("@Message", message);
                            cmd.Parameters.AddWithValue("@ExceptionDetails", exceptionDetails);

                            var result = await cmd.ExecuteScalarAsync();
                            return Convert.ToInt32(result);
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1)); // 分别等待50ms, 100ms, 150ms
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"数据库写入错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，抛出一个自定义异常
            throw new InvalidOperationException("多次尝试写入日志失败，数据库可能被锁定");
        }

        /// <summary>
        /// 获取最近的日志记录
        /// </summary>
        public async Task<IEnumerable<SystemLog>> GetRecentLogsAsync(
            int count = 1000,
            string logType = null,
            string source = null
        )
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();

                        string whereClause = "";
                        var parameters = new DynamicParameters();

                        if (!string.IsNullOrEmpty(logType))
                        {
                            whereClause += " WHERE LogType = @LogType";
                            parameters.Add("@LogType", logType);

                            if (!string.IsNullOrEmpty(source))
                            {
                                whereClause += " AND Source = @Source";
                                parameters.Add("@Source", source);
                            }
                        }
                        else if (!string.IsNullOrEmpty(source))
                        {
                            whereClause += " WHERE Source = @Source";
                            parameters.Add("@Source", source);
                        }

                        parameters.Add("@Count", count);

                        string sql =
                            $@"
                            SELECT * FROM SystemLog
                            {whereClause}
                            ORDER BY Timestamp DESC
                            LIMIT @Count";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒

                            foreach (var param in parameters.ParameterNames)
                            {
                                var value = parameters.Get<object>(param);
                                var dbParam = cmd.Parameters.AddWithValue(param, value);
                            }

                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                var result = new List<SystemLog>();
                                while (await reader.ReadAsync())
                                {
                                    result.Add(
                                        new SystemLog
                                        {
                                            LogId = reader.GetInt32(reader.GetOrdinal("LogId")),
                                            Timestamp = reader.GetDateTime(
                                                reader.GetOrdinal("Timestamp")
                                            ),
                                            LogType = reader.GetString(
                                                reader.GetOrdinal("LogType")
                                            ),
                                            Source = reader.GetString(reader.GetOrdinal("Source")),
                                            Message = reader.GetString(
                                                reader.GetOrdinal("Message")
                                            ),
                                            ExceptionDetails = reader.IsDBNull(
                                                reader.GetOrdinal("ExceptionDetails")
                                            )
                                                ? null
                                                : reader.GetString(
                                                    reader.GetOrdinal("ExceptionDetails")
                                                ),
                                        }
                                    );
                                }
                                return result;
                            }
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"读取日志错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，返回空列表
            System.Diagnostics.Debug.WriteLine("多次尝试读取日志失败，返回空列表");
            return new List<SystemLog>();
        }

        /// <summary>
        /// 获取指定日期范围内的日志
        /// </summary>
        public async Task<IEnumerable<SystemLog>> GetLogsByDateRangeAsync(
            DateTime startDate,
            DateTime endDate,
            string logType = null,
            string source = null
        )
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();

                        string whereClause = " WHERE Timestamp BETWEEN @StartDate AND @EndDate";
                        var parameters = new DynamicParameters();
                        // 直接使用DateTime参数
                        parameters.Add("@StartDate", startDate);
                        parameters.Add("@EndDate", endDate);

                        if (!string.IsNullOrEmpty(logType))
                        {
                            whereClause += " AND LogType = @LogType";
                            parameters.Add("@LogType", logType);
                        }

                        if (!string.IsNullOrEmpty(source))
                        {
                            whereClause += " AND Source = @Source";
                            parameters.Add("@Source", source);
                        }

                        string sql =
                            $@"
                            SELECT * FROM SystemLog
                            {whereClause}
                            ORDER BY Timestamp DESC";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 5; // 设置超时为5秒

                            foreach (var param in parameters.ParameterNames)
                            {
                                var value = parameters.Get<object>(param);
                                var dbParam = cmd.Parameters.AddWithValue(param, value);
                            }

                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                var result = new List<SystemLog>();
                                while (await reader.ReadAsync())
                                {
                                    result.Add(
                                        new SystemLog
                                        {
                                            LogId = reader.GetInt32(reader.GetOrdinal("LogId")),
                                            // 直接获取DateTime值
                                            Timestamp = reader.GetDateTime(
                                                reader.GetOrdinal("Timestamp")
                                            ),
                                            LogType = reader.GetString(
                                                reader.GetOrdinal("LogType")
                                            ),
                                            Source = reader.GetString(reader.GetOrdinal("Source")),
                                            Message = reader.GetString(
                                                reader.GetOrdinal("Message")
                                            ),
                                            ExceptionDetails = reader.IsDBNull(
                                                reader.GetOrdinal("ExceptionDetails")
                                            )
                                                ? null
                                                : reader.GetString(
                                                    reader.GetOrdinal("ExceptionDetails")
                                                ),
                                        }
                                    );
                                }
                                return result;
                            }
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"读取日志错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，返回空列表
            System.Diagnostics.Debug.WriteLine("多次尝试读取日志失败，返回空列表");
            return new List<SystemLog>();
        }

        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        public async Task<int> CleanupLogsAsync(DateTime beforeDate)
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        const string sql = @"DELETE FROM SystemLog WHERE Timestamp < @BeforeDate";

                        using (var cmd = connection.CreateCommand())
                        {
                            cmd.CommandText = sql;
                            cmd.CommandTimeout = 10; // 清理可能需要更长时间，设置超时为10秒
                            // 直接使用DateTime参数
                            cmd.Parameters.AddWithValue("@BeforeDate", beforeDate);

                            return await cmd.ExecuteNonQueryAsync();
                        }
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"清理日志错误: {ex.Message}");
                    throw;
                }
            }

            // 所有重试都失败后，返回0（表示没有清理任何记录）
            System.Diagnostics.Debug.WriteLine("多次尝试清理日志失败，返回0");
            return 0;
        }

        /// <summary>
        /// 按日志类型和日期范围获取日志
        /// </summary>
        public async Task<IEnumerable<SystemLog>> GetLogsByTypeAndDateRangeAsync(
            List<string> logTypes,
            DateTime startDate,
            DateTime endDate,
            string source = null
        )
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();

                        var parameters = new DynamicParameters();
                        parameters.Add("@StartDate", startDate);
                        parameters.Add("@EndDate", endDate);

                        // 构建SQL查询
                        string sql = "SELECT * FROM SystemLog WHERE Timestamp BETWEEN @StartDate AND @EndDate";

                        // 构建日志类型条件
                        if (logTypes != null && logTypes.Count > 0)
                        {
                            // 使用Dapper参数数组处理IN查询
                            sql += " AND LogType IN @LogTypes";
                            parameters.Add("@LogTypes", logTypes);
                        }

                        // 添加来源条件
                        if (!string.IsNullOrEmpty(source))
                        {
                            sql += " AND Source = @Source";
                            parameters.Add("@Source", source);
                        }

                        // 添加排序
                        sql += " ORDER BY Timestamp DESC";

                        // 使用Dapper的Query方法直接执行查询
                        return await connection.QueryAsync<SystemLog>(sql, parameters);
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex) when (retry < 2)
                {
                    // 其他错误也尝试重试
                    System.Diagnostics.Debug.WriteLine($"查询日志错误 (重试 {retry}): {ex.Message}");
                    await Task.Delay(100 * (retry + 1));
                }
            }

            // 所有重试都失败，抛出异常
            throw new Exception("无法查询日志数据，数据库访问失败");
        }

        /// <summary>
        /// 获取所有不同的日志来源
        /// </summary>
        public async Task<IEnumerable<string>> GetDistinctSourcesAsync()
        {
            // 最多重试3次
            for (int retry = 0; retry < 3; retry++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(_connectionString))
                    {
                        await connection.OpenAsync();
                        
                        // 使用Dapper查询不同的日志来源
                        const string sql = "SELECT DISTINCT Source FROM SystemLog ORDER BY Source";
                        return await connection.QueryAsync<string>(sql);
                    }
                }
                catch (SQLiteException ex) when (ex.ErrorCode == 5 && retry < 2) // 错误码5是"database is locked"
                {
                    // 如果是数据库锁定错误，且还有重试次数，则等待一段时间后重试
                    await Task.Delay(50 * (retry + 1));
                }
                catch (Exception ex) when (retry < 2)
                {
                    // 其他错误也尝试重试
                    System.Diagnostics.Debug.WriteLine($"查询日志来源错误 (重试 {retry}): {ex.Message}");
                    await Task.Delay(100 * (retry + 1));
                }
            }

            // 所有重试都失败，返回空列表
            return new List<string>();
        }
    }
}
