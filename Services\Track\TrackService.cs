using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HandyControl.Controls;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.Track;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.Alarms;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;

namespace Nickel_Inspect.Services.Track
{
    /// <summary>
    /// 板卡运输状态机
    /// </summary>
    public enum BoardTransportState
    {
        Idle, // 空闲等待

        // 等待进板相关状态
        WaitingForBoard_Init, // 等待进板初始化
        WaitingForBoard_SMEMA, // 等待进板-SMEMA处理
        WaitingForBoard_Sensor, // 等待进板-传感器检测

        // 进板相关状态
        BoardEntering, // 板卡进入中
        BoardPositioning, // 板卡定位中

        // 对位相关状态
        BoardAligning_Start, // 开始对位
        BoardAligning_Clamp, // 夹紧中
        BoardAligning_Stop, // 停止传送带

        // 加工相关状态
        Processing_Ready, // 准备加工
        Processing_Active, // 加工中
        Processing_Complete, // 加工完成

        // 出板准备相关状态
        PrepareExit_Start, // 开始准备出板
        PrepareExit_Release, // 释放夹具
        PrepareExit_Conveyor, // 启动传送带

        // 出板相关状态
        BoardExiting, // 板卡出板中
        WaitingForDownstream, // 等待下游准备好
        BoardExiting_SMEMA, // SMEMA出板处理

        // 完成出板相关状态
        CompleteExit_Start, // 开始完成出板
        CompleteExit_Wait, // 等待传感器关闭
        CompleteExit_Stop, // 停止传送带

        // 复位相关状态
        ResetState_Start, // 开始复位
        ResetState_SMEMA, // SMEMA信号复位
        ResetState_Complete, // 复位完成

        // 错误状态
        Error // 错误状态
        ,
    }

    public class TrackService : ITrackService
    {
        private readonly BkMotionCard _motionCard;
        private readonly ILogService _logService;
        private readonly ITrackConfigService _trackConfigService;
        private readonly StatusManager _statusManager;
        private TrackStateData _currentState;
        private bool _isInitialized;
        private DeviceConfiguration _config;
        private AxisConfig _beltAxis;
        private AxisConfig _widthAxis;
        private MachineConfigurationHelper _machineConfig;
        private readonly object _lockObject = new object(); // 添加锁对象

        // 状态机相关
        private BoardTransportState _transportState = BoardTransportState.Idle;
        private bool _isRunning = false;
        private Task _autoRunTask;
        private CancellationTokenSource _cancellationTokenSource;

        // 离线模拟相关
        private bool _isOfflineSimulation = false;
        private CancellationTokenSource _simulationCancellationTokenSource;
        private Task _simulationTask;
        private bool _simulatedIncomingSensor = false;
        private bool _simulatedPositionSensor = false;
        private bool _simulatedOutgoingSensor = false;
        private const int SIMULATION_INCOMING_DELAY = 3000; // 入料传感器模拟延时 (ms)
        private const int SIMULATION_POSITION_DELAY = 5000; // 到位传感器模拟延时 (ms)
        private const int SIMULATION_OUTGOING_DELAY = 3000; // 出料传感器模拟延时 (ms)

        // 添加标志位以跟踪模拟任务是否已启动
        private bool _incomingSimulationStarted = false;
        private bool _positionSimulationStarted = false;
        private bool _outgoingSimulationStarted = false;

        // 传感器防抖动相关
        private Dictionary<string, SensorDebouncer> _sensorDebouncers =
            new Dictionary<string, SensorDebouncer>();
        private const int DEBOUNCE_TIME_MS = 100; // 防抖时间从50ms调整为100ms
        private bool _isDebugMode = false; // 添加调试模式标志
        private const int SENSOR_UPDATE_INTERVAL_MS = 100; // 将传感器更新间隔从500ms减小到100ms，提高响应速度

        // 传感器监控相关
        private Task _sensorMonitorTask;
        private CancellationTokenSource _sensorMonitorCts;

        // IO配置
        private IoConfig _stopperCylinder;
        private IoConfig _clampCylinder;
        private IoConfig _incomingSensor;
        private IoConfig _positionSensor;
        private IoConfig _outgoingSensor;
        private IoConfig _nextMachineReady;
        private IoConfig _boardAvailable;
        private IoConfig _machineReady;

        // 状态机延时参数（单位：毫秒）
        private const int BOARD_ALIGNING_DELAY = 500; // 板材到位后继续运行时间
        private const int CLAMP_OPERATION_DELAY = 200; // 夹紧气缸动作延时
        private const int CONVEYOR_STOP_DELAY = 300; // 停止传送带延时
        private const int CONVEYOR_START_DELAY = 300; // 启动传送带延时
        private const int CYLINDER_OPERATION_DELAY = 200; // 气缸动作延时

        // 状态机计时器
        private DateTime _stateEnterTime;
        private bool _delayCompleted;

        // 在类的开头添加方向控制相关的字段
        private bool IsLeftToRight => _currentState.Direction == TrackDirection.LeftToRight;

        // 修改传感器方向相关的属性
        private bool GetIncomingSensorValue =>
            _isOfflineSimulation
                ? _simulatedIncomingSensor
                : (
                    IsLeftToRight
                        ? _sensorDebouncers["IncomingSensor"].Value
                        : _sensorDebouncers["OutgoingSensor"].Value
                );

        private bool GetPositionSensorValue =>
            _isOfflineSimulation
                ? _simulatedPositionSensor
                : _sensorDebouncers["PositionSensor"].Value;

        private bool GetOutgoingSensorValue =>
            _isOfflineSimulation
                ? _simulatedOutgoingSensor
                : (
                    IsLeftToRight
                        ? _sensorDebouncers["OutgoingSensor"].Value
                        : _sensorDebouncers["IncomingSensor"].Value
                );

        // 添加一个变量来保存暂停前的状态
        private BoardTransportState _pausedState = BoardTransportState.Idle;
        private bool _wasPaused = false;

        public TrackService(
            BkMotionCard motionCard,
            MachineConfigurationHelper machineConfig,
            ILogService logService,
            ITrackConfigService trackConfigService,
            StatusManager statusManager
        )
        {
            _motionCard = motionCard ?? throw new ArgumentNullException(nameof(motionCard));
            _machineConfig = machineConfig;
            _logService = logService;
            _trackConfigService = trackConfigService;
            _statusManager = statusManager;

            // 订阅状态管理器的报警事件
            if (_statusManager != null)
            {
                _statusManager.AlarmsChanged += OnAlarmsChanged;
                _logService.LogInformation("轨道服务已订阅报警事件", "轨道系统");
            }

            // 初始化基本状态，不依赖于配置文件的值
            _currentState = new TrackStateData
            {
                CurrentState = "未初始化",
                CurrentMode = TrackMode.Normal,
                Direction = TrackDirection.LeftToRight,
                LastUpdateTime = DateTime.Now,
                SmemaEnabled = false,
                IsDebugMode = false,
                BeltSpeed = 0, // 先设置为0，等配置加载后再更新
                WidthAxisSpeed = 0, // 先设置为0，等配置加载后再更新
            };

            // 确保状态更新事件被触发
            OnStateChanged();

            // 异步加载配置，避免阻塞UI线程
            Task.Run(() =>
            {
                try
                {
                    LoadConfiguration();

                    // 配置加载完成后，更新速度值
                    if (_beltAxis != null && _widthAxis != null)
                    {
                        _currentState.BeltSpeed = (int)_beltAxis.RunSpeed;
                        _currentState.WidthAxisSpeed = (int)_widthAxis.RunSpeed / 2;
                        OnStateChanged();
                    }

                    InitializeSensorDebouncers();

                    // 启动传感器监控任务
                    StartSensorMonitoring();

                    // 异步初始化
                    InitializeAsync()
                        .ContinueWith(t =>
                        {
                            if (t.IsFaulted)
                            {
                                OnErrorOccurred(
                                    $"初始化失败: {t.Exception.InnerException?.Message}"
                                );
                            }
                        });
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"初始化失败: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 初始化传感器防抖动器
        /// </summary>
        private void InitializeSensorDebouncers()
        {
            // 初始化传感器防抖器
            _sensorDebouncers.Clear();
            _sensorDebouncers.Add(
                "IncomingSensor",
                new SensorDebouncer(DEBOUNCE_TIME_MS, "IncomingSensor")
            );
            _sensorDebouncers.Add(
                "PositionSensor",
                new SensorDebouncer(DEBOUNCE_TIME_MS, "PositionSensor")
            );
            _sensorDebouncers.Add(
                "OutgoingSensor",
                new SensorDebouncer(DEBOUNCE_TIME_MS, "OutgoingSensor")
            );
            _sensorDebouncers.Add(
                "NextMachineReady",
                new SensorDebouncer(DEBOUNCE_TIME_MS, "NextMachineReady")
            );
        }

        private void LoadConfiguration()
        {
            try
            {
                // 首先尝试从当前目录加载
                string configPath = "DeviceConfiguration.json";
                if (!File.Exists(configPath))
                {
                    // 如果当前目录不存在，尝试从应用程序目录加载
                    configPath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        "DeviceConfiguration.json"
                    );

                    if (!File.Exists(configPath))
                    {
                        throw new FileNotFoundException(
                            $"无法找到配置文件，已尝试路径: {Path.GetFullPath("DeviceConfiguration.json")} 和 {configPath}"
                        );
                    }
                }

                OnMessageLogged($"正在加载配置文件: {Path.GetFullPath(configPath)}");

                string jsonString = File.ReadAllText(configPath);
                if (string.IsNullOrWhiteSpace(jsonString))
                {
                    throw new InvalidOperationException("配置文件内容为空");
                }

                // 先检查JSON是否有效
                try
                {
                    var config = JObject.Parse(jsonString);
                    OnMessageLogged("JSON解析成功，尝试反序列化配置对象");

                    // 检查关键路径是否存在
                    if (config["DeviceConfig"] == null)
                    {
                        throw new InvalidOperationException(
                            $"配置文件缺少DeviceConfig部分, 文件名：{configPath}"
                        );
                    }

                    if (config["DeviceConfig"]["Conveyor"] == null)
                    {
                        throw new InvalidOperationException("配置文件缺少Conveyor部分");
                    }

                    // 反序列化根配置对象
                    var rootConfig = JsonConvert.DeserializeObject<RootConfig>(jsonString);
                    if (rootConfig == null || rootConfig.DeviceConfig == null)
                    {
                        throw new InvalidOperationException("无法反序列化配置对象");
                    }

                    _config = rootConfig.DeviceConfig;
                    OnMessageLogged("成功加载设备配置基本信息");

                    // 获取轨道相关的IO配置 - 更健壮的方式
                    try
                    {
                        _stopperCylinder = GetNestedConfigObject<IoConfig>(
                            config,
                            "DeviceConfig.Conveyor.Actuators.StopperCylinder"
                        );
                        OnMessageLogged("已加载阻挡气缸配置");

                        _clampCylinder = GetNestedConfigObject<IoConfig>(
                            config,
                            "DeviceConfig.Conveyor.Actuators.ClampCylinder"
                        );
                        OnMessageLogged("已加载夹紧气缸配置");

                        _incomingSensor = GetNestedConfigObject<IoConfig>(
                            config,
                            "DeviceConfig.Conveyor.Sensors.IncomingBoard"
                        );
                        OnMessageLogged("已加载入料传感器配置");

                        _positionSensor = GetNestedConfigObject<IoConfig>(
                            config,
                            "DeviceConfig.Conveyor.Sensors.BoardInPosition"
                        );
                        OnMessageLogged("已加载到位传感器配置");

                        _outgoingSensor = GetNestedConfigObject<IoConfig>(
                            config,
                            "DeviceConfig.Conveyor.Sensors.OutgoingBoard"
                        );
                        OnMessageLogged("已加载出料传感器配置");
                    }
                    catch (Exception ex)
                    {
                        OnWarning($"加载传送带IO配置失败: {ex.Message}");
                        // 继续后续处理，因为SMEMA和轴配置仍然可能有效
                    }

                    // 获取SMEMA信号配置 - 更健壮的方式
                    try
                    {
                        _nextMachineReady = GetNestedConfigObject<IoConfig>(
                            config,
                            "Smema.Downstream.MachineReady"
                        );
                        _boardAvailable = GetNestedConfigObject<IoConfig>(
                            config,
                            "Smema.Upstream.BoardAvailable"
                        );
                        _machineReady = GetNestedConfigObject<IoConfig>(
                            config,
                            "Smema.Upstream.MachineReady"
                        );
                        OnMessageLogged("已加载SMEMA配置");
                    }
                    catch (Exception ex)
                    {
                        OnWarning($"加载SMEMA配置失败: {ex.Message}");
                        // 继续后续处理，因为轴配置仍然可能有效
                    }

                    // 获取轴配置
                    _beltAxis = _config.GetAxisByName("Belt");
                    if (_beltAxis == null)
                    {
                        OnWarning("未找到Belt轴配置，轨道可能无法正常工作");
                    }
                    else
                    {
                        OnMessageLogged($"已加载Belt轴配置: AxisNo={_beltAxis.AxisNo}");
                        // 设置Belt轴的速度参数
                        _motionCard.SetStartSpeed(_beltAxis.AxisNo, (int)_beltAxis.StartSpeed);
                        _motionCard.SetMaxSpeed(_beltAxis.AxisNo, (int)_beltAxis.RunSpeed);
                    }

                    _widthAxis = _config.GetAxisByName("Width");
                    if (_widthAxis == null)
                    {
                        OnWarning("未找到Width轴配置，调宽功能可能无法正常工作");
                    }
                    else
                    {
                        OnMessageLogged($"已加载Width轴配置: AxisNo={_widthAxis.AxisNo}");
                        // 设置Width轴的速度参数
                        _motionCard.SetStartSpeed(_widthAxis.AxisNo, (int)_widthAxis.StartSpeed);
                        _motionCard.SetMaxSpeed(_widthAxis.AxisNo, (int)_widthAxis.RunSpeed);
                    }

                    // 更新当前状态中的速度值
                    if (_beltAxis != null)
                    {
                        _currentState.BeltSpeed = (int)_beltAxis.RunSpeed;
                    }

                    if (_widthAxis != null)
                    {
                        _currentState.WidthAxisSpeed = (int)_widthAxis.RunSpeed;
                    }

                    OnMessageLogged("配置加载成功");
                }
                catch (JsonException ex)
                {
                    throw new InvalidOperationException($"JSON解析错误: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                // 记录详细的错误并让调用者知道出了问题
                OnErrorOccurred($"加载配置文件失败: {ex.Message}");
                _logService.LogError(ex, "加载配置文件失败", "轨道系统");
                throw;
            }

            // 初始化传感器防抖动器
            InitializeSensorDebouncers();

            // 启动传感器监控任务
            StartSensorMonitoring();
        }

        /// <summary>
        /// 辅助方法：从嵌套JSON路径中获取对象
        /// </summary>
        private T GetNestedConfigObject<T>(JObject config, string path)
        {
            try
            {
                string[] segments = path.Split('.');
                JToken current = config;

                foreach (string segment in segments)
                {
                    current = current[segment];
                    if (current == null)
                    {
                        throw new InvalidOperationException(
                            $"配置路径'{path}'中的'{segment}'部分不存在"
                        );
                    }
                }

                return current.ToObject<T>();
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                throw new InvalidOperationException($"解析配置路径'{path}'失败: {ex.Message}", ex);
            }
        }

        public TrackStateData CurrentState => _currentState;

        public event EventHandler<TrackStateData> StateChanged;
        public event EventHandler<string> ErrorOccurred;
        public event EventHandler<string> WarningOccurred;
        public event EventHandler<string> MessageLogged;
        public event EventHandler<BoardArrivedEventArgs> BoardArrived;

        private async Task InitializeAsync()
        {
            try
            {
                if (_isInitialized)
                    return;

                // 初始化气缸状态
                await ControlStopperAsync(false); // 确保阻挡气缸缩回
                await ControlClampAsync(false); // 确保夹紧气缸松开

                // 初始化Belt轴 - 移除回零操作，皮带轴不需要回零
                // 只设置速度参数
                _motionCard.SetStartSpeed(_beltAxis.AxisNo, (int)_beltAxis.StartSpeed);
                _motionCard.SetMaxSpeed(_beltAxis.AxisNo, (int)_beltAxis.RunSpeed);

                // 初始化SMEMA信号
                _motionCard.SetOutputBit(
                    _boardAvailable.IoAddress,
                    _boardAvailable.IoBitIndex,
                    false
                );
                _motionCard.SetOutputBit(_machineReady.IoAddress, _machineReady.IoBitIndex, false);

                _isInitialized = true;
                _transportState = BoardTransportState.Idle;
                _currentState.CurrentState = "已停止";
                OnStateChanged();
                OnMessageLogged("轨道控制系统初始化完成");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查是否有报警
        /// </summary>
        /// <returns>如果有报警返回true</returns>
        private bool CheckForAlarms()
        {
            if (_statusManager == null)
                return false;

            if (_statusManager.HasActiveAlarms)
            {
                if (_statusManager.HasServoAlarms || _statusManager.HasMotionAlarms)
                {
                    _logService.LogWarning("存在严重报警，无法执行轨道操作", "轨道系统");
                    OnErrorOccurred("存在严重报警，无法执行轨道操作");
                }
                else
                {
                    _logService.LogWarning("存在报警，无法执行轨道操作", "轨道系统");
                    OnErrorOccurred("存在报警，无法执行轨道操作");
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 启动轨道
        /// </summary>
        public async Task StartAsync()
        {
            try
            {
                await EnsureInitializedAsync();

                // 检查是否存在报警
                if (CheckForAlarms())
                {
                    return;
                }

                if (_isRunning)
                {
                    _logService.LogInformation("轨道已经在运行中", "轨道系统");
                    return;
                }

                try
                {
                    // 重置取消标记
                    _cancellationTokenSource = new CancellationTokenSource();

                    // 设置状态
                    _isRunning = true;
                    _transportState = BoardTransportState.Idle;
                    _wasPaused = false; // 重置暂停标志

                    // 启动自动运行任务
                    _autoRunTask = Task.Run(
                        () => AutoRunAsync(_cancellationTokenSource.Token),
                        _cancellationTokenSource.Token
                    );

                    _currentState.CurrentState = "运行中";
                    _currentState.LastUpdateTime = DateTime.Now;
                    OnStateChanged();

                    _logService.LogInformation("皮带轨道已启动", "轨道系统");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"启动失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"启动失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 停止轨道
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                // 即使有报警也允许停止操作
                await EnsureInitializedAsync();
                try
                {
                    // 停止自动运行状态机
                    _isRunning = false;
                    _wasPaused = false; // 重置暂停标志

                    await Task.Run(async () =>
                    {
                        if (_cancellationTokenSource != null)
                        {
                            _cancellationTokenSource.Cancel();
                            try
                            {
                                if (_autoRunTask != null)
                                {
                                    await _autoRunTask;
                                }
                            }
                            catch (OperationCanceledException)
                            {
                                // 预期的取消异常，忽略
                            }
                            if (_cancellationTokenSource != null)
                            {
                                _cancellationTokenSource.Dispose();
                                _cancellationTokenSource = null;
                            }
                        }

                        // 停止Belt轴
                        _motionCard.StopAxisJog(_beltAxis.AxisNo);

                        // 复位SMEMA信号
                        _motionCard.SetOutputBit(
                            _boardAvailable.IoAddress,
                            _boardAvailable.IoBitIndex,
                            false
                        );
                        _motionCard.SetOutputBit(
                            _machineReady.IoAddress,
                            _machineReady.IoBitIndex,
                            false
                        );
                    });

                    _transportState = BoardTransportState.Idle;
                    _currentState.CurrentState = "已停止";
                    OnStateChanged();
                    OnMessageLogged("轨道已停止");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"停止失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"停止失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 暂停轨道
        /// </summary>
        public async Task PauseAsync()
        {
            try
            {
                // 即使有报警也允许暂停操作
                await EnsureInitializedAsync();

                try
                {
                    // 检查当前状态，避免在错误状态下暂停
                    if (_transportState == BoardTransportState.Error)
                    {
                        OnWarning("无法暂停：当前处于错误状态");
                        return;
                    }

                    // 记录暂停前状态的更多信息
                    OnMessageLogged($"准备暂停，当前状态: {_transportState}");

                    // 保存当前状态，以便后续恢复
                    _pausedState = _transportState;
                    _wasPaused = true;

                    // 记录暂停时的传感器状态，帮助调试
                    OnMessageLogged(
                        $"暂停时传感器状态 - 入料: {GetIncomingSensorValue}, 到位: {GetPositionSensorValue}, 出料: {GetOutgoingSensorValue}"
                    );

                    // 暂停自动运行状态机
                    _isRunning = false;

                    // 如果有自动运行任务，取消它
                    if (_cancellationTokenSource != null)
                    {
                        _cancellationTokenSource.Cancel();
                        _cancellationTokenSource.Dispose();
                        _cancellationTokenSource = null;
                    }

                    // 暂停Belt轴
                    await Task.Run(() =>
                    {
                        _motionCard.StopAxisJog(_beltAxis.AxisNo);
                    });

                    _currentState.CurrentState = "已暂停";
                    OnStateChanged();
                    OnMessageLogged($"轨道已暂停，保存状态: {_pausedState} 以便后续恢复");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"暂停失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"暂停失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 继续运行
        /// </summary>
        public async Task ResumeAsync()
        {
            try
            {
                await EnsureInitializedAsync();

                // 检查是否存在严重报警，轻微报警仍允许继续
                if (
                    _statusManager != null
                    && (_statusManager.HasServoAlarms || _statusManager.HasMotionAlarms)
                )
                {
                    OnWarning("无法继续：存在严重报警");
                    return;
                }

                try
                {
                    if (_wasPaused)
                    {
                        // 如果是从暂停状态恢复，需要重启状态机并恢复之前的状态
                        OnMessageLogged($"准备继续运行，将恢复至状态: {_pausedState}");

                        // 设置运行状态但保持暂停标志为true，让状态机知道是从暂停状态恢复的
                        // _wasPaused标志将在状态机中根据状态处理情况适当的时机重置
                        _isRunning = true;

                        // 创建新的取消标记
                        _cancellationTokenSource = new CancellationTokenSource();

                        // 设置当前状态为之前保存的状态
                        _transportState = _pausedState;

                        // 根据恢复的状态重新启动相应的操作
                        if (
                            _transportState == BoardTransportState.BoardEntering
                            || _transportState == BoardTransportState.BoardPositioning
                            || _transportState == BoardTransportState.BoardExiting
                        )
                        {
                            // 如果是在板卡移动过程中暂停的，需要重新启动皮带
                            await MoveBeltForwardAsync();
                            OnMessageLogged($"已恢复皮带运动，当前状态: {_transportState}");
                        }

                        // 启动新的自动运行任务
                        _autoRunTask = Task.Run(
                            () => AutoRunAsync(_cancellationTokenSource.Token),
                            _cancellationTokenSource.Token
                        );

                        OnMessageLogged($"轨道继续运行，已恢复至状态: {_pausedState}");
                    }
                    else
                    {
                        // 如果已停止，则重新启动
                        OnMessageLogged("未检测到暂停状态，将重新启动轨道");
                        await StartAsync();
                        return;
                    }

                    // 更新状态
                    _currentState.CurrentState = "运行中";
                    _currentState.LastUpdateTime = DateTime.Now;
                    OnStateChanged();

                    _logService.LogInformation("皮带轨道已继续运行", "轨道系统");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"继续运行失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"继续运行失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 紧急停止
        /// </summary>
        public async Task EmergencyStopAsync()
        {
            try
            {
                // 即使有报警也允许紧急停止操作
                await EnsureInitializedAsync();
                try
                {
                    // 停止自动运行状态机
                    _isRunning = false;
                    _wasPaused = false; // 重置暂停标志

                    await Task.Run(async () =>
                    {
                        if (_cancellationTokenSource != null)
                        {
                            _cancellationTokenSource.Cancel();
                            _cancellationTokenSource.Dispose();
                            _cancellationTokenSource = null;
                        }

                        // 紧急停止Belt轴
                        _motionCard.StopAxisJog(_beltAxis.AxisNo);

                        // 确保气缸处于安全状态
                        await ControlStopperAsync(true); // 伸出阻挡气缸
                        await ControlClampAsync(false); // 松开夹紧气缸

                        // 复位SMEMA信号
                        _motionCard.SetOutputBit(
                            _boardAvailable.IoAddress,
                            _boardAvailable.IoBitIndex,
                            false
                        );
                        _motionCard.SetOutputBit(
                            _machineReady.IoAddress,
                            _machineReady.IoBitIndex,
                            false
                        );
                    });

                    _transportState = BoardTransportState.Error;
                    _currentState.CurrentState = "紧急停止";
                    _currentState.ErrorMessage = "紧急停止已触发";
                    OnStateChanged();
                    OnWarning("紧急停止已触发");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"紧急停止失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"紧急停止失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 复位
        /// </summary>
        public async Task ResetAsync()
        {
            try
            {
                await EnsureInitializedAsync();

                // 检查是否存在报警
                if (CheckForAlarms())
                {
                    return;
                }

                bool isRunning;
                lock (_lockObject)
                {
                    isRunning = _isRunning;
                }

                // 在lock块外执行异步操作
                if (isRunning)
                {
                    await StopAsync();
                }

                // 重置所有标志位和状态
                ResetSimulationState();

                // 重置气缸和状态
                await Task.Run(async () =>
                {
                    try
                    {
                        // 复位气缸
                        await ControlStopperAsync(false);
                        await ControlClampAsync(false);

                        // 停止皮带
                        _motionCard.StopAxisJog(_beltAxis.AxisNo);

                        // 停止调宽轴
                        _motionCard.StopAxisJog(_widthAxis.AxisNo);

                        // 初始化SMEMA信号
                        if (_isDebugMode)
                        {
                            await SetNextMachineReadyAsync(false);
                            await SetHasBoardAsync(false);
                            await SetReadyToReceiveAsync(false);
                        }

                        // 更新状态
                        lock (_lockObject)
                        {
                            // 清除错误状态
                            _currentState.ErrorMessage = string.Empty;
                            _currentState.CurrentState = "已复位";
                            _currentState.LastUpdateTime = DateTime.Now;
                            _transportState = BoardTransportState.Idle;
                        }
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred($"轨道复位失败: {ex.Message}");
                        throw;
                    }
                });

                OnStateChanged();
                _logService.LogInformation("轨道已复位", "轨道系统");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"轨道复位失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 重置所有模拟传感器状态
        /// </summary>
        private void ResetSimulationState()
        {
            // 取消正在进行的任何模拟任务
            _simulationCancellationTokenSource?.Cancel();
            _simulationCancellationTokenSource?.Dispose();
            _simulationCancellationTokenSource = new CancellationTokenSource();

            // 重置所有模拟传感器状态
            _simulatedIncomingSensor = false;
            _simulatedPositionSensor = false;
            _simulatedOutgoingSensor = false;

            // 重置所有模拟任务启动标志位
            _incomingSimulationStarted = false;
            _positionSimulationStarted = false;
            _outgoingSimulationStarted = false;

            OnMessageLogged("离线模拟：已重置所有模拟传感器状态");
        }

        /// <summary>
        /// 设置运行模式
        /// </summary>
        public async Task SetModeAsync(TrackMode mode)
        {
            try
            {
                await EnsureInitializedAsync();

                // 检查是否存在报警（仅在切换到Normal或Auto模式时检查）
                if (mode != TrackMode.Manual && CheckForAlarms())
                {
                    return;
                }

                if (_currentState.CurrentMode == mode)
                {
                    return;
                }

                try
                {
                    // 切换模式前先停止轨道
                    if (_isRunning)
                    {
                        await StopAsync();
                    }

                    // 更新模式
                    _currentState.CurrentMode = mode;
                    _currentState.LastUpdateTime = DateTime.Now;

                    if (mode == TrackMode.Manual)
                    {
                        _currentState.CurrentState = "手动模式";
                    }
                    else
                    {
                        _currentState.CurrentState = "正常模式";
                    }

                    OnStateChanged();
                    _logService.LogInformation($"轨道模式已切换为: {mode}", "轨道系统");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"切换模式失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"切换模式失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置运行方向
        /// </summary>
        public async Task SetDirectionAsync(TrackDirection direction)
        {
            try
            {
                await EnsureInitializedAsync();

                // 检查是否存在报警
                if (CheckForAlarms())
                {
                    return;
                }

                if (_currentState.Direction == direction)
                {
                    return;
                }

                try
                {
                    // 切换方向前先停止轨道
                    if (_isRunning)
                    {
                        await StopAsync();
                    }

                    // 更新方向
                    _currentState.Direction = direction;
                    _currentState.LastUpdateTime = DateTime.Now;

                    if (direction == TrackDirection.LeftToRight)
                    {
                        _currentState.CurrentState = "从左到右";
                    }
                    else
                    {
                        _currentState.CurrentState = "从右到左";
                    }

                    OnStateChanged();
                    _logService.LogInformation($"轨道方向已切换为: {direction}", "轨道系统");
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"切换方向失败: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"切换方向失败: {ex.Message}");
                throw;
            }
        }

        public async Task ControlStopperAsync(bool extend)
        {
            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _stopperCylinder.IoAddress,
                        _stopperCylinder.IoBitIndex,
                        extend
                    );
                });

                _currentState.StopperCylinder = extend;
                OnStateChanged();
                OnMessageLogged($"阻挡气缸已{(extend ? "伸出" : "缩回")}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"控制阻挡气缸失败: {ex.Message}");
                throw;
            }
        }

        public async Task ControlClampAsync(bool clamp)
        {
            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _clampCylinder.IoAddress,
                        _clampCylinder.IoBitIndex,
                        clamp
                    );
                });

                _currentState.ClampCylinder = clamp;
                OnStateChanged();
                OnMessageLogged($"夹紧气缸已{(clamp ? "夹紧" : "松开")}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"控制夹紧气缸失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 自动运行状态机
        /// </summary>
        private async Task AutoRunAsync(CancellationToken cancellationToken)
        {
            OnMessageLogged("自动运行状态机启动");

            // 只有在非暂停恢复的情况下才重置状态
            if (!_wasPaused)
            {
                EnterNewState(BoardTransportState.Idle);
            }
            else
            {
                // 从暂停恢复时，不调用EnterNewState以避免重置状态相关变量
                OnMessageLogged(
                    $"从暂停状态恢复，当前状态: {_transportState}，会继续执行该状态的逻辑"
                );

                // 确保延时标记被重置，避免跳过某些需要延时的操作
                _stateEnterTime = DateTime.Now;
                _delayCompleted = false;
            }

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 检查是否运行中
                    if (!_isRunning)
                    {
                        // 如果状态被设置为非运行，则退出状态机循环
                        OnMessageLogged("状态机停止运行");
                        break;
                    }

                    // 在BoardExiting状态下，优先检查出料传感器状态，提高响应速度
                    if (_transportState == BoardTransportState.BoardExiting)
                    {
                        // 主动更新一次传感器状态
                        UpdateSensors();

                        // 直接检测出料传感器的原始值
                        ushort inputsX1 = _motionCard.ReadInputsX1();
                        bool outgoingSensorRawValue =
                            (inputsX1 & (1 << _outgoingSensor.IoBitIndex)) != 0;

                        if (outgoingSensorRawValue || GetOutgoingSensorValue)
                        {
                            OnMessageLogged(
                                $"[快速响应] 检测到出料传感器触发，原始值：{outgoingSensorRawValue}，防抖值：{GetOutgoingSensorValue}"
                            );

                            // 立即停止传送带
                            try
                            {
                                await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                                OnMessageLogged("[快速响应] 出料传感器触发，立即停止传送带");
                            }
                            catch (Exception ex)
                            {
                                OnWarning($"[快速响应] 停止传送带失败: {ex.Message}");
                            }

                            // 根据SMEMA状态决定下一步
                            EnterNewState(
                                _currentState.SmemaEnabled
                                    ? BoardTransportState.WaitingForDownstream
                                    : BoardTransportState.CompleteExit_Start
                            );
                        }
                    }

                    // 正常的状态机处理
                    switch (_transportState)
                    {
                        case BoardTransportState.Idle:
                            if (_isRunning)
                            {
                                EnterNewState(BoardTransportState.WaitingForBoard_Init);
                            }
                            break;

                        case BoardTransportState.WaitingForBoard_Init:
                            if (_currentState.SmemaEnabled)
                            {
                                // 发送本机准备好信号
                                _motionCard.SetOutputBit(
                                    _machineReady.IoAddress,
                                    _machineReady.IoBitIndex,
                                    true
                                );
                                EnterNewState(BoardTransportState.WaitingForBoard_SMEMA);
                            }
                            else
                            {
                                EnterNewState(BoardTransportState.WaitingForBoard_Sensor);
                            }
                            break;

                        case BoardTransportState.WaitingForBoard_SMEMA:
                        case BoardTransportState.WaitingForBoard_Sensor:
                            if (GetIncomingSensorValue)
                            {
                                if (_currentState.SmemaEnabled)
                                {
                                    _motionCard.SetOutputBit(
                                        _boardAvailable.IoAddress,
                                        _boardAvailable.IoBitIndex,
                                        true
                                    );
                                    _currentState.HasBoard = true;
                                }
                                // 重置模拟启动标志位
                                _incomingSimulationStarted = false;
                                EnterNewState(BoardTransportState.BoardEntering);
                            }
                            else if (
                                _isOfflineSimulation
                                && (
                                    _transportState == BoardTransportState.WaitingForBoard_Sensor
                                    || _transportState == BoardTransportState.WaitingForBoard_SMEMA
                                )
                                && !_incomingSimulationStarted
                            )
                            {
                                // 标记模拟任务已启动
                                _incomingSimulationStarted = true;
                                // 在离线模拟模式下，启动入料传感器模拟
                                StartIncomingSensorSimulation();
                            }
                            break;

                        case BoardTransportState.BoardEntering:
                            // 启动传送带并打开阻拦气缸
                            // 注意：从暂停恢复时，皮带可能已经在ResumeAsync中启动，这里保持逻辑不变作为保险
                            await MoveBeltForwardAsync();
                            await ControlStopperAsync(true);
                            EnterNewState(BoardTransportState.BoardPositioning);
                            break;

                        case BoardTransportState.BoardPositioning:
                            // 检查是否从暂停恢复
                            if (_wasPaused)
                            {
                                // 如果是从暂停恢复且之前已经重置了_wasPaused标志，记录日志并继续
                                OnMessageLogged(
                                    "从暂停状态恢复BoardPositioning，继续检测到位传感器"
                                );
                                _wasPaused = false; // 清除标志，避免重复处理
                            }

                            if (GetPositionSensorValue)
                            {
                                // 重置模拟启动标志位
                                _positionSimulationStarted = false;
                                EnterNewState(BoardTransportState.BoardAligning_Start);
                            }
                            else if (_isOfflineSimulation && !_positionSimulationStarted)
                            {
                                // 标记模拟任务已启动
                                _positionSimulationStarted = true;
                                // 在离线模拟模式下，启动到位传感器模拟
                                StartPositionSensorSimulation();
                            }
                            break;

                        case BoardTransportState.BoardAligning_Start:
                            // 继续运行一段时间
                            if (IsDelayCompleted(BOARD_ALIGNING_DELAY))
                            {
                                EnterNewState(BoardTransportState.BoardAligning_Clamp);
                            }
                            break;

                        case BoardTransportState.BoardAligning_Clamp:
                            await ControlClampAsync(true);
                            if (IsDelayCompleted(CLAMP_OPERATION_DELAY))
                            {
                                EnterNewState(BoardTransportState.BoardAligning_Stop);
                            }
                            break;

                        case BoardTransportState.BoardAligning_Stop:
                            if (IsDelayCompleted(CONVEYOR_STOP_DELAY))
                            {
                                await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                                EnterNewState(
                                    _currentState.CurrentMode == TrackMode.PassThrough
                                        ? BoardTransportState.PrepareExit_Start
                                        : BoardTransportState.Processing_Ready
                                );
                            }
                            break;

                        case BoardTransportState.Processing_Ready:
                            // 触发产品就位事件，通知外部系统开始加工
                            OnBoardArrived(
                                new BoardArrivedEventArgs(
                                    $"Board_{DateTime.Now.ToString("yyyyMMddHHmmss")}",
                                    "Default",
                                    true
                                )
                            );
                            EnterNewState(BoardTransportState.Processing_Active);
                            break;

                        case BoardTransportState.Processing_Active:
                            // 这里可以添加等待外部系统加工完成的逻辑
                            //EnterNewState(BoardTransportState.Processing_Complete);
                            await Task.Delay(1000); //循环等待外部系统通知加工完成
                            OnMessageLogged("等待外部完成事件");
                            break;

                        case BoardTransportState.Processing_Complete:
                            EnterNewState(BoardTransportState.PrepareExit_Start);
                            break;

                        case BoardTransportState.PrepareExit_Start:
                            await ControlClampAsync(false);
                            if (IsDelayCompleted(CYLINDER_OPERATION_DELAY))
                            {
                                EnterNewState(BoardTransportState.PrepareExit_Release);
                            }
                            break;

                        case BoardTransportState.PrepareExit_Release:
                            await ControlStopperAsync(false);
                            if (IsDelayCompleted(CYLINDER_OPERATION_DELAY))
                            {
                                EnterNewState(BoardTransportState.PrepareExit_Conveyor);
                            }
                            break;

                        case BoardTransportState.PrepareExit_Conveyor:
                            if (IsDelayCompleted(CONVEYOR_START_DELAY))
                            {
                                await MoveBeltForwardAsync();
                                EnterNewState(BoardTransportState.BoardExiting);
                            }
                            break;

                        case BoardTransportState.BoardExiting:
                            // 直接检查出料传感器的原始值，不通过防抖处理，加快响应速度
                            ushort inputsX1 = _motionCard.ReadInputsX1();
                            bool outgoingSensorRawValue =
                                (inputsX1 & (1 << _outgoingSensor.IoBitIndex)) != 0;

                            // 使用原始值或已防抖值进行检测，任一为true就停止，提高响应性
                            if (outgoingSensorRawValue || GetOutgoingSensorValue)
                            {
                                // 检测到传感器触发，立即记录日志
                                OnMessageLogged(
                                    $"检测到出料传感器触发(直接读取)，原始值：{outgoingSensorRawValue}，防抖值：{GetOutgoingSensorValue}"
                                );

                                // 重置模拟启动标志位
                                _outgoingSimulationStarted = false;

                                // 不管SMEMA是否启用，先紧急停止传送带防止过度运行
                                try
                                {
                                    await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                                    OnMessageLogged("出料传感器触发，立即停止传送带");
                                }
                                catch (Exception ex)
                                {
                                    OnWarning($"停止传送带失败: {ex.Message}");
                                }

                                // 然后根据SMEMA状态决定下一步
                                if (_currentState.SmemaEnabled)
                                {
                                    EnterNewState(BoardTransportState.WaitingForDownstream);
                                }
                                else
                                {
                                    EnterNewState(BoardTransportState.CompleteExit_Start);
                                }
                            }
                            else if (_isOfflineSimulation && !_outgoingSimulationStarted)
                            {
                                // 标记模拟任务已启动
                                _outgoingSimulationStarted = true;
                                // 在离线模拟模式下，启动出料传感器模拟
                                StartOutgoingSensorSimulation();
                            }
                            break;

                        case BoardTransportState.WaitingForDownstream:
                            if (_sensorDebouncers["NextMachineReady"].Value)
                            {
                                EnterNewState(BoardTransportState.BoardExiting_SMEMA);
                            }
                            else if (_isOfflineSimulation)
                            {
                                // 在离线模拟模式下，模拟下游准备好信号
                                OnMessageLogged("离线模拟：模拟下游准备好信号");
                                // 延迟后模拟下游准备好信号
                                await Task.Delay(2000)
                                    .ContinueWith(_ =>
                                    {
                                        if (
                                            _transportState
                                            == BoardTransportState.WaitingForDownstream
                                        )
                                        {
                                            // 设置模拟的NextMachineReady值
                                            var debouncer = _sensorDebouncers["NextMachineReady"];
                                            typeof(SensorDebouncer)
                                                .GetMethod(
                                                    "Update",
                                                    System.Reflection.BindingFlags.Public
                                                        | System.Reflection.BindingFlags.Instance
                                                )
                                                .Invoke(debouncer, new object[] { true });
                                        }
                                    });
                            }
                            break;

                        case BoardTransportState.BoardExiting_SMEMA:
                            if (!GetOutgoingSensorValue)
                            {
                                EnterNewState(BoardTransportState.CompleteExit_Start);
                            }
                            break;

                        case BoardTransportState.CompleteExit_Start:
                            // 进入该状态时立即停止传送带，确保及时响应
                            await Task.Run(() =>
                            {
                                try
                                {
                                    _motionCard.StopAxisJog(_beltAxis.AxisNo);
                                    OnMessageLogged("出料完成，已停止传送带");
                                }
                                catch (Exception ex)
                                {
                                    OnWarning($"停止传送带失败: {ex.Message}");
                                }
                            });
                            EnterNewState(BoardTransportState.CompleteExit_Wait);
                            break;

                        case BoardTransportState.CompleteExit_Wait:
                            if (IsDelayCompleted(CONVEYOR_STOP_DELAY))
                            {
                                EnterNewState(BoardTransportState.CompleteExit_Stop);
                            }
                            break;

                        case BoardTransportState.CompleteExit_Stop:
                            EnterNewState(BoardTransportState.ResetState_Start);
                            break;

                        case BoardTransportState.ResetState_Start:
                            await ControlStopperAsync(false);
                            await ControlClampAsync(false);
                            EnterNewState(BoardTransportState.ResetState_SMEMA);
                            break;

                        case BoardTransportState.ResetState_SMEMA:
                            if (_currentState.SmemaEnabled)
                            {
                                _motionCard.SetOutputBit(
                                    _boardAvailable.IoAddress,
                                    _boardAvailable.IoBitIndex,
                                    false
                                );
                                _currentState.HasBoard = false;
                            }
                            EnterNewState(BoardTransportState.ResetState_Complete);
                            break;

                        case BoardTransportState.ResetState_Complete:
                            EnterNewState(BoardTransportState.Idle);
                            break;

                        case BoardTransportState.Error:
                            OnWarning("系统处于错误状态，请复位");
                            await Task.Delay(1000, cancellationToken);
                            break;
                    }

                    // 更新状态显示
                    _currentState.TransportState = _transportState.ToString();
                    OnStateChanged();

                    // 控制循环间隔，减少延迟提高响应速度
                    await Task.Delay(20, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnErrorOccurred($"自动运行出错: {ex.Message}");
                    EnterNewState(BoardTransportState.Error);
                    await Task.Delay(1000, cancellationToken);
                }
            }

            OnMessageLogged("自动运行状态机已停止");
        }

        /// <summary>
        /// 启动传感器监控
        /// </summary>
        private void StartSensorMonitoring()
        {
            // 如果任务已在运行，先停止
            StopSensorMonitoring();

            _sensorMonitorCts = new CancellationTokenSource();

            _sensorMonitorTask = Task.Run(
                async () =>
                {
                    try
                    {
                        while (!_sensorMonitorCts.Token.IsCancellationRequested)
                        {
                            try
                            {
                                // 只有在运行或调试模式下才更新传感器状态
                                if (_isRunning || _isDebugMode)
                                {
                                    UpdateSensors();
                                }
                            }
                            catch (Exception ex)
                            {
                                OnErrorOccurred($"更新传感器状态失败: {ex.Message}");
                            }

                            // 等待指定的时间间隔
                            await Task.Delay(SENSOR_UPDATE_INTERVAL_MS, _sensorMonitorCts.Token);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，忽略
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred($"传感器监控任务异常: {ex.Message}");
                    }
                },
                _sensorMonitorCts.Token
            );
        }

        // 停止传感器监控
        private void StopSensorMonitoring()
        {
            if (_sensorMonitorCts != null)
            {
                try
                {
                    _sensorMonitorCts.Cancel();

                    // 等待任务完成
                    if (_sensorMonitorTask != null)
                    {
                        Task.WaitAny(new[] { _sensorMonitorTask }, 1000);
                    }
                }
                catch
                {
                    // 忽略取消异常
                }
                finally
                {
                    _sensorMonitorCts.Dispose();
                    _sensorMonitorCts = null;
                }
            }
        }

        /// <summary>
        /// 更新传感器状态
        /// </summary>
        private void UpdateSensors()
        {
            try
            {
                // 一次性读取所有输入和输出寄存器，减少IO操作次数
                ushort inputsX0 = _motionCard.ReadInputsX0();
                ushort inputsX1 = _motionCard.ReadInputsX1();
                ushort outputsY0 = _motionCard.ReadOutputsY0();
                ushort outputsY1 = _motionCard.ReadOutputsY1();

                // 从寄存器值中提取各个传感器状态
                bool incomingSensorRaw = (inputsX0 & (1 << _incomingSensor.IoBitIndex)) != 0;
                bool positionSensorRaw = (inputsX1 & (1 << _positionSensor.IoBitIndex)) != 0;
                bool outgoingSensorRaw = (inputsX1 & (1 << _outgoingSensor.IoBitIndex)) != 0;
                bool nextMachineReadyRaw = (inputsX1 & (1 << _nextMachineReady.IoBitIndex)) != 0;

                // 检查是否在BoardExiting状态并且出料传感器发生了变化，这种情况下添加详细日志
                bool previousOutgoingValue = _currentState.BoardOutSensor;
                if (
                    _transportState == BoardTransportState.BoardExiting
                    && previousOutgoingValue != outgoingSensorRaw
                )
                {
                    OnMessageLogged(
                        $"出料传感器状态变化: {previousOutgoingValue} -> {outgoingSensorRaw}, 当前状态: {_transportState}, SMEMA启用: {_currentState.SmemaEnabled}"
                    );
                }

                // 更新防抖动器
                _sensorDebouncers["IncomingSensor"].Update(incomingSensorRaw);
                _sensorDebouncers["PositionSensor"].Update(positionSensorRaw);
                _sensorDebouncers["OutgoingSensor"].Update(outgoingSensorRaw);
                _sensorDebouncers["NextMachineReady"].Update(nextMachineReadyRaw);

                // 根据传送方向更新状态
                _currentState.BoardInSensor = GetIncomingSensorValue;
                _currentState.BoardArrivedSensor = GetPositionSensorValue;
                _currentState.BoardOutSensor = GetOutgoingSensorValue;
                _currentState.NextMachineReady = _sensorDebouncers["NextMachineReady"].Value;

                // 更新SMEMA信号状态（不需要根据方向改变）
                _currentState.HasBoard = (outputsY1 & (1 << _boardAvailable.IoBitIndex)) != 0;
                _currentState.ReadyToReceive = (outputsY1 & (1 << _machineReady.IoBitIndex)) != 0;

                // 更新状态
                if (System.Windows.Application.Current != null)
                {
                    System.Windows.Application.Current.Dispatcher.BeginInvoke(
                        (Action)(
                            () =>
                            {
                                OnStateChanged();
                            }
                        )
                    );
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"更新传感器状态失败: {ex.Message}");
            }
        }

        private async Task EnsureInitializedAsync()
        {
            if (!_isInitialized)
            {
                await InitializeAsync();
            }
        }

        protected virtual void OnStateChanged()
        {
            _currentState.LastUpdateTime = DateTime.Now;
            StateChanged?.Invoke(this, _currentState);

            // 更新设备指示灯状态
            UpdateIndicators();
        }

        /// <summary>
        /// 更新设备指示灯状态
        /// </summary>
        private void UpdateIndicators()
        {
            try
            {
                if (_statusManager == null)
                    return;

                Task.Run(async () =>
                {
                    try
                    {
                        // 根据设备状态设置指示灯
                        switch (_currentState.CurrentState)
                        {
                            case "运行中":
                                await _statusManager.SetNormalState();
                                break;
                            case "已停止":
                            case "已暂停":
                                await _statusManager.SetStandbyState();
                                break;
                            case "错误":
                            case "紧急停止":
                                await _statusManager.SetErrorState(true);
                                break;
                            default:
                                // 默认为待机状态
                                await _statusManager.SetStandbyState();
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        OnWarning($"更新指示灯状态失败: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                OnWarning($"更新指示灯状态异常: {ex.Message}");
            }
        }

        protected virtual void OnErrorOccurred(string message)
        {
            _logService.LogError(message, "轨道系统");
            ErrorOccurred?.Invoke(this, message);

            // 更新状态
            _currentState.CurrentState = $"错误: {message}";
            _currentState.LastUpdateTime = DateTime.Now;

            // 触发状态变更事件
            OnStateChanged();
        }

        protected virtual void OnWarning(string message)
        {
            WarningOccurred?.Invoke(this, message);
        }

        protected virtual void OnMessageLogged(string message)
        {
            MessageLogged?.Invoke(this, message);
        }

        /// <summary>
        /// 触发产品就位事件
        /// </summary>
        /// <param name="args">事件参数</param>
        protected virtual void OnBoardArrived(BoardArrivedEventArgs args)
        {
            BoardArrived?.Invoke(this, args);
            OnMessageLogged($"产品就位事件已触发，产品ID: {args.BoardId}");
        }

        /// <summary>
        /// 通知轨道服务检查已完成
        /// </summary>
        /// <param name="isPass">检查是否通过</param>
        /// <returns>操作任务</returns>
        public async Task NotifyInspectionCompletedAsync(bool isPass)
        {
            await EnsureInitializedAsync();

            try
            {
                // 记录检查结果
                OnMessageLogged($"收到检查完成通知，结果: {(isPass ? "通过" : "不通过")}");

                // 如果当前状态是加工中，则转换到加工完成状态
                if (_transportState == BoardTransportState.Processing_Active)
                {
                    EnterNewState(BoardTransportState.Processing_Complete);

                    // 如果检查不通过，可以在这里添加特殊处理逻辑
                    if (!isPass)
                    {
                        OnWarning("检查结果不通过，请注意处理");
                    }
                }
                else
                {
                    OnWarning($"收到检查完成通知，但当前状态不是加工中: {_transportState}");
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"处理检查完成通知时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置SMEMA功能启用状态
        /// </summary>
        /// <param name="enabled">是否启用SMEMA功能</param>
        public async Task SetSmemaEnabledAsync(bool enabled)
        {
            await EnsureInitializedAsync();

            if (_currentState.SmemaEnabled != enabled)
            {
                _currentState.SmemaEnabled = enabled;
                OnMessageLogged($"SMEMA功能已{(enabled ? "启用" : "禁用")}");

                // 如果禁用SMEMA，清除所有SMEMA信号
                if (!enabled)
                {
                    try
                    {
                        await Task.Run(() =>
                        {
                            // 清除本机准备好信号
                            if (_machineReady != null)
                            {
                                _motionCard.SetOutputBit(
                                    _machineReady.IoAddress,
                                    _machineReady.IoBitIndex,
                                    false
                                );
                            }

                            // 清除本机有板信号
                            if (_boardAvailable != null)
                            {
                                _motionCard.SetOutputBit(
                                    _boardAvailable.IoAddress,
                                    _boardAvailable.IoBitIndex,
                                    false
                                );
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        OnWarning($"清除SMEMA信号时出错: {ex.Message}");
                    }
                }

                OnStateChanged();
            }
        }

        /// <summary>
        /// 设置调试模式状态
        /// </summary>
        public async Task SetDebugModeAsync(bool enabled)
        {
            await EnsureInitializedAsync();

            if (_isRunning)
            {
                throw new InvalidOperationException("无法在轨道运行时切换调试模式");
            }

            if (_isDebugMode != enabled)
            {
                // 如果退出调试模式，确保所有轴都停止
                if (!enabled)
                {
                    try
                    {
                        await Task.Run(() =>
                        {
                            // 停止皮带轴
                            _motionCard.StopAxisJog(_beltAxis.AxisNo);

                            // 停止调宽轴
                            _motionCard.StopAxisJog(_widthAxis.AxisNo);
                        });
                    }
                    catch (Exception ex)
                    {
                        OnWarning($"退出调试模式时停止轴运动出错: {ex.Message}");
                    }
                }

                _isDebugMode = enabled;
                _currentState.IsDebugMode = enabled;
                OnMessageLogged($"调试模式已{(enabled ? "启用" : "禁用")}");
                OnStateChanged();
            }
        }

        /// <summary>
        /// 手动控制皮带运动
        /// </summary>
        /// <param name="direction">运动方向，1表示正向，-1表示反向，0表示停止</param>
        /// <param name="speed">运动速度</param>
        public async Task ManualControlBeltAsync(int direction, int speed)
        {
            await EnsureInitializedAsync();

            if (!_currentState.IsDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下手动控制皮带");
            }

            try
            {
                await Task.Run(() =>
                    {
                        if (direction == 0)
                        {
                            // 停止皮带
                            _motionCard.StopAxisJog(_beltAxis.AxisNo);
                        }
                        else
                        {
                            // 设置速度
                            _motionCard.SetStartSpeed(_beltAxis.AxisNo, (int)_beltAxis.StartSpeed);
                            _motionCard.SetMaxSpeed(_beltAxis.AxisNo, speed);

                            // 根据方向和传送方向设置启动
                            if (direction > 0)
                            {
                                if (IsLeftToRight)
                                {
                                    _motionCard.MoveAxisJogForward(_beltAxis.AxisNo);
                                }
                                else
                                {
                                    _motionCard.MoveAxisJogBackward(_beltAxis.AxisNo);
                                }
                            }
                            else
                            {
                                if (IsLeftToRight)
                                {
                                    _motionCard.MoveAxisJogBackward(_beltAxis.AxisNo);
                                }
                                else
                                {
                                    _motionCard.MoveAxisJogForward(_beltAxis.AxisNo);
                                }
                            }
                        }
                    })
                    .ConfigureAwait(false);

                if (direction == 0)
                {
                    OnMessageLogged("皮带已停止");
                }
                else
                {
                    string directionStr = direction > 0 ? "正向" : "反向";
                    OnMessageLogged($"皮带{directionStr}运动，速度: {speed}");
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"控制皮带时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 手动控制调宽轴运动
        /// </summary>
        /// <param name="direction">运动方向，1表示正向，-1表示反向，0表示停止</param>
        /// <param name="speed">运动速度</param>
        public async Task ManualControlWidthAxisAsync(int direction, int speed)
        {
            await EnsureInitializedAsync();

            if (!_currentState.IsDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下手动控制调宽轴");
            }

            try
            {
                await Task.Run(() =>
                    {
                        if (direction == 0)
                        {
                            // 停止调宽轴
                            _motionCard.StopAxisJog(_widthAxis.AxisNo);
                        }
                        else
                        {
                            // 设置速度
                            _motionCard.SetStartSpeed(
                                _widthAxis.AxisNo,
                                (int)_widthAxis.StartSpeed
                            );
                            _motionCard.SetMaxSpeed(_widthAxis.AxisNo, speed);

                            // 根据方向启动调宽轴
                            if (direction > 0)
                            {
                                _motionCard.MoveAxisJogForward(_widthAxis.AxisNo);
                            }
                            else
                            {
                                _motionCard.MoveAxisJogBackward(_widthAxis.AxisNo);
                            }
                        }
                    })
                    .ConfigureAwait(false);

                if (direction == 0)
                {
                    OnMessageLogged("调宽轴已停止");
                }
                else if (direction > 0)
                {
                    OnMessageLogged($"调宽轴正向运动，速度: {speed}");
                }
                else
                {
                    OnMessageLogged($"调宽轴反向运动，速度: {speed}");
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"控制调宽轴时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 调宽轴回零
        /// </summary>
        public async Task HomeWidthAxisAsync()
        {
            await EnsureInitializedAsync();

            if (!_currentState.IsDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下控制调宽轴回零");
            }

            try
            {
                await Task.Run(() =>
                    {
                        _motionCard.HomeAxis(_widthAxis.AxisNo);
                    })
                    .ConfigureAwait(false);

                OnMessageLogged("调宽轴回零命令已发送");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"调宽轴回零出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置皮带速度
        /// </summary>
        /// <param name="speed">速度值</param>
        public async Task SetBeltSpeedAsync(int speed)
        {
            await EnsureInitializedAsync();

            if (speed < 0)
            {
                throw new ArgumentException("速度不能为负值", nameof(speed));
            }

            _currentState.BeltSpeed = speed;
            OnMessageLogged($"皮带速度已设置为: {speed}");
            OnStateChanged();
        }

        /// <summary>
        /// 设置调宽轴速度
        /// </summary>
        /// <param name="speed">速度值</param>
        public async Task SetWidthAxisSpeedAsync(int speed)
        {
            await EnsureInitializedAsync();

            if (speed < 0)
            {
                throw new ArgumentException("速度不能为负值", nameof(speed));
            }

            _currentState.WidthAxisSpeed = speed;
            OnMessageLogged($"调宽轴速度已设置为: {speed}");
            OnStateChanged();
        }

        /// <summary>
        /// 检查状态延时是否完成
        /// </summary>
        private bool IsDelayCompleted(int delayMs)
        {
            if (_delayCompleted)
                return true;

            if ((DateTime.Now - _stateEnterTime).TotalMilliseconds >= delayMs)
            {
                _delayCompleted = true;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 进入新状态时重置延时
        /// </summary>
        private void EnterNewState(BoardTransportState newState)
        {
            var previousState = _transportState;
            _transportState = newState;
            _stateEnterTime = DateTime.Now;
            _delayCompleted = false;
            OnMessageLogged($"进入状态: {newState}");

            // 重置模拟传感器触发标志
            if (_isOfflineSimulation)
            {
                // 当离开等待入板状态时，重置入料传感器的模拟触发标志
                if (
                    (
                        previousState == BoardTransportState.WaitingForBoard_SMEMA
                        || previousState == BoardTransportState.WaitingForBoard_Sensor
                    )
                    && newState != BoardTransportState.WaitingForBoard_SMEMA
                    && newState != BoardTransportState.WaitingForBoard_Sensor
                )
                {
                    _incomingSimulationStarted = false;
                    OnMessageLogged("重置入料传感器模拟触发状态");
                }

                // 当离开进板相关状态时，重置定位传感器的模拟触发标志
                if (
                    previousState == BoardTransportState.BoardEntering
                    && newState != BoardTransportState.BoardEntering
                )
                {
                    _positionSimulationStarted = false;
                    OnMessageLogged("重置定位传感器模拟触发状态");
                }

                // 当离开加工或出板准备状态时，重置出料传感器的模拟触发标志
                if (
                    (
                        previousState == BoardTransportState.Processing_Active
                        || previousState == BoardTransportState.PrepareExit_Start
                    )
                    && newState != BoardTransportState.Processing_Active
                    && newState != BoardTransportState.PrepareExit_Start
                )
                {
                    _outgoingSimulationStarted = false;
                    OnMessageLogged("重置出料传感器模拟触发状态");
                }
            }
        }

        /// <summary>
        /// 设置入料传感器状态（仅调试模式）
        /// </summary>
        public async Task SetBoardInSensorAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置传感器状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _incomingSensor.IoAddress,
                        _incomingSensor.IoBitIndex,
                        state
                    );
                });
                _currentState.BoardInSensor = state;
                OnStateChanged();
                OnMessageLogged($"入料传感器状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置入料传感器状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置到位传感器状态（仅调试模式）
        /// </summary>
        public async Task SetBoardArrivedSensorAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置传感器状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _positionSensor.IoAddress,
                        _positionSensor.IoBitIndex,
                        state
                    );
                });
                _currentState.BoardArrivedSensor = state;
                OnStateChanged();
                OnMessageLogged($"到位传感器状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置到位传感器状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置出料传感器状态（仅调试模式）
        /// </summary>
        public async Task SetBoardOutSensorAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置传感器状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _outgoingSensor.IoAddress,
                        _outgoingSensor.IoBitIndex,
                        state
                    );
                });
                _currentState.BoardOutSensor = state;
                OnStateChanged();
                OnMessageLogged($"出料传感器状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置出料传感器状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置阻挡气缸状态（仅调试模式）
        /// </summary>
        public async Task SetStopperCylinderAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置气缸状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _stopperCylinder.IoAddress,
                        _stopperCylinder.IoBitIndex,
                        state
                    );
                });
                _currentState.StopperCylinder = state;
                OnStateChanged();
                OnMessageLogged($"阻挡气缸状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置阻挡气缸状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置夹紧气缸状态（仅调试模式）
        /// </summary>
        public async Task SetClampCylinderAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置气缸状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _clampCylinder.IoAddress,
                        _clampCylinder.IoBitIndex,
                        state
                    );
                });
                _currentState.ClampCylinder = state;
                OnStateChanged();
                OnMessageLogged($"夹紧气缸状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置夹紧气缸状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置下游准备好状态（仅调试模式）
        /// </summary>
        public async Task SetNextMachineReadyAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置SMEMA状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _nextMachineReady.IoAddress,
                        _nextMachineReady.IoBitIndex,
                        state
                    );
                });
                _currentState.NextMachineReady = state;
                OnStateChanged();
                OnMessageLogged($"下游准备好状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置下游准备好状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置本机有板状态（仅调试模式）
        /// </summary>
        public async Task SetHasBoardAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置SMEMA状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _boardAvailable.IoAddress,
                        _boardAvailable.IoBitIndex,
                        state
                    );
                });
                _currentState.HasBoard = state;
                OnStateChanged();
                OnMessageLogged($"本机有板状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置本机有板状态失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 设置本机准备好状态（仅调试模式）
        /// </summary>
        public async Task SetReadyToReceiveAsync(bool state)
        {
            await EnsureInitializedAsync();
            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下设置SMEMA状态");
            }

            try
            {
                await Task.Run(() =>
                {
                    _motionCard.SetOutputBit(
                        _machineReady.IoAddress,
                        _machineReady.IoBitIndex,
                        state
                    );
                });
                _currentState.ReadyToReceive = state;
                OnStateChanged();
                OnMessageLogged($"本机准备好状态已设置为: {state}");
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"设置本机准备好状态失败: {ex.Message}");
                throw;
            }
        }

        // 修改皮带运动相关的方法
        private async Task MoveBeltForwardAsync()
        {
            await Task.Run(() =>
            {
                if (IsLeftToRight)
                {
                    _motionCard.MoveAxisJogForward(_beltAxis.AxisNo);
                }
                else
                {
                    _motionCard.MoveAxisJogBackward(_beltAxis.AxisNo);
                }
            });
        }

        private async Task MoveBeltBackwardAsync()
        {
            await Task.Run(() =>
            {
                if (IsLeftToRight)
                {
                    _motionCard.MoveAxisJogBackward(_beltAxis.AxisNo);
                }
                else
                {
                    _motionCard.MoveAxisJogForward(_beltAxis.AxisNo);
                }
            });
        }

        /// <summary>
        /// 设置机器配置助手
        /// </summary>
        /// <param name="machineConfig">机器配置助手实例</param>
        public void SetMachineConfiguration(MachineConfigurationHelper machineConfig)
        {
            _machineConfig = machineConfig;

            // 检查并设置离线模拟模式
            if (_machineConfig != null)
            {
                SetOfflineSimulationMode(_machineConfig.IsOfflineSimulationEnabled());
            }
        }

        /// <summary>
        /// 设置离线模拟模式
        /// </summary>
        /// <param name="enabled">是否启用离线模拟</param>
        public void SetOfflineSimulationMode(bool enabled)
        {
            if (_isOfflineSimulation == enabled)
                return;

            _isOfflineSimulation = enabled;

            // 重置所有模拟传感器状态
            _simulatedIncomingSensor = false;
            _simulatedPositionSensor = false;
            _simulatedOutgoingSensor = false;

            if (enabled)
            {
                OnMessageLogged("轨道服务已进入离线模拟模式，将使用模拟传感器信号");
            }
            else
            {
                OnMessageLogged("轨道服务已退出离线模拟模式，将使用实际硬件传感器信号");

                // 取消任何正在进行的模拟任务
                _simulationCancellationTokenSource?.Cancel();
                _simulationCancellationTokenSource?.Dispose();
                _simulationCancellationTokenSource = null;
            }

            // 更新当前状态
            _currentState.IsOfflineSimulation = enabled;
            OnStateChanged();
        }

        /// <summary>
        /// 在离线模拟模式下启动入料传感器模拟
        /// </summary>
        private void StartIncomingSensorSimulation()
        {
            if (_simulatedIncomingSensor)
                return;

            OnMessageLogged(
                $"离线模拟：启动入料传感器模拟，将在 {SIMULATION_INCOMING_DELAY}ms 后触发"
            );

            // 取消之前的模拟任务（如果有）
            _simulationCancellationTokenSource?.Cancel();
            _simulationCancellationTokenSource?.Dispose();
            _simulationCancellationTokenSource = new CancellationTokenSource();

            // 启动新的模拟任务
            _simulationTask = Task.Run(
                async () =>
                {
                    try
                    {
                        await Task.Delay(
                            SIMULATION_INCOMING_DELAY,
                            _simulationCancellationTokenSource.Token
                        );

                        // 模拟传感器触发
                        _simulatedIncomingSensor = true;
                        OnMessageLogged("离线模拟：入料传感器已触发");
                    }
                    catch (OperationCanceledException)
                    {
                        // 任务被取消，不做任何处理
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred($"离线模拟错误: {ex.Message}");
                    }
                },
                _simulationCancellationTokenSource.Token
            );
        }

        /// <summary>
        /// 在离线模拟模式下启动到位传感器模拟
        /// </summary>
        private void StartPositionSensorSimulation()
        {
            if (_simulatedPositionSensor)
                return;

            OnMessageLogged(
                $"离线模拟：启动到位传感器模拟，将在 {SIMULATION_POSITION_DELAY}ms 后触发"
            );

            // 取消之前的模拟任务（如果有）
            _simulationCancellationTokenSource?.Cancel();
            _simulationCancellationTokenSource?.Dispose();
            _simulationCancellationTokenSource = new CancellationTokenSource();

            // 启动新的模拟任务
            _simulationTask = Task.Run(
                async () =>
                {
                    try
                    {
                        await Task.Delay(
                            SIMULATION_POSITION_DELAY,
                            _simulationCancellationTokenSource.Token
                        );

                        // 模拟传感器触发
                        _simulatedPositionSensor = true;
                        OnMessageLogged("离线模拟：到位传感器已触发");
                    }
                    catch (OperationCanceledException)
                    {
                        // 任务被取消，不做任何处理
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred($"离线模拟错误: {ex.Message}");
                    }
                },
                _simulationCancellationTokenSource.Token
            );
        }

        /// <summary>
        /// 在离线模拟模式下启动出料传感器模拟
        /// </summary>
        private void StartOutgoingSensorSimulation()
        {
            if (_simulatedOutgoingSensor)
                return;

            OnMessageLogged(
                $"离线模拟：启动出料传感器模拟，将在 {SIMULATION_OUTGOING_DELAY}ms 后触发"
            );

            // 取消之前的模拟任务（如果有）
            _simulationCancellationTokenSource?.Cancel();
            _simulationCancellationTokenSource?.Dispose();
            _simulationCancellationTokenSource = new CancellationTokenSource();

            // 启动新的模拟任务
            _simulationTask = Task.Run(
                async () =>
                {
                    try
                    {
                        await Task.Delay(
                            SIMULATION_OUTGOING_DELAY,
                            _simulationCancellationTokenSource.Token
                        );

                        // 模拟传感器触发
                        _simulatedOutgoingSensor = true;
                        OnMessageLogged("离线模拟：出料传感器已触发");

                        // 延迟后自动关闭出料传感器信号（模拟板子通过）
                        await Task.Delay(2000, _simulationCancellationTokenSource.Token);
                        _simulatedOutgoingSensor = false;
                        OnMessageLogged("离线模拟：出料传感器已关闭");
                    }
                    catch (OperationCanceledException)
                    {
                        // 任务被取消，不做任何处理
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred($"离线模拟错误: {ex.Message}");
                    }
                },
                _simulationCancellationTokenSource.Token
            );
        }

        /// <summary>
        /// 处理报警状态变更
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="alarms">当前活动报警列表</param>
        private void OnAlarmsChanged(object sender, List<AlarmInfo> alarms)
        {
            // 检查是否有严重报警（如伺服报警或运动异常）
            bool hasSeriousAlarm =
                alarms?.Any(a =>
                    a.AlarmType == AlarmType.ServoAlarm || a.AlarmType == AlarmType.MotionAlarm
                ) ?? false;

            // 如果有严重报警且轨道正在运行，则停止轨道
            if (hasSeriousAlarm && _isRunning)
            {
                _logService.LogWarning("检测到严重报警，停止轨道", "轨道系统");
                Task.Run(async () => await StopAsync()).ContinueWith(_ => { });
            }

            // 更新轨道状态中的报警信息
            if (_currentState != null)
            {
                int alarmCount = alarms != null ? alarms.Count : 0;

                // 如果有报警，更新状态描述
                if (alarmCount > 0 && _transportState != BoardTransportState.Error)
                {
                    EnterNewState(BoardTransportState.Error);
                    _currentState.CurrentState = "设备报警";
                    _currentState.LastUpdateTime = DateTime.Now;
                    OnStateChanged();
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _isRunning = false;

                // 停止状态机和模拟
                StopAsync().Wait();

                // 停止传感器监控
                StopSensorMonitoring();

                // 取消订阅报警事件
                if (_statusManager != null)
                {
                    _statusManager.AlarmsChanged -= OnAlarmsChanged;
                }

                _logService.LogInformation("轨道服务已释放资源", "轨道系统");
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "释放轨道服务资源时发生错误", "轨道系统");
            }
        }

        /// <summary>
        /// 手动进板（仅调试模式）
        /// </summary>
        public async Task ManualBoardInAsync()
        {
            await EnsureInitializedAsync();

            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下进行手动进板操作");
            }

            try
            {
                // 阻挡气缸升起
                await ControlStopperAsync(true);
                OnMessageLogged("手动进板：阻挡气缸已升起");

                // 启动传送带
                await MoveBeltForwardAsync();
                OnMessageLogged("手动进板：皮带已启动");

                // 在离线模拟模式下，模拟传感器触发；否则等待真实传感器
                if (_isOfflineSimulation)
                {
                    // 模拟入料传感器触发
                    _simulatedIncomingSensor = true;
                    OnMessageLogged("手动进板：模拟入料传感器已触发");

                    // 延时后模拟定位传感器触发
                    await Task.Delay(2000);
                    _simulatedPositionSensor = true;
                    OnMessageLogged("手动进板：模拟定位传感器已触发");
                }
                else
                {
                    // 等待真实的入料传感器触发
                    OnMessageLogged("手动进板：等待入料传感器触发...");

                    // 设置超时时间（30秒）
                    int timeoutMs = 30000;
                    int checkIntervalMs = 100;
                    int elapsedMs = 0;

                    // 等待入料传感器触发或超时
                    while (!_sensorDebouncers["IncomingSensor"].Value)
                    {
                        await Task.Delay(checkIntervalMs);
                        elapsedMs += checkIntervalMs;

                        if (elapsedMs >= timeoutMs)
                        {
                            OnMessageLogged("手动进板：等待入料传感器超时，操作取消");
                            // 停止传送带
                            await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                            return;
                        }
                    }

                    OnMessageLogged("手动进板：入料传感器已触发");

                    // 等待真实的定位传感器触发
                    OnMessageLogged("手动进板：等待定位传感器触发...");

                    // 重置超时
                    elapsedMs = 0;

                    // 等待定位传感器触发或超时
                    while (!_sensorDebouncers["PositionSensor"].Value)
                    {
                        await Task.Delay(checkIntervalMs);
                        elapsedMs += checkIntervalMs;

                        if (elapsedMs >= timeoutMs)
                        {
                            OnMessageLogged("手动进板：等待定位传感器超时，操作取消");
                            // 停止传送带
                            await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                            return;
                        }
                    }

                    OnMessageLogged("手动进板：定位传感器已触发");
                }

                // 继续运行一小段时间，让板子完全到位
                await Task.Delay(500);

                // 停止传送带
                await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                OnMessageLogged("手动进板：板材已到位，皮带已停止");

                // 夹紧气缸动作
                await ControlClampAsync(true);
                OnMessageLogged("手动进板：夹紧气缸已动作");

                OnMessageLogged("手动进板流程完成");
            }
            catch (Exception ex)
            {
                // 确保出错时停止皮带
                try
                {
                    await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                }
                catch { }

                OnErrorOccurred($"手动进板失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 手动出板（仅调试模式）
        /// </summary>
        public async Task ManualBoardOutAsync()
        {
            await EnsureInitializedAsync();

            if (!_isDebugMode)
            {
                throw new InvalidOperationException("只能在调试模式下进行手动出板操作");
            }

            try
            {
                // 首先松开夹紧气缸
                await ControlClampAsync(false);
                OnMessageLogged("手动出板：夹紧气缸已松开");

                // 短暂延时，等待夹紧气缸动作完成
                await Task.Delay(300);

                // 缩回阻挡气缸
                await ControlStopperAsync(false);
                OnMessageLogged("手动出板：阻挡气缸已缩回");

                // 短暂延时，等待阻挡气缸动作完成
                await Task.Delay(300);

                // 创建一个取消标记源，用于在出料传感器触发时取消等待
                using (var cts = new CancellationTokenSource())
                {
                    // 创建一个任务来监视出料传感器状态
                    var sensorMonitorTask = Task.Run(
                        async () =>
                        {
                            try
                            {
                                // 设置超时时间（30秒）
                                int timeoutMs = 30000;
                                int elapsedMs = 0;
                                int checkIntervalMs = 10; // 减小检查间隔，提高响应速度

                                OnMessageLogged("手动出板：等待出料传感器触发...");

                                while (!cts.Token.IsCancellationRequested && elapsedMs < timeoutMs)
                                {
                                    // 直接读取出料传感器状态，不通过防抖处理，以提高响应速度
                                    ushort inputsX0 = _motionCard.ReadInputsX0();
                                    ushort inputsX1 = _motionCard.ReadInputsX1();

                                    bool outgoingSensorValue;
                                    if (IsLeftToRight)
                                    {
                                        // 如果是从左到右，使用实际的出料传感器
                                        outgoingSensorValue =
                                            (inputsX1 & (1 << _outgoingSensor.IoBitIndex)) != 0;
                                    }
                                    else
                                    {
                                        // 如果是从右到左，入料传感器变成出料传感器
                                        outgoingSensorValue =
                                            (inputsX0 & (1 << _incomingSensor.IoBitIndex)) != 0;
                                    }

                                    if (_isOfflineSimulation)
                                    {
                                        outgoingSensorValue = _simulatedOutgoingSensor;
                                    }

                                    if (outgoingSensorValue)
                                    {
                                        // 立即停止传送带
                                        await Task.Run(
                                            () => _motionCard.StopAxisJog(_beltAxis.AxisNo)
                                        );
                                        OnMessageLogged(
                                            "手动出板：检测到出料传感器触发，立即停止传送带"
                                        );
                                        return true; // 成功检测到传感器
                                    }

                                    await Task.Delay(checkIntervalMs, cts.Token);
                                    elapsedMs += checkIntervalMs;
                                }

                                if (elapsedMs >= timeoutMs)
                                {
                                    OnMessageLogged("手动出板：等待出料传感器超时");
                                    return false; // 超时
                                }

                                return false; // 被取消
                            }
                            catch (OperationCanceledException)
                            {
                                return false; // 被取消
                            }
                        },
                        cts.Token
                    );

                    // 启动传送带
                    await MoveBeltForwardAsync();
                    OnMessageLogged("手动出板：皮带已启动");

                    if (_isOfflineSimulation)
                    {
                        // 在离线模拟模式下，延时后模拟出料传感器触发
                        await Task.Delay(2000);
                        _simulatedOutgoingSensor = true;
                        OnMessageLogged("手动出板：模拟出料传感器已触发");

                        // 立即停止传送带
                        await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                        OnMessageLogged("手动出板：检测到模拟出料传感器触发，立即停止传送带");

                        // 取消监视任务
                        cts.Cancel();
                    }
                    else
                    {
                        try
                        {
                            // 等待监视任务完成
                            bool sensorTriggered = await sensorMonitorTask;

                            if (!sensorTriggered)
                            {
                                // 如果传感器未触发（超时或被取消），停止传送带
                                await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                                OnMessageLogged("手动出板：未检测到出料传感器触发，停止传送带");
                            }
                        }
                        catch
                        {
                            // 确保无论发生什么错误，都停止传送带
                            await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                            throw;
                        }
                    }
                }

                OnMessageLogged("手动出板流程完成");
            }
            catch (Exception ex)
            {
                // 确保出错时停止皮带
                try
                {
                    await Task.Run(() => _motionCard.StopAxisJog(_beltAxis.AxisNo));
                }
                catch { }

                OnErrorOccurred($"手动出板失败: {ex.Message}");
                throw;
            }
        }
    }

    /// <summary>
    /// 传感器防抖动类
    /// </summary>
    public class SensorDebouncer
    {
        private bool _currentValue = false;
        private bool _lastRawValue = false;
        private DateTime _lastChangeTime = DateTime.MinValue;
        private readonly int _debounceTimeMs;
        private string _name = ""; // 添加传感器名称字段用于日志

        public bool Value => _currentValue;

        public SensorDebouncer(int debounceTimeMs, string name = "")
        {
            _debounceTimeMs = debounceTimeMs;
            _name = name;
        }

        public void Update(bool rawValue)
        {
            // 如果原始值发生变化
            if (rawValue != _lastRawValue)
            {
                _lastRawValue = rawValue;
                _lastChangeTime = DateTime.Now;
            }

            // 如果原始值稳定超过防抖时间，则更新当前值
            if ((DateTime.Now - _lastChangeTime).TotalMilliseconds >= _debounceTimeMs)
            {
                // 如果值发生变化，记录这个变化（仅针对出料传感器进行额外记录）
                if (_currentValue != _lastRawValue && _name == "OutgoingSensor")
                {
                    // 这里不能直接使用_logService，因为SensorDebouncer不是TrackService的内部类
                    System.Diagnostics.Debug.WriteLine(
                        $"{_name} 传感器状态变化: {_currentValue} -> {_lastRawValue} [防抖后]"
                    );
                }
                _currentValue = _lastRawValue;
            }
        }
    }
}
