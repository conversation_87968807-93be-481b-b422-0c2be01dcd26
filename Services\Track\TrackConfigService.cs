using System;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Nickel_Inspect.Models.Track;

namespace Nickel_Inspect.Services.Track
{
    /// <summary>
    /// 轨道配置服务实现，使用JSON文件存储配置
    /// </summary>
    public class TrackConfigService : ITrackConfigService
    {
        private readonly string _configFilePath;
        private readonly ILogService _logService;
        private TrackConfig _cachedConfig;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logService">日志服务</param>
        public TrackConfigService(ILogService logService)
        {
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));

            // 配置文件路径为应用程序目录下的Config目录
            string configDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            _configFilePath = Path.Combine(configDir, "TrackConfig.json");
        }

        /// <summary>
        /// 加载轨道配置
        /// </summary>
        /// <returns>轨道配置对象</returns>
        public async Task<TrackConfig> LoadConfigAsync()
        {
            try
            {
                // 如果已经有缓存的配置，直接返回
                if (_cachedConfig != null)
                {
                    return _cachedConfig;
                }

                // 如果配置文件不存在，创建默认配置
                if (!File.Exists(_configFilePath))
                {
                    _cachedConfig = TrackConfig.CreateDefault();
                    await SaveConfigAsync(_cachedConfig).ConfigureAwait(false);
                    return _cachedConfig;
                }

                // 从文件读取配置
                string json = File.ReadAllText(_configFilePath);
                _cachedConfig = JsonConvert.DeserializeObject<TrackConfig>(json);

                // 如果反序列化失败，创建默认配置
                if (_cachedConfig == null)
                {
                    _cachedConfig = TrackConfig.CreateDefault();
                    await SaveConfigAsync(_cachedConfig).ConfigureAwait(false);
                }

                return _cachedConfig;
            }
            catch (Exception ex)
            {
                _logService.LogError($"加载轨道配置失败: {ex.Message}", "轨道配置服务");

                // 如果加载失败，返回默认配置但不保存
                return TrackConfig.CreateDefault();
            }
        }

        /// <summary>
        /// 保存轨道配置
        /// </summary>
        /// <param name="config">要保存的配置</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveConfigAsync(TrackConfig config)
        {
            if (config == null)
            {
                return false;
            }

            try
            {
                // 更新最后修改时间
                config.LastUpdated = DateTime.Now;

                // 序列化配置对象
                string json = JsonConvert.SerializeObject(config, Formatting.Indented);

                // 保存到文件
                File.WriteAllText(_configFilePath, json);

                // 更新缓存
                _cachedConfig = config;

                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"保存轨道配置失败: {ex.Message}", "轨道配置服务");
                return false;
            }
        }

        /// <summary>
        /// 加载轨道状态配置并应用到状态对象
        /// </summary>
        /// <param name="state">要应用配置的状态对象</param>
        /// <returns>应用是否成功</returns>
        public async Task<bool> LoadAndApplyConfigAsync(TrackStateData state)
        {
            if (state == null)
            {
                return false;
            }

            try
            {
                var config = await LoadConfigAsync().ConfigureAwait(false);
                config.ApplyToState(state);
                return true;
            }
            catch (Exception ex)
            {
                _logService.LogError($"应用轨道配置失败: {ex.Message}", "轨道配置服务");
                return false;
            }
        }

        /// <summary>
        /// 从当前状态保存配置
        /// </summary>
        /// <param name="state">当前状态对象</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveConfigFromStateAsync(TrackStateData state)
        {
            if (state == null)
            {
                return false;
            }

            try
            {
                var config = TrackConfig.FromState(state);
                return await SaveConfigAsync(config).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logService.LogError($"从状态保存轨道配置失败: {ex.Message}", "轨道配置服务");
                return false;
            }
        }

        /// <summary>
        /// 检查配置是否有变更
        /// </summary>
        /// <param name="state">当前状态</param>
        /// <returns>如果有变更返回true，否则返回false</returns>
        public async Task<bool> HasConfigChangedAsync(TrackStateData state)
        {
            if (state == null)
            {
                return false;
            }

            try
            {
                var currentConfig = await LoadConfigAsync().ConfigureAwait(false);

                // 检查各项配置是否有变更
                return currentConfig.DefaultMode != state.CurrentMode
                    || currentConfig.DefaultDirection != state.Direction
                    || currentConfig.SmemaEnabled != state.SmemaEnabled;
            }
            catch (Exception ex)
            {
                _logService.LogError($"检查配置变更失败: {ex.Message}", "轨道配置服务");
                return false;
            }
        }
    }
}
