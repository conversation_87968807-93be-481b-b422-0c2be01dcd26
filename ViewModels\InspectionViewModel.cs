using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using HandyControl.Controls;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.Track;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.Alarms;
using Nickel_Inspect.Services.Track;
using Prism.Commands;
using Prism.Mvvm;
using AlarmsLogMessageEventArgs = Nickel_Inspect.Services.Alarms.LogMessageEventArgs;
using MessageBox = HandyControl.Controls.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using MessageBoxImage = System.Windows.MessageBoxImage;
using ServicesLogMessageEventArgs = Nickel_Inspect.Services.LogMessageEventArgs;

namespace Nickel_Inspect.ViewModels
{
    public class InspectionViewModel : BindableBase, IDisposable
    {
        private readonly BkMotionCard _bkMottion;
        private readonly DatabaseService _databaseService;
        private readonly IInspectionService _inspectionService;
        private MachineConfigurationHelper _machineConfigurationHelper;
        private readonly ITrackService _trackService;
        private readonly ILogService _logService;
        private readonly StatusManager _statusManager;

        // 物理按钮状态
        private bool _startButtonState;
        private bool _resetButtonState;
        private System.Threading.Timer _buttonStateTimer;

        // 标记系统是否需要复位
        private bool _needsReset = true;

        // 日志过滤选项
        private bool _showInspectionLogs = true;
        private bool _showMotionCardLogs = true;
        private bool _showTrackLogs = true;
        private bool _showMachineLogs = true;
        private bool _showErrorLogs = true;
        private bool _showIndicatorLogs = true;
        private bool _showAlarmLogs = true; // 添加报警日志过滤选项

        // 报警相关属性
        private ObservableCollection<AlarmInfo> _activeAlarms;
        private AlarmInfo _selectedAlarm;
        private bool _hasActiveAlarms;
        private int _alarmCount;
        private bool _hasServoAlarms;
        private bool _hasMotionAlarms;

        public bool ShowInspectionLogs
        {
            get => _showInspectionLogs;
            set => SetProperty(ref _showInspectionLogs, value);
        }

        public bool ShowMotionCardLogs
        {
            get => _showMotionCardLogs;
            set => SetProperty(ref _showMotionCardLogs, value);
        }

        public bool ShowTrackLogs
        {
            get => _showTrackLogs;
            set => SetProperty(ref _showTrackLogs, value);
        }

        public bool ShowMachineLogs
        {
            get => _showMachineLogs;
            set => SetProperty(ref _showMachineLogs, value);
        }

        public bool ShowErrorLogs
        {
            get => _showErrorLogs;
            set => SetProperty(ref _showErrorLogs, value);
        }

        public bool ShowIndicatorLogs
        {
            get => _showIndicatorLogs;
            set => SetProperty(ref _showIndicatorLogs, value);
        }

        public bool ShowAlarmLogs
        {
            get => _showAlarmLogs;
            set => SetProperty(ref _showAlarmLogs, value);
        }

        // 报警状态属性
        public ObservableCollection<AlarmInfo> ActiveAlarms
        {
            get => _activeAlarms;
            set => SetProperty(ref _activeAlarms, value);
        }

        public AlarmInfo SelectedAlarm
        {
            get => _selectedAlarm;
            set => SetProperty(ref _selectedAlarm, value);
        }

        public bool HasActiveAlarms
        {
            get => _hasActiveAlarms;
            set => SetProperty(ref _hasActiveAlarms, value);
        }

        public int AlarmCount
        {
            get => _alarmCount;
            set => SetProperty(ref _alarmCount, value);
        }

        public bool HasServoAlarms
        {
            get => _hasServoAlarms;
            set => SetProperty(ref _hasServoAlarms, value);
        }

        public bool HasMotionAlarms
        {
            get => _hasMotionAlarms;
            set => SetProperty(ref _hasMotionAlarms, value);
        }

        // 报警文本颜色
        public Brush AlarmStatusColor
        {
            get
            {
                if (HasServoAlarms || HasMotionAlarms)
                    return Brushes.Red;
                else if (HasActiveAlarms)
                    return Brushes.Orange;
                else
                    return Brushes.Green;
            }
        }

        // 报警状态文本
        public string AlarmStatusText
        {
            get
            {
                if (HasServoAlarms)
                    return "伺服报警";
                else if (HasMotionAlarms)
                    return "运动报警";
                else if (HasActiveAlarms)
                    return $"报警中 ({AlarmCount})";
                else
                    return "正常";
            }
        }

        // 命令
        public DelegateCommand StartCommand { get; }
        public DelegateCommand PauseCommand { get; }
        public DelegateCommand StopCommand { get; }
        public DelegateCommand ResetCommand { get; }
        public DelegateCommand LoadProductModelsCommand { get; }
        public DelegateCommand ToggleOfflineSimulationCommand { get; }
        public DelegateCommand ResetAlarmsCommand { get; }

        // 检查进度
        private int _currentInspectionIndex = 18;
        private int _totalInspectionPoints = 20;
        public string InspectionProgress => $"{_currentInspectionIndex}/{_totalInspectionPoints}";

        // NG结果统计
        private int _ngCount = 1;
        public string NgCountDisplay => $"{_ngCount}/{_totalInspectionPoints}";

        // 综合判定
        private string _finalJudgment = "OK";
        public string FinalJudgment
        {
            get => _finalJudgment;
            set => SetProperty(ref _finalJudgment, value);
        }

        // 选择机种
        private ObservableCollection<ProductModel> _productModels;
        public ObservableCollection<ProductModel> ProductModels
        {
            get => _productModels;
            set => SetProperty(ref _productModels, value);
        }

        private ProductModel _selectedModel;
        public ProductModel SelectedModel
        {
            get => _selectedModel;
            set => SetProperty(ref _selectedModel, value);
        }

        // 检查结果列表
        private ObservableCollection<InspectionResult> _inspectionResults;
        public ObservableCollection<InspectionResult> InspectionResults
        {
            get => _inspectionResults;
            set => SetProperty(ref _inspectionResults, value);
        }

        // 当前选中的检查结果
        private InspectionResult _selectedResult;
        public InspectionResult SelectedResult
        {
            get => _selectedResult;
            set
            {
                if (SetProperty(ref _selectedResult, value) && value != null)
                {
                    // 加载对应的图像
                    LoadResultImage(value);
                }
            }
        }

        // 当前显示的图像
        private BitmapImage _currentImage;
        public BitmapImage CurrentImage
        {
            get => _currentImage;
            set => SetProperty(ref _currentImage, value);
        }

        // 是否仅采集模式
        private bool _isCaptureOnly;
        public bool IsCaptureOnly
        {
            get => _isCaptureOnly;
            set => SetProperty(ref _isCaptureOnly, value);
        }

        // 离线模拟模式
        private bool _isOfflineSimulation;
        public bool IsOfflineSimulation
        {
            get => _isOfflineSimulation;
            set
            {
                if (SetProperty(ref _isOfflineSimulation, value))
                {
                    // 更新配置
                    if (_machineConfigurationHelper != null)
                    {
                        _machineConfigurationHelper.SetOfflineSimulation(value);

                        // 直接通知TrackService更新离线模拟状态
                        if (_trackService != null)
                        {
                            _trackService.SetOfflineSimulationMode(value);
                        }

                        // 直接应用到InspectionService中的离线状态
                        // (InspectionService每次直接从配置读取，所以不需要额外调用方法)

                        AddLogMessage("信息", $"离线模拟模式已{(value ? "启用" : "禁用")}", "系统");
                    }
                }
            }
        }

        // 用于追踪系统是否已经复位的标志
        private bool _isSystemReset = false;

        // 物理按钮状态属性
        public bool StartButtonState
        {
            get => _startButtonState;
            set => SetProperty(ref _startButtonState, value);
        }

        public bool ResetButtonState
        {
            get => _resetButtonState;
            set => SetProperty(ref _resetButtonState, value);
        }

        public InspectionViewModel(
            BkMotionCard bkMottion,
            DatabaseService databaseService,
            ITrackService trackService,
            ILogService logService,
            IInspectionService inspectionService,
            StatusManager statusManager
        )
        {
            _bkMottion = bkMottion;
            _databaseService = databaseService;
            _trackService = trackService;
            _logService = logService;
            _inspectionService = inspectionService;
            _statusManager = statusManager;
            // _machineConfigurationHelper将在MainWindowViewModel中设置

            // 初始化报警集合
            ActiveAlarms = new ObservableCollection<AlarmInfo>();
            HasActiveAlarms = false;
            AlarmCount = 0;
            HasServoAlarms = false;
            HasMotionAlarms = false;

            // 初始化命令
            StartCommand = new DelegateCommand(ExecuteStart, CanExecuteStart);
            PauseCommand = new DelegateCommand(ExecutePause, CanExecutePause);
            StopCommand = new DelegateCommand(ExecuteStop, CanExecuteStop);
            ResetCommand = new DelegateCommand(ExecuteReset, CanExecuteReset);
            LoadProductModelsCommand = new DelegateCommand(async () => await LoadProductModels());
            ToggleOfflineSimulationCommand = new DelegateCommand(ExecuteToggleOfflineSimulation);
            ResetAlarmsCommand = new DelegateCommand(ExecuteResetAlarms, CanExecuteResetAlarms);

            // 订阅状态管理器的报警事件
            if (_statusManager != null)
            {
                _statusManager.AlarmsChanged += OnAlarmsChanged;
                AddLogMessage("信息", "已订阅报警状态变更事件", "系统");
            }

            // 订阅运动控制卡的日志事件
            _bkMottion.LogMessageReceived += OnMotionCardLogMessage;
            _bkMottion.LogErrorReceived += OnMotionCardLogError;
            _bkMottion.LogWarningReceived += OnMotionCardLogWarning;

            // 订阅检查服务的日志事件
            if (_inspectionService is InspectionService service)
            {
                service.LogMessageReceived += OnInspectionServiceLogReceived;
            }

            // 订阅轨道服务的日志事件
            if (_trackService != null)
            {
                _trackService.ErrorOccurred += OnTrackError;
                _trackService.WarningOccurred += OnTrackWarning;
                _trackService.MessageLogged += OnTrackMessage;
            }

            // 订阅指示灯服务的日志
            // 指示灯服务的日志已通过ILogService发送，在这里添加过滤条件
            // _logService在构造函数中已注入，可以直接使用

            // 添加一个新的过滤器显示属性
            ShowIndicatorLogs = true;

            // 订阅检查服务的事件
            if (_inspectionService != null)
            {
                _inspectionService.InspectionCompleted += OnInspectionCompleted;
                _inspectionService.InspectionPointCompleted += OnInspectionPointCompleted;
                _inspectionService.InspectionError += OnInspectionError;

                // 监听检查服务状态变化
                _inspectionService.StatusChanged += OnInspectionStatusChanged;
            }

            // 初始化数据
            InitializeData();

            // 添加一些初始日志
            AddLogMessage("信息", "系统初始化完成");
            AddLogMessage("信息", "等待开始检查...");

            // 初始化时更新按钮状态
            UpdateCommandsAvailability();
        }

        private void InitializeData()
        {
            string defaultImagePath = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory,
                "Images",
                "default.bmp"
            );

            // 确保默认图像文件存在
            Directory.CreateDirectory(
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images")
            );
            if (!File.Exists(defaultImagePath))
            {
                try
                {
                    // 创建一个简单的空文件作为默认图像
                    using (FileStream fs = File.Create(defaultImagePath))
                    {
                        // 写入BMP文件头
                        byte[] bmpHeader = new byte[]
                        {
                            0x42,
                            0x4D,
                            0x3A,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x36,
                            0x00,
                            0x00,
                            0x00,
                            0x28,
                            0x00,
                            0x00,
                            0x00,
                            0x01,
                            0x00,
                            0x00,
                            0x00,
                            0x01,
                            0x00,
                            0x00,
                            0x00,
                            0x01,
                            0x00,
                            0x18,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x04,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0x00,
                            0xFF,
                            0xFF,
                            0xFF,
                            0x00,
                        };
                        fs.Write(bmpHeader, 0, bmpHeader.Length);
                    }
                }
                catch
                {
                    // 忽略创建默认图像的错误
                }
            }

            // 初始化检查结果列表
            InspectionResults = new ObservableCollection<InspectionResult>
            {
                new InspectionResult
                {
                    PointName = "镍片",
                    Result = "NG",
                    ImagePath = defaultImagePath,
                },
                new InspectionResult
                {
                    PointName = "NTC",
                    Result = "OK",
                    ImagePath = defaultImagePath,
                },
                new InspectionResult
                {
                    PointName = "NTC",
                    Result = "OK",
                    ImagePath = defaultImagePath,
                },
            };

            // 默认选中第一个结果
            if (InspectionResults.Count > 0)
            {
                SelectedResult = InspectionResults[0];
            }

            // 加载机种列表
            LoadProductModelsCommand.Execute();
        }

        private async Task LoadProductModels()
        {
            try
            {
                var models = await _databaseService.GetProductModelsAsync();
                ProductModels = new ObservableCollection<ProductModel>(models);

                // 默认选中第一个机种
                if (ProductModels.Count > 0)
                {
                    SelectedModel = ProductModels[0];
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载机种失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private void LoadResultImage(InspectionResult result)
        {
            // 直接使用结果对象中的已加载图像
            if (result.Image != null)
            {
                CurrentImage = result.Image;
                return;
            }

            try
            {
                string imagePath = result.ImagePath;

                // 如果路径为空或文件不存在，使用默认图像
                if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
                {
                    string defaultImagePath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        "Images",
                        "default.bmp"
                    );
                    if (File.Exists(defaultImagePath))
                    {
                        imagePath = defaultImagePath;
                    }
                    else
                    {
                        AddLogMessage("警告", "默认图像文件不存在");
                        return;
                    }
                }

                // 加载图像
                BitmapImage image = new BitmapImage();
                image.BeginInit();
                image.CacheOption = BitmapCacheOption.OnLoad;
                image.UriSource = new Uri(imagePath, UriKind.RelativeOrAbsolute);
                image.EndInit();
                image.Freeze(); // 提高性能

                // 更新当前显示的图像
                CurrentImage = image;
                AddLogMessage("信息", $"已加载图像: {imagePath}");
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"加载图像失败: {ex.Message}");
            }
        }

        // 命令可否执行判断
        private bool CanExecuteStart()
        {
            // 只有在完成复位或者暂停状态下且没有任何报警才能执行开始/继续命令
            // 且在报警清除后必须先执行复位操作
            return (
                    _inspectionService.Status == InspectionStatus.Idle
                    || _inspectionService.Status == InspectionStatus.Paused
                )
                && !HasActiveAlarms // 没有任何报警
                && !_needsReset; // 不需要复位（已经复位过）
        }

        private bool CanExecutePause()
        {
            // 只要在检查进行中状态都可以执行暂停命令，不受轻微报警影响
            // 但在有任何报警的情况下都不允许操作
            return _inspectionService.Status == InspectionStatus.Inspecting && !HasActiveAlarms;
        }

        private bool CanExecuteStop()
        {
            // 在检查中或暂停状态下都可以执行停止命令，不受报警影响
            // 但在有任何报警的情况下都不允许操作
            return (
                    _inspectionService.Status == InspectionStatus.Inspecting
                    || _inspectionService.Status == InspectionStatus.Paused
                ) && !HasActiveAlarms;
        }

        private bool CanExecuteReset()
        {
            // 系统初始状态或完成或错误状态下且没有任何报警可以执行复位
            return (
                    _inspectionService.Status == InspectionStatus.Idle
                    || _inspectionService.Status == InspectionStatus.Completed
                    || _inspectionService.Status == InspectionStatus.Error
                ) && !HasActiveAlarms; // 只要有任何报警都不允许复位
        }

        // 更新命令可用状态
        private void UpdateCommandsAvailability()
        {
            // 先记录当前各个命令的可用状态
            bool canStart = CanExecuteStart();
            bool canPause = CanExecutePause();
            bool canStop = CanExecuteStop();
            bool canReset = CanExecuteReset();
            bool canResetAlarms = CanExecuteResetAlarms();

            // 如果有报警状态，强制禁用除复位报警外的所有命令
            if (HasActiveAlarms)
            {
                // 确认只有复位报警按钮可用
                AddLogMessage(
                    "调试",
                    $"命令状态更新 - 报警状态：开始={canStart}, 暂停={canPause}, 停止={canStop}, 复位={canReset}, 清除异常={canResetAlarms}",
                    "系统"
                );
            }
            else if (_needsReset)
            {
                // 报警已清除但需要复位
                AddLogMessage(
                    "调试",
                    $"命令状态更新 - 需要复位状态：开始={canStart}, 暂停={canPause}, 停止={canStop}, 复位={canReset}, 清除异常={canResetAlarms}",
                    "系统"
                );
            }

            // 更新所有命令的状态
            StartCommand.RaiseCanExecuteChanged();
            StopCommand.RaiseCanExecuteChanged();
            PauseCommand.RaiseCanExecuteChanged();
            ResetCommand.RaiseCanExecuteChanged();
            ResetAlarmsCommand.RaiseCanExecuteChanged();

            // 再次检查按钮状态是否符合预期
            if (HasActiveAlarms)
            {
                // 在报警状态下，只有复位按钮可用，其他按钮全部禁用
                AddLogMessage("信息", "报警状态下，只有清除异常按钮可用，其他按钮已禁用", "系统");
            }
            else if (_needsReset)
            {
                // 报警已清除但需要复位
                AddLogMessage("信息", "报警已清除，需要执行系统复位，启动按钮已禁用", "系统");
            }
        }

        private async void ExecuteStart()
        {
            try
            {
                // 如果当前是暂停状态，则继续检查
                if (_inspectionService.Status == InspectionStatus.Paused)
                {
                    AddLogMessage("信息", "继续执行检查...");

                    // 使用Task.WhenAll同时恢复轨道和检查服务，即使其中一个失败也不会影响另一个
                    var resumeTasks = new List<Task>();

                    try
                    {
                        // 继续轨道
                        resumeTasks.Add(_trackService.ResumeAsync());
                        AddLogMessage("信息", "已发送恢复轨道命令");
                    }
                    catch (Exception ex)
                    {
                        AddLogMessage("警告", $"恢复轨道失败: {ex.Message}");
                    }

                    try
                    {
                        // 继续检查服务
                        resumeTasks.Add(_inspectionService.ResumeInspectionAsync());
                        AddLogMessage("信息", "已发送恢复检查命令");
                    }
                    catch (Exception ex)
                    {
                        AddLogMessage("警告", $"恢复检查服务失败: {ex.Message}");
                    }

                    // 等待所有恢复任务完成
                    await Task.WhenAll(resumeTasks);

                    // 记录日志
                    AddLogMessage("信息", "检查已从暂停点继续");
                    UpdateCommandsAvailability();
                    return;
                }

                // 以下为正常启动流程
                if (SelectedModel == null)
                {
                    MessageBox.Show(
                        "请先选择机种",
                        "提示",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                    return;
                }

                // 清空之前的检查结果
                InspectionResults.Clear();
                _ngCount = 0;
                _currentInspectionIndex = 0;
                FinalJudgment = "OK";

                // 获取当前机种的检查点
                var points = await _databaseService.GetInspectionPointsAsync(SelectedModel.ModelId);
                if (!points.Any())
                {
                    MessageBox.Show(
                        "当前机种没有配置检查点",
                        "提示",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                    return;
                }

                _inspectionService.LoadPointsToolBlock((List<InspectionPoint>)points);

                // 启动轨道
                await _trackService.StartAsync();

                // 记录日志
                AddLogMessage("信息", "开始检查");

                // 启动检查服务
                bool started = await _inspectionService.StartInspectionAsync(SelectedModel, points);
                if (!started)
                {
                    AddLogMessage("错误", "启动检查失败");
                    return;
                }

                // 订阅产品就位事件
                _trackService.BoardArrived += OnBoardArrived;

                // 更新命令可用状态
                UpdateCommandsAvailability();
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"启动检查失败: {ex.Message}");
                MessageBox.Show(
                    $"启动检查失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async void ExecutePause()
        {
            try
            {
                AddLogMessage("信息", "开始暂停检查...");

                // 使用Task.WhenAll同时暂停轨道和检查服务，即使其中一个失败也不会影响另一个
                var pauseTasks = new List<Task>();

                try
                {
                    // 暂停轨道
                    pauseTasks.Add(_trackService.PauseAsync());
                    AddLogMessage("信息", "已发送暂停轨道命令");
                }
                catch (Exception ex)
                {
                    AddLogMessage("警告", $"暂停轨道失败: {ex.Message}");
                }

                try
                {
                    // 暂停检查服务
                    pauseTasks.Add(_inspectionService.PauseInspectionAsync());
                    AddLogMessage("信息", "已发送暂停检查命令");
                }
                catch (Exception ex)
                {
                    AddLogMessage("警告", $"暂停检查服务失败: {ex.Message}");
                }

                // 等待所有暂停任务完成
                await Task.WhenAll(pauseTasks);

                // 记录日志
                AddLogMessage("信息", "检查已暂停");

                // 更新命令可用状态
                UpdateCommandsAvailability();
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"暂停检查失败: {ex.Message}");
                MessageBox.Show(
                    $"暂停检查失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async void ExecuteStop()
        {
            try
            {
                AddLogMessage("信息", "开始停止检查...");

                // 使用Task.WhenAll同时停止轨道和检查服务，即使其中一个失败也不会影响另一个
                var stopTasks = new List<Task>();

                try
                {
                    // 停止轨道
                    stopTasks.Add(_trackService.StopAsync());
                    AddLogMessage("信息", "已发送停止轨道命令");
                }
                catch (Exception ex)
                {
                    AddLogMessage("警告", $"停止轨道失败: {ex.Message}");
                }

                try
                {
                    // 停止检查服务
                    stopTasks.Add(_inspectionService.StopInspectionAsync());
                    AddLogMessage("信息", "已发送停止检查命令");
                }
                catch (Exception ex)
                {
                    AddLogMessage("警告", $"停止检查服务失败: {ex.Message}");
                }

                // 等待所有停止任务完成
                await Task.WhenAll(stopTasks);

                // 取消订阅产品就位事件
                _trackService.BoardArrived -= OnBoardArrived;

                // 记录日志
                AddLogMessage("信息", "检查已停止");

                // 停止后系统需要重新复位
                _isSystemReset = false;

                // 更新命令可用状态
                UpdateCommandsAvailability();
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"停止检查失败: {ex.Message}");
                MessageBox.Show(
                    $"停止检查失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async void ExecuteReset()
        {
            try
            {
                // 记录日志
                AddLogMessage("信息", "开始复位XYZ轴轨道...");

                // 复位X轴
                await Task.Run(() => _bkMottion.HomeAxis(0));
                AddLogMessage("信息", "X轴复位完成");

                // 复位Y轴
                await Task.Run(() => _bkMottion.HomeAxis(1));
                AddLogMessage("信息", "Y轴复位完成");

                // 复位Z轴
                await Task.Run(() => _bkMottion.HomeAxis(2));
                AddLogMessage("信息", "Z轴复位完成");

                //复位轨道服务
                await _trackService.ResetAsync();
                AddLogMessage("信息", "轨道复位完成");

                // 记录复位完成日志
                AddLogMessage("信息", "XYZ轴轨道复位完成");

                // 标记系统已复位，不再需要复位
                _isSystemReset = true;
                _needsReset = false;

                // 显示复位成功消息
                AddLogMessage("信息", "系统已成功复位，可以开始操作", "系统");

                //try {
                //    MessageBox.Show(
                //        "系统已完成复位，现在可以开始操作。",
                //        "复位完成",
                //        MessageBoxButton.OK,
                //        MessageBoxImage.Information
                //    );
                //} catch (Exception ex) {
                //    AddLogMessage("错误", $"显示复位完成对话框失败: {ex.Message}", "系统");
                //}

                // 更新命令可用状态
                UpdateCommandsAvailability();
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"XYZ轴轨道复位失败: {ex.Message}");
                MessageBox.Show(
                    $"XYZ轴轨道复位失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        // 处理产品就位事件
        private void OnBoardArrived(object sender, BoardArrivedEventArgs e)
        {
            AddLogMessage("信息", $"产品就位，ID: {e.BoardId}, 类型: {e.BoardType}");

            // 触发检查服务开始实际检查
            System.Windows.Application.Current.Dispatcher.Invoke(async () =>
            {
                try
                {
                    // 清空之前的检查结果列表，为新产品做准备
                    InspectionResults.Clear();

                    AddLogMessage("信息", "开始执行检查...");

                    // 通知检查服务开始检查点位检查
                    bool started = await _inspectionService.StartInspectPointsAsync();
                    if (!started)
                    {
                        AddLogMessage("错误", "开始点位检查失败");
                    }
                }
                catch (Exception ex)
                {
                    AddLogMessage("错误", $"检查过程出错: {ex.Message}");
                }
            });
        }

        // 处理检查服务事件
        private void OnInspectionCompleted(object sender, InspectionCompletedEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                FinalJudgment = e.IsPass ? "OK" : "NG";
                _ngCount = e.NgCount;
                RaisePropertyChanged(nameof(NgCountDisplay));

                AddLogMessage("信息", $"检查完成，结果: {FinalJudgment}，NG数量: {e.NgCount}");

                // 根据检查结果更新设备状态
                Task.Run(async () =>
                {
                    try
                    {
                        if (!e.IsPass)
                        {
                            // NG结果 - 警告状态（黄灯闪烁+短促蜂鸣）
                            await _statusManager.SetWarningState();
                        }
                        else
                        {
                            // OK结果 - 正常状态（绿灯）
                            await _statusManager.SetNormalState();
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLogMessage("错误", $"更新设备状态失败: {ex.Message}");
                    }
                });

                // 通知轨道服务检查完成
                _trackService.NotifyInspectionCompletedAsync(e.IsPass).ConfigureAwait(false);

                // 更新命令可用状态
                UpdateCommandsAvailability();
            });
        }

        private void OnInspectionPointCompleted(object sender, InspectionPointCompletedEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 更新检查进度
                _currentInspectionIndex = e.CurrentIndex;
                _totalInspectionPoints = e.TotalCount;
                RaisePropertyChanged(nameof(InspectionProgress));

                // 创建新的检查结果对象
                var result = new InspectionResult
                {
                    PointName = e.Point.PointName,
                    Result = e.IsPass ? "OK" : "NG",
                    ImagePath = e.Result.ImagePath,
                };

                // 确保图像已经加载
                if (string.IsNullOrEmpty(result.ImagePath) || !File.Exists(result.ImagePath))
                {
                    // 如果图像路径无效，尝试使用默认图像
                    AddLogMessage(
                        "警告",
                        $"检查点 {e.Point.PointName} 的图像路径无效: {result.ImagePath}"
                    );
                    string defaultPath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        "Images",
                        "default.bmp"
                    );
                    if (File.Exists(defaultPath))
                    {
                        result.ImagePath = defaultPath;
                    }
                }

                // 将结果添加到列表的最前面
                InspectionResults.Insert(0, result);

                // 如果结果是NG，更新NG计数
                if (!e.IsPass)
                {
                    _ngCount++;
                    RaisePropertyChanged(nameof(NgCountDisplay));
                }

                // 设置为当前选中的结果，这将触发加载图像
                SelectedResult = result;

                // 在日志中记录检查点结果
                AddLogMessage("信息", $"检查点 {e.Point.PointName} 完成，结果: {result.Result}");
            });
        }

        private void OnInspectionError(object sender, InspectionErrorEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                AddLogMessage("错误", $"检查错误: {e.ErrorMessage}");

                // 设置设备状态为错误状态
                Task.Run(async () => await _statusManager.SetErrorState(true));

                MessageBox.Show(
                    e.ErrorMessage,
                    "检查错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );

                // 检查出错后系统需要重新复位
                _isSystemReset = false;

                // 更新按钮状态
                UpdateCommandsAvailability();
            });
        }

        // 处理检查服务状态变化
        private void OnInspectionStatusChanged(object sender, InspectionStatus status)
        {
            // 在UI线程中更新按钮状态
            System.Windows.Application.Current.Dispatcher.Invoke(async () =>
            {
                AddLogMessage("信息", $"检查服务状态变更为：{status}");

                // 根据检查状态更新设备状态
                try
                {
                    switch (status)
                    {
                        case InspectionStatus.Inspecting:
                            // 检查中 - 正常运行状态（绿灯）
                            await _statusManager.SetNormalState();

                            // 显示状态通知
                            ShowStatusNotification("检查状态", "系统运行中", "信息");
                            break;

                        case InspectionStatus.Paused:
                            // 暂停状态 - 待机（黄灯）
                            await _statusManager.SetPausedState();

                            // 显示状态通知
                            ShowStatusNotification("检查状态", "系统已暂停", "警告");
                            break;

                        case InspectionStatus.Error:
                            // 错误状态 - 故障（红灯+蜂鸣器）
                            await _statusManager.SetErrorState(true);

                            // 显示状态通知
                            ShowStatusNotification("检查状态", "系统出错，请检查", "错误");
                            break;

                        case InspectionStatus.Idle:
                            // 空闲状态 - 待机（黄灯）
                            await _statusManager.SetStandbyState();

                            // 显示状态通知
                            ShowStatusNotification("检查状态", "系统空闲", "信息");
                            break;

                        case InspectionStatus.Completed:
                            // 修改：完成状态应保持绿灯（正常状态），而不是待机状态
                            await _statusManager.SetNormalState();

                            // 显示状态通知
                            ShowStatusNotification("检查状态", "检查已完成", "信息");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    AddLogMessage("错误", $"更新设备状态失败: {ex.Message}");
                }

                UpdateCommandsAvailability();
            });
        }

        // 显示状态通知的辅助方法
        private void ShowStatusNotification(string title, string message, string type)
        {
            try
            {
                // 使用UI线程调用显示通知
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    // 获取当前视图并显示通知
                    if (
                        System.Windows.Application.Current.MainWindow
                        is System.Windows.Window window
                    )
                    {
                        var mainWindow = window.Content as Views.MainWindow;
                        var inspectionView =
                            mainWindow?.FindName("InspectionViewControl") as Views.InspectionView;

                        inspectionView?.ShowStatusNotification(title, message, type);
                    }
                });
            }
            catch (Exception ex)
            {
                // 仅记录错误，不影响主业务逻辑
                System.Diagnostics.Debug.WriteLine($"显示状态通知失败: {ex.Message}");
            }
        }

        // 添加日志记录方法
        public void AddLogMessage(string type, string message, string source = "机台")
        {
            // 记录到日志服务
            switch (type.ToLower())
            {
                case "错误":
                    _logService.LogError(message, source);
                    break;
                case "警告":
                    _logService.LogWarning(message, source);
                    break;
                case "信息":
                    _logService.LogInformation(message, source);
                    break;
                case "调试":
                    _logService.LogDebug(message, source);
                    break;
            }

            // 根据过滤条件判断是否显示
            if (!ShouldDisplayLog(type, source))
                return;

            // 显示到UI
            if (Application.Current != null)
            {
                Application.Current.Dispatcher.BeginInvoke(
                    new Action(() =>
                    {
                        try
                        {
                            if (
                                Application.Current.MainWindow
                                is HandyControl.Controls.Window window
                            )
                            {
                                // 直接通过Content查找MainWindow
                                var mainWindow = window.Content as Views.MainWindow;
                                var inspectionView =
                                    mainWindow?.FindName("InspectionViewControl")
                                    as Views.InspectionView;

                                if (inspectionView == null)
                                {
                                    var inspectionViews = FindVisualChildren<Views.InspectionView>(
                                        window
                                    );
                                    inspectionView = inspectionViews.FirstOrDefault();
                                }

                                // 添加日志到RichTextBox，包含来源信息
                                if (inspectionView != null)
                                {
                                    // 使用高精度时间戳
                                    inspectionView.AppendLogMessage(
                                        DateTime.Now,
                                        type,
                                        $"[{source}] {message}"
                                    );
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // 记录错误，但不抛出异常
                            System.Diagnostics.Debug.WriteLine($"添加UI日志时出错: {ex.Message}");
                            _logService.LogError(ex, "添加UI日志时出错", "检查服务");
                        }
                    })
                );
            }
        }

        // 判断是否应该显示日志
        private bool ShouldDisplayLog(string type, string source)
        {
            // 根据日志类型过滤
            if (type.ToLower() == "错误" && !ShowErrorLogs)
                return false;

            // 根据日志来源过滤
            switch (source)
            {
                case "检查服务":
                    return ShowInspectionLogs;
                case "运动控制卡":
                    return ShowMotionCardLogs;
                case "轨道控制":
                    return ShowTrackLogs;
                case "机台":
                    return ShowMachineLogs;
                case "指示灯服务":
                    return ShowIndicatorLogs;
                case "报警系统":
                    return ShowAlarmLogs;
                default:
                    // 对于未知来源，默认显示
                    return true;
            }
        }

        // 辅助方法：查找指定类型的子控件
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj)
            where T : DependencyObject
        {
            if (depObj == null)
                yield break;

            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(depObj, i);

                if (child is T t)
                    yield return t;

                foreach (var childOfChild in FindVisualChildren<T>(child))
                    yield return childOfChild;
            }
        }

        // 轨道服务事件处理方法
        private void OnTrackError(object sender, string message)
        {
            AddLogMessage("错误", message, "轨道控制");
        }

        private void OnTrackWarning(object sender, string message)
        {
            AddLogMessage("警告", message, "轨道控制");
        }

        private void OnTrackMessage(object sender, string message)
        {
            AddLogMessage("信息", message, "轨道控制");
        }

        // 从TrackControlViewModel获取日志
        public void ImportLogsFromTrackViewModel(TrackControlViewModel trackViewModel)
        {
            if (trackViewModel?.LogMessages == null || !ShowTrackLogs)
                return;

            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 获取当前视图
                if (System.Windows.Application.Current.MainWindow is System.Windows.Window window)
                {
                    var mainWindow = window.Content as Views.MainWindow;
                    var inspectionView =
                        mainWindow?.FindName("InspectionViewControl") as Views.InspectionView;

                    if (inspectionView != null)
                    {
                        // 添加轨道控制日志
                        foreach (var log in trackViewModel.LogMessages)
                        {
                            inspectionView.AppendLogMessage(log.Timestamp, log.Type, log.Message);
                        }

                        // 添加一条分隔日志
                        inspectionView.AppendLogMessage(
                            DateTime.Now,
                            "信息",
                            "--- 轨道控制日志导入完成 ---"
                        );
                    }
                }
            });
        }

        /// <summary>
        /// 处理检查服务的日志消息
        /// </summary>
        private void OnInspectionServiceLogReceived(object sender, ServicesLogMessageEventArgs e)
        {
            // InspectionService的LogMessageEventArgs有Timestamp, Type, Message属性
            AddLogMessage(e.Type, e.Message, "检查服务");
        }

        // 修改运动控制卡日志事件处理方法
        private void OnMotionCardLogMessage(object sender, LogEventArgs e)
        {
            AddLogMessage("信息", e.Message, "运动控制卡");
        }

        private void OnMotionCardLogError(object sender, LogEventArgs e)
        {
            AddLogMessage("错误", e.Message, "运动控制卡");
        }

        private void OnMotionCardLogWarning(object sender, LogEventArgs e)
        {
            AddLogMessage("警告", e.Message, "运动控制卡");
        }

        /// <summary>
        /// 设置机器配置助手实例，由MainWindowViewModel调用
        /// </summary>
        public void SetMachineConfigurationHelper(MachineConfigurationHelper configHelper)
        {
            _machineConfigurationHelper = configHelper;
            var isOfflineEnabled = _machineConfigurationHelper.IsOfflineSimulationEnabled();
            if (isOfflineEnabled != IsOfflineSimulation)
            {
                IsOfflineSimulation = isOfflineEnabled;
            }

            // 初始化物理按钮状态监控
            InitializeButtonStateMonitoring();
        }

        /// <summary>
        /// 初始化物理按钮状态监控
        /// </summary>
        private void InitializeButtonStateMonitoring()
        {
            if (IsOfflineSimulation)
            {
                AddLogMessage("信息", "离线模拟模式下，物理按钮监控已禁用", "机台");
                return;
            }

            try
            {
                // 创建定时器，将轮询间隔增加到300毫秒，减少对系统的负担
                _buttonStateTimer = new System.Threading.Timer(CheckButtonStates, null, 0, 300);

                AddLogMessage("信息", "物理按钮状态监控已初始化", "机台");
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"初始化物理按钮状态监控失败: {ex.Message}", "机台");
            }
        }

        /// <summary>
        /// 检查按钮状态并处理按钮事件
        /// </summary>
        private void CheckButtonStates(object state)
        {
            if (IsOfflineSimulation || _bkMottion == null)
                return;

            try
            {
                bool needUpdateUI = false;
                bool startButtonPressed = false;
                bool resetButtonPressed = false;

                // 获取开始按钮状态
                var startButtonConfig = _machineConfigurationHelper.GetControlButton("StartButton");
                if (startButtonConfig != null)
                {
                    bool currentStartState = _bkMottion.GetInputBitStatus(
                        1,
                        startButtonConfig.IoBitIndex
                    );
                    bool previousStartState = StartButtonState;

                    // 只有状态发生变化时才更新UI
                    if (currentStartState != previousStartState)
                    {
                        StartButtonState = currentStartState;
                        needUpdateUI = true;
                    }

                    // 检测上升沿（按钮按下时）
                    if (currentStartState && !previousStartState)
                    {
                        startButtonPressed = true;
                    }
                }

                // 获取复位按钮状态
                var resetButtonConfig = _machineConfigurationHelper.GetControlButton("ResetButton");
                if (resetButtonConfig != null)
                {
                    bool currentResetState = _bkMottion.GetInputBitStatus(
                        1,
                        resetButtonConfig.IoBitIndex
                    );
                    bool previousResetState = ResetButtonState;

                    // 只有状态发生变化时才更新UI
                    if (currentResetState != previousResetState)
                    {
                        ResetButtonState = currentResetState;
                        needUpdateUI = true;
                    }

                    // 检测上升沿（按钮按下时）
                    if (currentResetState && !previousResetState)
                    {
                        resetButtonPressed = true;
                    }
                }

                // 如果状态发生变化或按钮被按下，才进行UI更新和命令执行
                if (needUpdateUI || startButtonPressed || resetButtonPressed)
                {
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        // 如果状态发生变化，通知UI刷新
                        if (needUpdateUI)
                        {
                            RaisePropertyChanged(nameof(StartButtonState));
                            RaisePropertyChanged(nameof(ResetButtonState));
                        }

                        // 执行按钮命令
                        if (startButtonPressed && StartCommand.CanExecute())
                        {
                            AddLogMessage("信息", "物理开始按钮被按下", "机台");
                            StartCommand.Execute();
                        }
                        else if (resetButtonPressed && ResetCommand.CanExecute())
                        {
                            AddLogMessage("信息", "物理复位按钮被按下", "机台");
                            ResetCommand.Execute();
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                // 日志记录异常，但不显示在UI上，避免频繁弹出
                Console.WriteLine($"按钮状态检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理定时器资源
        /// </summary>
        private void CleanupButtonStateTimer()
        {
            if (_buttonStateTimer != null)
            {
                _buttonStateTimer.Dispose();
                _buttonStateTimer = null;
            }
        }

        /// <summary>
        /// 切换离线模拟模式
        /// </summary>
        private void ExecuteToggleOfflineSimulation()
        {
            IsOfflineSimulation = !IsOfflineSimulation;
            if (_machineConfigurationHelper != null)
            {
                _machineConfigurationHelper.SetOfflineSimulation(IsOfflineSimulation);
            }

            // 根据离线模拟模式状态处理按钮监控
            if (IsOfflineSimulation)
            {
                // 关闭按钮状态监控
                CleanupButtonStateTimer();
                AddLogMessage("信息", "离线模拟模式已启用，物理按钮监控已禁用", "机台");
            }
            else
            {
                // 重新初始化按钮状态监控
                InitializeButtonStateMonitoring();
                AddLogMessage("信息", "离线模拟模式已禁用，物理按钮监控已启用", "机台");
            }
        }

        private void ExecuteResetAlarms()
        {
            // 尝试重置报警
            try
            {
                AddLogMessage("信息", "尝试重置报警...", "报警系统");

                // 先重置伺服报警或运动报警
                if (HasServoAlarms || HasMotionAlarms)
                {
                    // 通过运动控制卡清除总报警
                    _bkMottion.ClearTotalException();
                    AddLogMessage("信息", "已清除运动卡总报警寄存器", "报警系统");
                }

                // 异步尝试重置状态管理器中的所有报警
                Task.Run(async () =>
                {
                    bool resetSuccessful = await ResetAlarmsAsync();

                    // 更新UI状态
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        if (resetSuccessful)
                        {
                            AddLogMessage("信息", "报警重置操作已成功执行", "报警系统");
                        }
                        else
                        {
                            AddLogMessage("警告", "报警重置操作未完全成功，请再次尝试", "报警系统");
                        }

                        // 确保命令状态立即更新
                        UpdateCommandsAvailability();
                    });
                });
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"重置报警失败: {ex.Message}", "报警系统");
                MessageBox.Show(
                    $"重置报警失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        // 添加一个包装方法用于重置报警，方便获取结果
        private async Task<bool> ResetAlarmsAsync()
        {
            if (_statusManager == null)
            {
                AddLogMessage("错误", "状态管理器未初始化，无法重置报警", "报警系统");
                return false;
            }

            try
            {
                await _statusManager.ResetAlarmsAsync();

                // 检查报警是否确实被清除
                return !HasActiveAlarms;
            }
            catch (Exception ex)
            {
                AddLogMessage("错误", $"重置报警过程中发生异常: {ex.Message}", "报警系统");
                return false;
            }
        }

        // 在这里添加OnAlarmsChanged方法
        private void OnAlarmsChanged(object sender, List<AlarmInfo> alarms)
        {
            // 在UI线程中更新报警状态
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    bool previousAlarmState = HasActiveAlarms;

                    // 更新报警列表
                    ActiveAlarms.Clear();
                    if (alarms != null && alarms.Count > 0)
                    {
                        foreach (var alarm in alarms)
                        {
                            ActiveAlarms.Add(alarm);
                        }
                    }

                    // 更新报警状态
                    HasActiveAlarms = ActiveAlarms.Count > 0;
                    AlarmCount = ActiveAlarms.Count;

                    // 检查是否有伺服报警或运动报警
                    HasServoAlarms = ActiveAlarms.Any(a => a.AlarmType == AlarmType.ServoAlarm);
                    HasMotionAlarms = ActiveAlarms.Any(a => a.AlarmType == AlarmType.MotionAlarm);

                    // 触发属性变更通知，更新状态颜色和文本
                    RaisePropertyChanged(nameof(AlarmStatusColor));
                    RaisePropertyChanged(nameof(AlarmStatusText));

                    // 报警状态变更日志
                    if (HasActiveAlarms)
                    {
                        // 如果出现了报警，标记系统需要复位
                        _needsReset = true;

                        string alarmText =
                            HasServoAlarms ? "伺服报警"
                            : HasMotionAlarms ? "运动报警"
                            : "一般报警";
                        AddLogMessage(
                            "警告",
                            $"设备报警状态：{alarmText}，共{AlarmCount}个报警",
                            "报警系统"
                        );

                        // 记录每个报警的详细信息
                        foreach (var alarm in ActiveAlarms)
                        {
                            AddLogMessage(
                                "警告",
                                $"报警详情：{alarm.Name} - {alarm.Description}",
                                "报警系统"
                            );
                        }

                        // 如果报警状态从无报警变为有报警，显示明确的提示
                        if (!previousAlarmState && HasActiveAlarms)
                        {
                            AddLogMessage(
                                "警告",
                                "系统进入报警状态，所有操作按钮已禁用，仅允许清除异常操作",
                                "报警系统"
                            );

                            //// 可以考虑显示一个对话框提示用户
                            //try {
                            //    MessageBox.Show(
                            //        "系统检测到报警信号，已急停所有轴运动！\n请先清除异常，然后执行复位操作后才能继续。",
                            //        "设备报警",
                            //        MessageBoxButton.OK,
                            //        MessageBoxImage.Warning
                            //    );
                            //} catch (Exception ex) {
                            //    AddLogMessage("错误", $"显示报警对话框失败: {ex.Message}", "报警系统");
                            //}
                        }
                    }
                    else
                    {
                        //// 报警已清除但仍需要复位
                        //if (_needsReset)
                        //{
                        //    AddLogMessage("信息", "设备报警已全部清除，请执行系统复位后再继续操作", "报警系统");

                        //    try {
                        //        MessageBox.Show(
                        //            "报警已清除，请执行系统复位操作后才能继续。",
                        //            "需要复位",
                        //            MessageBoxButton.OK,
                        //            MessageBoxImage.Information
                        //        );
                        //    } catch (Exception ex) {
                        //        AddLogMessage("错误", $"显示复位提示对话框失败: {ex.Message}", "报警系统");
                        //    }
                        //}
                        //else
                        //{
                        //    AddLogMessage("信息", "设备报警已全部清除，操作按钮已恢复", "报警系统");
                        //}
                    }

                    // 更新所有命令状态
                    UpdateCommandsAvailability();
                }
                catch (Exception ex)
                {
                    AddLogMessage("错误", $"更新报警状态时发生错误: {ex.Message}", "报警系统");
                }
            });
        }

        private bool CanExecuteResetAlarms()
        {
            // 清除异常按钮始终可用，无论是否有报警
            // 这样用户可以随时尝试清除系统中可能存在的异常状态
            return true;
        }

        public void Dispose()
        {
            try
            {
                // 清理定时器资源
                CleanupButtonStateTimer();

                // 取消订阅报警事件
                if (_statusManager != null)
                {
                    _statusManager.AlarmsChanged -= OnAlarmsChanged;
                    AddLogMessage("信息", "已取消订阅报警事件", "系统");
                }

                // 取消订阅运动控制卡的日志事件
                if (_bkMottion != null)
                {
                    _bkMottion.LogMessageReceived -= OnMotionCardLogMessage;
                    _bkMottion.LogErrorReceived -= OnMotionCardLogError;
                    _bkMottion.LogWarningReceived -= OnMotionCardLogWarning;
                }

                // 取消订阅其他事件
                if (_inspectionService != null)
                {
                    _inspectionService.InspectionCompleted -= OnInspectionCompleted;
                    _inspectionService.InspectionPointCompleted -= OnInspectionPointCompleted;
                    _inspectionService.InspectionError -= OnInspectionError;
                    _inspectionService.StatusChanged -= OnInspectionStatusChanged;

                    if (_inspectionService is InspectionService service)
                    {
                        service.LogMessageReceived -= OnInspectionServiceLogReceived;
                    }
                }

                if (_trackService != null)
                {
                    _trackService.ErrorOccurred -= OnTrackError;
                    _trackService.WarningOccurred -= OnTrackWarning;
                    _trackService.MessageLogged -= OnTrackMessage;
                    _trackService.BoardArrived -= OnBoardArrived;
                }

                AddLogMessage("信息", "检查视图模型已释放", "系统");
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                AddLogMessage("错误", $"释放资源时发生错误: {ex.Message}", "系统");
            }
        }
    }

    // 检查结果类
    public class InspectionResult : BindableBase
    {
        private string _pointName;
        private string _result;
        private string _imagePath;
        private BitmapImage _image;

        public string PointName
        {
            get => _pointName;
            set => SetProperty(ref _pointName, value);
        }

        public string Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public string ImagePath
        {
            get => _imagePath;
            set
            {
                if (SetProperty(ref _imagePath, value))
                {
                    // 当图像路径改变时，立即加载图像
                    LoadImage();
                }
            }
        }

        public BitmapImage Image
        {
            get => _image;
            set => SetProperty(ref _image, value);
        }

        private void LoadImage()
        {
            try
            {
                if (string.IsNullOrEmpty(_imagePath))
                    return;

                if (File.Exists(_imagePath))
                {
                    var image = new BitmapImage();
                    image.BeginInit();
                    image.CacheOption = BitmapCacheOption.OnLoad;
                    image.UriSource = new Uri(_imagePath, UriKind.RelativeOrAbsolute);
                    image.EndInit();
                    image.Freeze(); // 提高性能
                    Image = image;
                }
            }
            catch (Exception)
            {
                // 忽略加载错误
            }
        }
    }

    // 使用TrackControlViewModel中已定义的LogMessage类
}
