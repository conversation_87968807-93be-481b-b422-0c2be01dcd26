<?xml version="1.0" encoding="utf-8"?>
<Obfuscator>
  <!-- 指定主程序集路径 -->
  <Var name="InPath" value="bin\x64\Release" />
  <Var name="OutPath" value="$(InPath)\Obfuscated" />
  <Var name="KeepPublicApi" value="false" />
  <Var name="HidePrivateApi" value="true" />
  <Var name="RenameProperties" value="true" />
  <Var name="RenameEvents" value="true" />
  <Var name="RenameFields" value="true" />
  <Var name="RegenerateDebugInfo" value="true" />
  <Var name="MarkedOnly" value="false" />

  <!-- 主程序集配置 -->
  <Module file="$(InPath)\Nickel_Inspect.exe" />

  <!-- 排除不应该混淆的类型和命名空间 -->
  <Skip match="Nickel_Inspect.Models.DeviceConfiguration" />
  <Skip match="Nickel_Inspect.App" />
  <Skip match="Nickel_Inspect.Views.*" />
  
  <!-- 排除UI相关类 -->
  <Skip match=".*Window$" />
  <Skip match=".*Dialog$" />
  <Skip match=".*View$" />
  <Skip match=".*ViewModel$" />
  
  <!-- 排除序列化相关类 -->
  <Skip match=".*Config$" />
  <Skip match=".*Configuration$" />
  
  <!-- 排除数据模型 -->
  <Skip match="Nickel_Inspect.Models.*" />
  
  <!-- 排除第三方依赖项 -->
  <Skip match="Cognex.*" />
  <Skip match="HandyControl.*" />
  <Skip match="Prism.*" />
  <Skip match="DryIoc.*" />
  <Skip match="Serilog.*" />
  <Skip match="System.*" />
  <Skip match="Microsoft.*" />

  <!-- 排除引用的程序集 -->
  <SkipAssembly name="Cognex.Vision.Core" />
  <SkipAssembly name="Cognex.VisionPro" />
  <SkipAssembly name="Cognex.VisionPro.Core" />
  <SkipAssembly name="Cognex.VisionPro.ImageFile" />
  <SkipAssembly name="Cognex.VisionPro.ToolGroup" />
  <SkipAssembly name="Dapper" />
  <SkipAssembly name="DryIoc" />
  <SkipAssembly name="EntityFramework" />
  <SkipAssembly name="FluentModbus" />
  <SkipAssembly name="HandyControl" />
  <SkipAssembly name="Microsoft.*" />
  <SkipAssembly name="Newtonsoft.Json" />
  <SkipAssembly name="Prism" />
  <SkipAssembly name="Serilog" />
  <SkipAssembly name="System" />
  <SkipAssembly name="WindowsBase" />
  <SkipAssembly name="PresentationCore" />
  <SkipAssembly name="PresentationFramework" />
</Obfuscator>