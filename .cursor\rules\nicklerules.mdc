---
description: 我这个项目是一个类似于AOI个一个监测设备：XYZ带着相机跑， 皮带+调宽用于上下载具
globs: 
alwaysApply: false
---
# 景旺镍片检查系统开发文档

## 项目概述

- 这是一个基于工业视觉的镍片缺陷检测系统，主要用于PCB板的传输、定位和缺陷检测
- 系统架构：XYZ运动平台+相机，皮带+调宽装置用于PCB的传送和定位
- 支持SMEMA标准通信，可与上下游设备无缝集成，实现自动化生产线

## 工作流程

当用户点击开始后，系统会：
1. 开始进载具
2. 等载具到位后，根据用户选择机种，查询该机种下被激活的点位
3. 逐个指挥相机运动至相应点位后发送相机触发信号
4. VisionPro脚本通过回调函数获取图像并返回结果
5. 所有检查点走完后输出一个综合判定结果
6. 无论综合判定是否OK，都会给轨道服务发送检查完成信号让轨道进入下板状态

## 技术栈

- 开发语言：C# (.NET Framework 4.8)
- UI框架：WPF (Windows Presentation Foundation)
- MVVM框架：Prism + DryIoc (依赖注入容器)
- 数据存储：本地SQLite数据库
- 视觉处理：集成康耐视VisionPro SDK
- 硬件通信：自定义运动控制卡SDK (BkMotionCard)
- 附加组件：HandyControl (UI组件)、Newtonsoft.Json (JSON处理)

## 项目结构

项目采用MVVM架构：
- [ViewModels/InspectionViewModel.cs](mdc:ViewModels/InspectionViewModel.cs) - 负责检测流程的控制、数据显示和用户交互
- [ViewModels/TrackControlViewModel.cs](mdc:ViewModels/TrackControlViewModel.cs) - 负责轨道控制的视图模型
- [Services/InspectionService.cs](mdc:Services/InspectionService.cs) - 检测服务实现
- [Services/VisionProService.cs](mdc:Services/VisionProService.cs) - 视觉服务实现
- [Services/BkMotionCard.cs](mdc:Services/BkMotionCard.cs) - 运动控制卡服务
- [Services/TrackService.cs](mdc:Services/Track/TrackService.cs) - 轨道服务实现
- [App.xaml.cs](mdc:App.xaml.cs) - 应用程序入口点，包含依赖注入配置

## 注意事项

1. 生成完成后注意检查函数重复定义问题
2. 可以阅读此项目的所有源码
3. 修改完成后需要build一下程序检查是否有报错
4. 项目的开发文档是[README.md](mdc:README.md)，包含详细的项目说明
5. 配置文件：[DeviceConfiguration.json](mdc:DeviceConfiguration.json)和[ModbusConfig.json](mdc:ModbusConfig.json)