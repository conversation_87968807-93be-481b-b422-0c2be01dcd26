using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Cognex.VisionPro;
using Cognex.VisionPro.ToolBlock;
using HandyControl.Controls;
using HandyControl.Tools;
using Newtonsoft.Json;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services;
using Nickel_Inspect.Services.Track;
using Nickel_Inspect.Views;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Services.Dialogs;
using MessageBox = HandyControl.Controls.MessageBox;

namespace Nickel_Inspect.ViewModels
{
    public class ConfigPointViewModel : BindableBase
    {
        private readonly BkMotionCard _bkMottion;
        private readonly IDialogService _dialogService;
        private readonly ITrackService _trackService;
        private readonly ILogService _logService;
        private readonly DatabaseService _databaseService;
        private readonly IVisionProService _visionProService;
        private MachineConfigurationHelper _machineConfigurationHelper;
        private readonly Services.LightControl.ILightControlService _lightControlService;
        private Dictionary<int, CogToolBlock> _toolBlockList; // 用于存储加载的VPP文件
        private bool _isLoadingVpp; // 是否正在加载VPP文件
        private double _loadingProgress; // 加载进度

        private ObservableCollection<ProductModel> _productModels;
        private ProductModel _selectedModel;
        private ObservableCollection<InspectionPoint> _inspectionPoints;

        // 当前显示的图像
        private System.Windows.Media.Imaging.BitmapImage _currentImage;
        public System.Windows.Media.Imaging.BitmapImage CurrentImage
        {
            get => _currentImage;
            set => SetProperty(ref _currentImage, value);
        }

        // 加载进度
        public double LoadingProgress
        {
            get => _loadingProgress;
            set => SetProperty(ref _loadingProgress, value);
        }

        // 是否正在加载
        public bool IsLoadingVpp
        {
            get => _isLoadingVpp;
            set => SetProperty(ref _isLoadingVpp, value);
        }

        #region 属性
        public ObservableCollection<ProductModel> ProductModels
        {
            get => _productModels;
            set => SetProperty(ref _productModels, value);
        }

        public ProductModel SelectedModel
        {
            get => _selectedModel;
            set
            {
                if (SetProperty(ref _selectedModel, value) && value != null)
                {
                    // 只有当不是首次加载时才执行加载操作
                    if (_isInitialLoad)
                    {
                        _isInitialLoad = false;
                        return;
                    }

                    // 清空之前加载的VPP文件
                    ClearToolBlockList();
                    // 加载当前机种的检查点
                    LoadInspectionPoints();
                    // 更新删除机种命令的可执行状态
                    DeleteProductModelCommand.RaiseCanExecuteChanged();
                }
            }
        }

        public ObservableCollection<InspectionPoint> InspectionPoints
        {
            get => _inspectionPoints;
            set => SetProperty(ref _inspectionPoints, value);
        }

        // 添加选中的检查点属性
        private InspectionPoint _selectedPoint;
        public InspectionPoint SelectedPoint
        {
            get => _selectedPoint;
            set
            {
                if (SetProperty(ref _selectedPoint, value))
                {
                    IsPointSelected = value != null;
                    // 如果选中了检查点，更新光源亮度控制器的值
                    if (value != null)
                    {
                        RedChannelBrightness = value.RedChannelBrightness;
                        GreenChannelBrightness = value.GreenChannelBrightness;
                        BlueChannelBrightness = value.BlueChannelBrightness;
                        WhiteChannelBrightness = value.WhiteChannelBrightness;

                        // 如果有光源控制器ID，尝试选择对应的控制器
                        if (value.LightControllerId > 0 && LightControllers != null)
                        {
                            var controller = LightControllers.FirstOrDefault(c =>
                                c.ControllerId == value.LightControllerId
                            );
                            if (controller != null)
                            {
                                SelectedLightController = controller;
                            }
                        }
                    }
                }
            }
        }

        // 是否有选中的检查点
        private bool _isPointSelected;
        public bool IsPointSelected
        {
            get => _isPointSelected;
            set => SetProperty(ref _isPointSelected, value);
        }

        // JOG速度控制相关属性
        private double _jogSpeedToggled = 200;
        public double JogSpeedToggled
        {
            get => _jogSpeedToggled;
            set
            {
                SetProperty(ref _jogSpeedToggled, value);
                JogSpeed = (int)value;
            }
        }

        private int _jogSpeed = 50;
        public int JogSpeed
        {
            get => _jogSpeed;
            set => SetProperty(ref _jogSpeed, value);
        }

        // Z轴JOG速度控制相关属性
        private double _zJogSpeedToggled = 100;
        public double ZJogSpeedToggled
        {
            get => _zJogSpeedToggled;
            set
            {
                SetProperty(ref _zJogSpeedToggled, value);
                ZJogSpeed = (int)value;
            }
        }

        private int _zJogSpeed = 30;
        public int ZJogSpeed
        {
            get => _zJogSpeed;
            set => SetProperty(ref _zJogSpeed, value);
        }

        // 光源控制器列表
        private ObservableCollection<Models.LightControl.LightController> _lightControllers;
        public ObservableCollection<Models.LightControl.LightController> LightControllers
        {
            get => _lightControllers;
            set => SetProperty(ref _lightControllers, value);
        }

        // 选中的光源控制器
        private Models.LightControl.LightController _selectedLightController;
        public Models.LightControl.LightController SelectedLightController
        {
            get => _selectedLightController;
            set
            {
                if (SetProperty(ref _selectedLightController, value) && value != null)
                {
                    // 当选择不同的光源控制器时可以在这里添加逻辑
                    // 选择后不自动应用设置
                }
            }
        }

        // 光源亮度属性
        private int _redChannelBrightness;
        public int RedChannelBrightness
        {
            get => _redChannelBrightness;
            set => SetProperty(ref _redChannelBrightness, value);
        }

        private int _greenChannelBrightness;
        public int GreenChannelBrightness
        {
            get => _greenChannelBrightness;
            set => SetProperty(ref _greenChannelBrightness, value);
        }

        private int _blueChannelBrightness;
        public int BlueChannelBrightness
        {
            get => _blueChannelBrightness;
            set => SetProperty(ref _blueChannelBrightness, value);
        }

        private int _whiteChannelBrightness;
        public int WhiteChannelBrightness
        {
            get => _whiteChannelBrightness;
            set => SetProperty(ref _whiteChannelBrightness, value);
        }
        #endregion

        #region 命令
        public DelegateCommand<InspectionPoint> CommandGo { get; }
        public DelegateCommand<InspectionPoint> ManualInspectCommand { get; }
        public DelegateCommand<string> StartJogCommand { get; }
        public DelegateCommand<string> StopJogCommand { get; }
        public DelegateCommand<string> HomeAxisCommand { get; }
        public DelegateCommand LoadProductModelsCommand { get; }
        public DelegateCommand AddProductModelCommand { get; }
        public DelegateCommand DeleteProductModelCommand { get; }
        public DelegateCommand ShowAddPointDialogCommand { get; }
        public DelegateCommand SingleAcquireCommand { get; }
        public DelegateCommand OpenTrackControlCommand { get; }
        public DelegateCommand<InspectionPoint> EditInspectionPointCommand { get; }
        public DelegateCommand<InspectionPoint> DeleteInspectionPointCommand { get; }
        public DelegateCommand ApplyLightSettingsCommand { get; private set; }
        public DelegateCommand<InspectionPoint> SelectInspectionPointCommand { get; }
        public DelegateCommand<string> UpdateChannelBrightnessCommand { get; }
        #endregion

        private bool _isInitialLoad = true;

        public ConfigPointViewModel(
            BkMotionCard bkMottion,
            IDialogService dialogService,
            ITrackService trackService,
            ILogService logService,
            DatabaseService databaseService,
            IVisionProService visionProService,
            MachineConfigurationHelper machineConfigurationHelper,
            Services.LightControl.ILightControlService lightControlService
        )
        {
            _bkMottion = bkMottion;
            _dialogService = dialogService;
            _trackService = trackService;
            _logService = logService;
            _databaseService = databaseService;
            _visionProService = visionProService;
            _machineConfigurationHelper = machineConfigurationHelper;
            _lightControlService = lightControlService;
            _toolBlockList = new Dictionary<int, CogToolBlock>();

            // 初始化命令
            LoadProductModelsCommand = new DelegateCommand(async () => await LoadProductModels());
            CommandGo = new DelegateCommand<InspectionPoint>(ExecuteCommandGo);
            ManualInspectCommand = new DelegateCommand<InspectionPoint>(
                ExecuteManualInspectCommand,
                CanExecuteManualInspect
            );
            StartJogCommand = new DelegateCommand<string>(ExecuteStartJog);
            StopJogCommand = new DelegateCommand<string>(ExecuteStopJog);
            HomeAxisCommand = new DelegateCommand<string>(ExecuteHomeAxis);
            AddProductModelCommand = new DelegateCommand(ExecuteAddProductModel);
            DeleteProductModelCommand = new DelegateCommand(
                ExecuteDeleteProductModel,
                CanExecuteDeleteProductModel
            );
            ShowAddPointDialogCommand = new DelegateCommand(
                ExecuteShowAddPointDialog,
                CanExcuteShow
            );
            SingleAcquireCommand = new DelegateCommand(ExecuteSingleAcquire);
            OpenTrackControlCommand = new DelegateCommand(ExecuteOpenTrackControl);
            EditInspectionPointCommand = new DelegateCommand<InspectionPoint>(
                ExecuteEditInspectionPoint
            );
            DeleteInspectionPointCommand = new DelegateCommand<InspectionPoint>(
                ExecuteDeleteInspectionPoint
            );
            ApplyLightSettingsCommand = new DelegateCommand(ExecuteApplyLightSettings);
            SelectInspectionPointCommand = new DelegateCommand<InspectionPoint>(
                ExecuteSelectInspectionPoint
            );
            UpdateChannelBrightnessCommand = new DelegateCommand<string>(
                ExecuteUpdateChannelBrightness
            );

            // 初始化集合
            InspectionPoints = new ObservableCollection<InspectionPoint>();
            ProductModels = new ObservableCollection<ProductModel>();
            LightControllers = new ObservableCollection<Models.LightControl.LightController>();

            // 初始化速度值
            JogSpeed = 50;
            JogSpeedToggled = 200;
            ZJogSpeed = 30;
            ZJogSpeedToggled = 50;

            // 初始化亮度值
            RedChannelBrightness = 128;
            GreenChannelBrightness = 128;
            BlueChannelBrightness = 128;
            WhiteChannelBrightness = 128;
        }

        // 添加OnNavigatedTo方法，用于在界面准备好后初始化数据
        public async void OnNavigatedTo()
        {
            try
            {
                // 加载光源控制器列表
                await LoadLightControllersAsync();

                var models = await _databaseService.GetProductModelsAsync();
                ProductModels = new ObservableCollection<ProductModel>(models);

                // 默认选中第一个机种
                if (ProductModels.Count > 0)
                {
                    _isInitialLoad = true;

                    // 直接加载第一个机种的检查点
                    var firstModel = ProductModels[0];
                    var points = await _databaseService.GetInspectionPointsAsync(
                        firstModel.ModelId
                    );
                    InspectionPoints = new ObservableCollection<InspectionPoint>(points);

                    // 加载VPP文件
                    await LoadToolBlocksAsync(points);

                    // 设置选中的机种
                    SelectedModel = firstModel;

                    // 确保更新命令可执行状态
                    ManualInspectCommand.RaiseCanExecuteChanged();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载机种数据时出错: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async Task LoadProductModels()
        {
            try
            {
                var models = await _databaseService.GetProductModelsAsync();
                ProductModels = new ObservableCollection<ProductModel>(models);

                // 默认选中第一个机种
                if (ProductModels.Count > 0)
                {
                    _isInitialLoad = true;

                    // 直接加载第一个机种的检查点
                    var firstModel = ProductModels[0];
                    var points = await _databaseService.GetInspectionPointsAsync(
                        firstModel.ModelId
                    );
                    InspectionPoints = new ObservableCollection<InspectionPoint>(points);

                    // 加载VPP文件
                    await LoadToolBlocksAsync(points);

                    // 设置选中的机种
                    SelectedModel = firstModel;

                    // 确保更新命令可执行状态
                    ManualInspectCommand.RaiseCanExecuteChanged();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载机种失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async void LoadInspectionPoints()
        {
            try
            {
                if (SelectedModel != null)
                {
                    var points = await _databaseService.GetInspectionPointsAsync(
                        SelectedModel.ModelId
                    );
                    InspectionPoints = new ObservableCollection<InspectionPoint>(points);

                    // 加载所有检查点的VPP文件
                    await LoadToolBlocksAsync(points);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载检查点失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async Task LoadToolBlocksAsync(IEnumerable<InspectionPoint> points)
        {
            try
            {
                // 清空之前的工具块列表
                ClearToolBlockList();

                // 获取有效的检查点（有检查文件路径的检查点）
                var validPoints = points
                    .Where(p =>
                        !string.IsNullOrEmpty(p.InspectionFilePath)
                        && File.Exists(p.InspectionFilePath)
                    )
                    .ToList();

                if (validPoints.Any())
                {
                    // 显示进度条
                    IsLoadingVpp = true;
                    LoadingProgress = 0;

                    // 异步加载所有VPP文件
                    await Task.Run(() =>
                    {
                        int total = validPoints.Count;
                        int current = 0;

                        foreach (var point in validPoints)
                        {
                            if (File.Exists(point.InspectionFilePath))
                            {
                                try
                                {
                                    // 加载工具块
                                    var toolBlock =
                                        CogSerializer.LoadObjectFromFile(point.InspectionFilePath)
                                        as CogToolBlock;
                                    if (toolBlock != null)
                                    {
                                        lock (_toolBlockList)
                                        {
                                            _toolBlockList[point.PointId] = toolBlock;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Application.Current.Dispatcher.Invoke(() =>
                                    {
                                        _logService.LogError(
                                            $"加载检查文件失败: {point.InspectionFilePath}, 错误: {ex.Message}",
                                            "检查点配置"
                                        );
                                    });
                                }
                            }

                            current++;
                            double progress = (double)current / total;

                            // 更新进度
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                LoadingProgress = progress * 100;
                            });
                        }
                    });

                    // 关闭进度条
                    IsLoadingVpp = false;
                    LoadingProgress = 100;

                    // 更新命令可执行状态
                    ManualInspectCommand.RaiseCanExecuteChanged();

                    _logService.LogInformation(
                        $"已成功加载 {_toolBlockList.Count} 个检查文件",
                        "检查点配置"
                    );
                }
            }
            catch (Exception ex)
            {
                IsLoadingVpp = false;
                MessageBox.Show(
                    $"加载检查文件失败：{ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                _logService.LogError($"加载检查文件失败: {ex.Message}", "检查点配置");
            }
        }

        // 清空工具块列表
        private void ClearToolBlockList()
        {
            lock (_toolBlockList)
            {
                foreach (var toolBlock in _toolBlockList.Values)
                {
                    toolBlock?.Dispose();
                }
                _toolBlockList.Clear();
            }
        }

        #region 命令执行方法
        private void ExecuteOpenTrackControl()
        {
            var parameters = new DialogParameters();
            _dialogService.ShowDialog("TrackControlView", parameters, r => { });
        }

        private void ExecuteAddProductModel()
        {
            _dialogService.ShowDialog(
                "NewProductWindow",
                null,
                r =>
                {
                    if (r.Result == ButtonResult.OK)
                    {
                        // 成功添加后刷新机种列表
                        LoadProductModelsCommand.Execute();
                    }
                }
            );
        }

        private bool CanExecuteDeleteProductModel()
        {
            return SelectedModel != null;
        }

        private async void ExecuteDeleteProductModel()
        {
            if (SelectedModel == null)
            {
                MessageBox.Show(
                    "请先选择要删除的机种",
                    "提示",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning
                );
                return;
            }

            // 弹出确认对话框
            var result = MessageBox.Show(
                $"确定要删除机种 \"{SelectedModel.ModelName}\" 吗？此操作将同时删除该机种下的所有检查点，无法恢复！",
                "确认删除",
                MessageBoxButton.OKCancel,
                MessageBoxImage.Warning
            );

            if (result == MessageBoxResult.OK)
            {
                try
                {
                    // 删除所有关联的检查点
                    var points = await _databaseService.GetInspectionPointsAsync(
                        SelectedModel.ModelId
                    );
                    foreach (var point in points)
                    {
                        await _databaseService.DeleteInspectionPointAsync(point.PointId);
                    }

                    // 删除机种
                    await _databaseService.DeleteProductModelAsync(SelectedModel.ModelId);

                    // 记录日志
                    _logService.LogInformation(
                        $"已删除机种：{SelectedModel.ModelName}",
                        "机种管理"
                    );

                    // 重新加载机种列表
                    await LoadProductModels();

                    MessageBox.Show(
                        "删除机种成功！",
                        "成功",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"删除机种失败：{ex.Message}",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                    _logService.LogError($"删除机种失败：{ex.Message}", "机种管理");
                }
            }
        }

        private void ExecuteShowAddPointDialog()
        {
            if (SelectedModel == null)
            {
                MessageBox.Show(
                    "请先选择机种",
                    "提示",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning
                );
                return;
            }

            var parameters = new DialogParameters();
            parameters.Add("ProductModel", SelectedModel);
            parameters.Add("XPosition", _bkMottion.GetCurrentPosition(0));
            parameters.Add("YPosition", _bkMottion.GetCurrentPosition(1));
            parameters.Add("ZPosition", _bkMottion.GetCurrentPosition(2));

            // 添加光源相关参数
            parameters.Add("LightController", SelectedLightController);
            parameters.Add("RedChannelBrightness", RedChannelBrightness);
            parameters.Add("GreenChannelBrightness", GreenChannelBrightness);
            parameters.Add("BlueChannelBrightness", BlueChannelBrightness);
            parameters.Add("WhiteChannelBrightness", WhiteChannelBrightness);

            _dialogService.ShowDialog(
                "AddInspectPoint",
                parameters,
                r =>
                {
                    if (r.Result == ButtonResult.OK)
                    {
                        LoadInspectionPoints();
                    }
                }
            );
        }

        private bool CanExcuteShow()
        {
            return true;
        }

        private async void ExecuteSingleAcquire()
        {
            try
            {
                _visionProService.LoadToolBlock("AcqTest.vpp");
                // 执行VisionPro工具块并获取图像
                var result = await _visionProService.AcquireAndProcessImageAsync();
                if (result.Success && result.OriginImage != null)
                {
                    // 创建保存路径
                    string directory = $"Images/liveImage";
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // 保存图像
                    string imagePath = $"{directory}/liveImage_{DateTime.Now:yyyyMMddHHmmss}.bmp";
                    if (
                        _visionProService.SaveImage(
                            new CogImage8Grey(result.OriginImage),
                            imagePath
                        )
                    )
                    {
                        _logService.LogInformation($"图像已保存至: {imagePath}", "检查点配置");
                        Thread.Sleep(200);
                        // 加载图像到UI
                        LoadImage(imagePath);
                    }
                    else
                    {
                        MessageBox.Show(
                            "保存图像失败",
                            "错误",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error
                        );
                    }
                }
                else
                {
                    MessageBox.Show(
                        "获取图像失败",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"单次采集失败",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                _logService.LogError($"单次采集失败: {ex.Message}", "检查点配置");
            }
        }

        /// <summary>
        /// 启动 jog
        /// </summary>
        /// <param name="axis">传参 X+ X- Y+ Y- Z+ Z-与xaml中对用 </param>
        private void ExecuteStartJog(string axis)
        {
            int axisNo = -1;
            int direction = 0;
            axisNo = _machineConfigurationHelper
                .GetAllAxes()
                .FirstOrDefault(s => s.Name == axis.Substring(0, 1))
                .AxisNo;
            direction = axis.Substring(1, 1) == "+" ? 1 : -1;

            if (axisNo >= 0)
            {
                var allAxes = _machineConfigurationHelper.GetAllAxes();
                var axisConfig = allAxes.FirstOrDefault(a => a.AxisNo == axisNo);
                if (axisConfig != null)
                {
                    _bkMottion.SetStartSpeed(axisNo, (int)axisConfig.StartSpeed);

                    // 使用对应轴的速度设置
                    if (axis.StartsWith("Z"))
                    {
                        // 使用Z轴专用速度
                        _bkMottion.SetMaxSpeed(axisNo, ZJogSpeed);
                        _logService.LogInformation(
                            $"Z轴JOG运动，速度: {ZJogSpeed}mm/s",
                            "运动控制"
                        );
                    }
                    else
                    {
                        // 使用XY平面速度
                        _bkMottion.SetMaxSpeed(axisNo, JogSpeed);
                    }

                    if (direction == 1)
                        _bkMottion.MoveAxisJogForward(axisNo);
                    else
                        _bkMottion.MoveAxisJogBackward(axisNo);
                }
            }
        }

        /// <summary>
        /// 停止 jog
        /// </summary>
        /// <param name="axis">传参 X Y Z， 与配置文件配套</param>
        private void ExecuteStopJog(string axis)
        {
            int axisNo = -1;
            try
            {
                axisNo = _machineConfigurationHelper
                    .GetAllAxes()
                    .FirstOrDefault(s => s.Name == axis)
                    .AxisNo;

                if (axisNo >= 0)
                {
                    // 使用同步方式直接调用，确保立即执行
                    _bkMottion.StopAxisJog(axisNo);
                    
                    // 记录停止命令的日志
                    _logService.LogInformation($"发出停止命令: 轴 {axis}", "运动控制");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"停止轴运动失败: {ex.Message}", "运动控制");
            }
        }

        /// <summary>
        /// 回零
        /// </summary>
        /// <param name="axis"> 传参 X Y Z， 与配置文件配套</param>
        private void ExecuteHomeAxis(string axis)
        {
            int axisNo = -1;
            axisNo = _machineConfigurationHelper
                .GetAllAxes()
                .FirstOrDefault(s => s.Name == axis)
                .AxisNo;

            if (axisNo >= 0)
            {
                try
                {
                    _bkMottion.HomeAxis(axisNo);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"回零失败: {ex.Message}",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
        }

        private void ExecuteCommandGo(InspectionPoint parameter)
        {
            if (parameter == null)
                return;

            try
            {
                // 获取位置
                double x = (double)parameter.XPosition;
                double y = (double)parameter.YPosition;
                double z = (double)parameter.ZPosition;

                // 移动到指定位置
                MoveToPosition(x, y, z);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"移动失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                _logService.LogError($"移动失败: {ex.Message}", "检查点配置");
            }
        }

        // 判断是否可以执行手动检查
        private bool CanExecuteManualInspect(InspectionPoint parameter)
        {
            if (parameter == null)
                return false;

            // 只要检查点有检查文件路径就允许点击按钮
            // 具体的文件存在和加载检查会在执行方法中处理
            return !string.IsNullOrEmpty(parameter.InspectionFilePath);
        }

        private async void ExecuteManualInspectCommand(InspectionPoint parameter)
        {
            if (parameter == null || string.IsNullOrEmpty(parameter.InspectionFilePath))
            {
                MessageBox.Show(
                    "检查文件路径未配置",
                    "提示",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning
                );
                return;
            }

            try
            {
                // 检查文件是否存在
                if (!File.Exists(parameter.InspectionFilePath))
                {
                    MessageBox.Show(
                        $"检查文件不存在: {parameter.InspectionFilePath}",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                    return;
                }

                // 检查VPP文件是否已加载
                if (!_toolBlockList.ContainsKey(parameter.PointId))
                {
                    MessageBox.Show(
                        "检查文件未加载，请重新加载",
                        "提示",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                    return;
                }

                // 获取对应的工具块
                var toolBlock = _toolBlockList[parameter.PointId];

                // 加载工具块到VisionProService
                _visionProService.LoadToolBlock(parameter.InspectionFilePath);

                // 执行VisionPro工具块并获取图像
                var result = await _visionProService.AcquireAndProcessImageAsync();
                if (result.Success && result.OriginImage != null)
                {
                    // 创建保存路径
                    string directory = $"Images/{parameter.PointName}";
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // 保存图像
                    string imagePath =
                        $"{directory}/{parameter.PointName}_{DateTime.Now:yyyyMMddHHmmss}.bmp";
                    if (
                        _visionProService.SaveImage(
                            new CogImage8Grey(result.OriginImage),
                            imagePath
                        )
                    )
                    {
                        _logService.LogInformation($"图像已保存至: {imagePath}", "检查点配置");
                        Thread.Sleep(200);
                        // 加载图像到UI
                        LoadImage(imagePath);

                        //MessageBox.Show($"检查已完成，图像已保存", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "保存图像失败",
                            "错误",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error
                        );
                    }
                }
                else
                {
                    MessageBox.Show(
                        "获取图像失败",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"执行检查失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                _logService.LogError($"执行检查失败: {ex.Message}", "检查点配置");
            }
        }

        // 加载图像到UI
        private void LoadImage(string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath))
                {
                    return;
                }

                // 创建BitmapImage
                var image = new System.Windows.Media.Imaging.BitmapImage();
                image.BeginInit();
                image.CacheOption = System.Windows.Media.Imaging.BitmapCacheOption.OnLoad;
                image.UriSource = new Uri(imagePath, UriKind.RelativeOrAbsolute);
                image.EndInit();
                image.Freeze(); // 提高性能

                // 更新UI
                CurrentImage = image;
            }
            catch (Exception ex)
            {
                _logService.LogError($"加载图像失败: {ex.Message}", "检查点配置");
            }
        }

        private void ExecuteEditInspectionPoint(InspectionPoint point)
        {
            if (point == null)
                return;

            var parameters = new DialogParameters
            {
                { "Point", point },
                { "Model", SelectedModel },
            };

            _dialogService.ShowDialog(
                "EditInspectPoint",
                parameters,
                r =>
                {
                    if (r.Result == ButtonResult.OK)
                    {
                        // 用新值更新InspectionPoints集合中的点位
                        LoadInspectionPoints();
                    }
                }
            );
        }

        // 移动到指定位置的辅助方法
        private void MoveToPosition(double x, double y, double z)
        {
            // 实现移动逻辑
            _logService.LogInformation($"移动到位置: X={x}, Y={y}, Z={z}", "检查点配置");
            int axisNoX = _machineConfigurationHelper
                .GetAllAxes()
                .Where(s => s.Name == "X")
                .FirstOrDefault()
                .AxisNo;
            _bkMottion.SetTargetPosition(axisNoX, x);
            int axisNoY = _machineConfigurationHelper
                .GetAllAxes()
                .Where(s => s.Name == "Y")
                .FirstOrDefault()
                .AxisNo;
            _bkMottion.SetTargetPosition(axisNoY, y);
            int axisNoZ = _machineConfigurationHelper
                .GetAllAxes()
                .Where(s => s.Name == "Z")
                .FirstOrDefault()
                .AxisNo;
            _bkMottion.SetTargetPosition(axisNoZ, z);
        }

        // 当ViewModel被销毁时清理资源
        public void OnNavigatedFrom()
        {
            // 清空工具块列表
            ClearToolBlockList();
        }

        private void ExecuteApplyLightSettings()
        {
            try
            {
                if (SelectedPoint == null)
                {
                    MessageBox.Show(
                        "请先选择一个检查点",
                        "提示",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning
                    );
                    return;
                }

                // 应用当前亮度设置到选中的检查点
                SelectedPoint.RedChannelBrightness = RedChannelBrightness;
                SelectedPoint.GreenChannelBrightness = GreenChannelBrightness;
                SelectedPoint.BlueChannelBrightness = BlueChannelBrightness;
                SelectedPoint.WhiteChannelBrightness = WhiteChannelBrightness;

                // 如果有选中的光源控制器，设置控制器ID
                if (SelectedLightController != null)
                {
                    SelectedPoint.LightControllerId = SelectedLightController.ControllerId;
                }

                // 记录日志
                _logService.LogInformation(
                    $"应用光源设置: 点位[{SelectedPoint.PointName}], RGB({RedChannelBrightness},{GreenChannelBrightness},{BlueChannelBrightness}), 白光({WhiteChannelBrightness})",
                    "检查点配置"
                );

                // 保存到数据库
                _databaseService.UpdateInspectionPointAsync(SelectedPoint);

                MessageBox.Show(
                    "光源设置已应用到选中的检查点并保存",
                    "成功",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"应用光源设置失败: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private void ExecuteSelectInspectionPoint(InspectionPoint point)
        {
            // 设置选中的检查点
            SelectedPoint = point;

            // 如果选中了检查点，可以执行一些额外的操作
            if (point != null)
            {
                _logService.LogInformation($"选中检查点: {point.PointName}", "检查点配置");

                // 可以在这里添加额外的选中后操作，例如显示对应图像等
            }
        }

        private async void ExecuteUpdateChannelBrightness(string channelType)
        {
            if (SelectedLightController == null)
                return;

            try
            {
                // 根据通道类型获取亮度值
                int brightness = 0;
                int channel = 0;

                switch (channelType)
                {
                    case "Red":
                        brightness = RedChannelBrightness;
                        channel = 1; // 假设红色通道为1
                        break;
                    case "Green":
                        brightness = GreenChannelBrightness;
                        channel = 2; // 假设绿色通道为2
                        break;
                    case "Blue":
                        brightness = BlueChannelBrightness;
                        channel = 3; // 假设蓝色通道为3
                        break;
                    case "White":
                        brightness = WhiteChannelBrightness;
                        channel = 4; // 假设白光通道为4
                        break;
                }

                // 更新光源亮度
                await _lightControlService.SetChannelBrightnessAsync(
                    SelectedLightController.ControllerId,
                    channel,
                    brightness
                );

                // 如果有选中的检查点，同时更新检查点的亮度值
                if (SelectedPoint != null)
                {
                    switch (channelType)
                    {
                        case "Red":
                            SelectedPoint.RedChannelBrightness = brightness;
                            break;
                        case "Green":
                            SelectedPoint.GreenChannelBrightness = brightness;
                            break;
                        case "Blue":
                            SelectedPoint.BlueChannelBrightness = brightness;
                            break;
                        case "White":
                            SelectedPoint.WhiteChannelBrightness = brightness;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"设置光源亮度失败: {ex.Message}", "光源控制");
            }
        }

        // 加载光源控制器列表
        private async Task LoadLightControllersAsync()
        {
            try
            {
                // 初始化光源控制服务
                await _lightControlService.InitializeAsync();

                // 创建示例光源控制器
                //LightControllers = new ObservableCollection<Models.LightControl.LightController>
                //{
                //    new Models.LightControl.LightController
                //    {
                //        ControllerId = 1,
                //        Name = "主光源控制器",
                //        Brand = Models.LightControl.LightControllerBrand.CCS,
                //        Model = "CCS-100",
                //        Port = "COM3",
                //        ChannelCount = 4,
                //        SupportsRgb = true
                //    },
                //    new Models.LightControl.LightController
                //    {
                //        ControllerId = 2,
                //        Name = "辅助光源控制器",
                //        Brand = Models.LightControl.LightControllerBrand.Yixun,
                //        Model = "YX-200",
                //        Port = "COM4",
                //        ChannelCount = 2,
                //        SupportsRgb = false
                //    }
                //};
                var availableCtrls = await _lightControlService.GetAvailableControllersAsync();
                LightControllers = new ObservableCollection<Models.LightControl.LightController>(
                    availableCtrls
                );

                // 默认选中第一个光源控制器
                if (LightControllers.Count > 0)
                {
                    SelectedLightController = LightControllers[0];
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载光源控制器数据时出错: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        private async void ExecuteDeleteInspectionPoint(InspectionPoint point)
        {
            if (point == null)
                return;

            // 弹出确认对话框
            var result = MessageBox.Show(
                $"确定要删除检查点 \"{point.PointName}\" 吗？此操作将同时删除该检查点，无法恢复！",
                "确认删除",
                MessageBoxButton.OKCancel,
                MessageBoxImage.Warning
            );

            if (result == MessageBoxResult.OK)
            {
                try
                {
                    // 删除检查点
                    bool success = await _databaseService.DeleteInspectionPointAsync(point.PointId);

                    if (success)
                    {
                        // 记录日志
                        _logService.LogInformation(
                            $"已删除检查点：{point.PointName}",
                            "检查点管理"
                        );

                        // 重新加载检查点列表
                        LoadInspectionPoints();

                        MessageBox.Show(
                            "删除检查点成功！",
                            "成功",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information
                        );
                    }
                    else
                    {
                        MessageBox.Show(
                            "删除检查点失败，可能已被删除或不存在。",
                            "错误",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error
                        );
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"删除检查点失败：{ex.Message}",
                        "错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                    _logService.LogError($"删除检查点失败：{ex.Message}", "检查点管理");
                }
            }
        }
        #endregion
    }
}
