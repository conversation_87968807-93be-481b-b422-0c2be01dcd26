using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Cognex.VisionPro;
using Cognex.VisionPro.ToolBlock;
using Nickel_Inspect.Models;
using Nickel_Inspect.Services.Alarms;
using Nickel_Inspect.Services.LightControl;

namespace Nickel_Inspect.Services
{
    /// <summary>
    /// 检查服务接口
    /// </summary>
    public class InspectionService : IInspectionService
    {
        private readonly BkMotionCard _motionCard;
        private readonly ILogService _logService;
        private readonly IVisionProService _visionProService;
        private readonly StatusManager _statusManager;
        private MachineConfigurationHelper _machineConfig;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _inspectionTask;
        private InspectionStatus _status = InspectionStatus.Idle;
        private bool _isPaused = false;
        private readonly object _lockObject = new object();
        private bool _isInitialized = false;
        private ProductModel _currentModel;
        private List<InspectionPoint> _inspectionPoints;
        private ILightControlService _lightControlService;

        private Dictionary<int, CogToolBlock> _toolBlockList = new Dictionary<int, CogToolBlock>(); // 用于存储加载的VPP文件

        /// <summary>
        /// 日志消息事件
        /// </summary>
        public event EventHandler<LogMessageEventArgs> LogMessageReceived;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="motionCard">运动控制卡</param>
        /// <param name="logService">日志服务</param>
        /// <param name="visionProService">VisionPro服务</param>
        /// <param name="statusManager">状态管理器</param>
        public InspectionService(
            BkMotionCard motionCard,
            ILogService logService,
            IVisionProService visionProService,
            StatusManager statusManager,
            ILightControlService lightControlService
        )
        {
            _motionCard = motionCard ?? throw new ArgumentNullException(nameof(motionCard));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            _visionProService =
                visionProService ?? throw new ArgumentNullException(nameof(visionProService));
            _statusManager =
                statusManager ?? throw new ArgumentNullException(nameof(statusManager));
            _lightControlService =
                lightControlService ?? throw new ArgumentNullException(nameof(lightControlService));

            // 订阅状态管理器的报警事件
            _statusManager.AlarmsChanged += OnAlarmsChanged;
            _logService.LogInformation("检查服务已创建并订阅报警事件", "检查服务");
        }

        /// <summary>
        /// 报警变更事件处理
        /// </summary>
        private void OnAlarmsChanged(object sender, List<AlarmInfo> alarms)
        {
            // 如果有活动报警且正在检查，则暂停或停止检查
            if (alarms != null && alarms.Count > 0 && _status != InspectionStatus.Idle)
            {
                // 检查是否有严重报警（如伺服报警或运动异常）
                bool hasSeriousAlarm = alarms.Any(a =>
                    a.AlarmType == AlarmType.ServoAlarm || a.AlarmType == AlarmType.MotionAlarm
                );

                if (hasSeriousAlarm)
                {
                    // 严重报警，立即停止检查
                    _logService.LogWarning("检测到严重报警，停止检查", "检查服务");
                    StopInspectionAsync().ContinueWith(_ => { });
                }
                else
                {
                    // 一般报警，暂停检查
                    _logService.LogWarning("检测到报警，暂停检查", "检查服务");
                    PauseInspectionAsync().ContinueWith(_ => { });
                }
            }
        }

        /// <summary>
        /// 检查是否有活动报警
        /// </summary>
        /// <returns>如果有活动报警则返回true</returns>
        private bool CheckForAlarms()
        {
            if (_statusManager == null)
                return false;

            if (_statusManager.HasActiveAlarms)
            {
                if (_statusManager.HasServoAlarms || _statusManager.HasMotionAlarms)
                {
                    _logService.LogWarning("存在严重报警，无法开始检查", "检查服务");
                    OnInspectionError("存在严重报警，无法开始检查", null);
                }
                else
                {
                    _logService.LogWarning("存在报警，无法开始检查", "检查服务");
                    OnInspectionError("存在报警，无法开始检查", null);
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 记录日志消息
        /// </summary>
        private void LogMessage(string message, string type, string source = "检查服务")
        {
            // 记录到文件
            switch (type.ToLower())
            {
                case "错误":
                    _logService.LogError(message, source);
                    break;
                case "警告":
                    _logService.LogWarning(message, source);
                    break;
                case "信息":
                    _logService.LogInformation(message, source);
                    break;
                default:
                    _logService.LogDebug(message, source);
                    break;
            }

            // 触发UI更新事件
            LogMessageReceived?.Invoke(
                this,
                new LogMessageEventArgs
                {
                    Timestamp = DateTime.Now,
                    Type = type,
                    Message = $"[{source}] {message}",
                }
            );
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="machineConfig">机器配置助手</param>
        public void Initialize(MachineConfigurationHelper machineConfig)
        {
            if (_isInitialized)
            {
                throw new InvalidOperationException("InspectionService已经初始化过了");
            }

            _machineConfig =
                machineConfig ?? throw new ArgumentNullException(nameof(machineConfig));
            _isInitialized = true;
            LogMessage("检查服务已初始化", "信息");
        }

        public void LoadPointsToolBlock(List<InspectionPoint> inspectionPoints)
        {
            _toolBlockList.Clear();
            int loadSucceded = 0;
            foreach (var point in inspectionPoints)
            {
                // 加载工具块
                var tool =
                    CogSerializer.LoadObjectFromFile(point.InspectionFilePath) as CogToolBlock;

                if (tool == null)
                {
                    LogMessage(
                        $"加载检查点：{point.PointName}工具块失败，无效的VPP文件:{point.InspectionFilePath}",
                        "警告",
                        "检查服务"
                    );
                    continue;
                }
                else
                {
                    loadSucceded++;
                    _toolBlockList.Add(point.PointId, tool);
                }
            }
            _logService.LogInformation(
                $"已加载完成所有检查点的toolBlock:{loadSucceded}/{inspectionPoints.Count}",
                "检查服务"
            );
        }

        private void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException(
                    "InspectionService尚未初始化，请先调用Initialize方法"
                );
            }
        }

        /// <summary>
        /// 检查完成事件
        /// </summary>
        public event EventHandler<InspectionCompletedEventArgs> InspectionCompleted;

        /// <summary>
        /// 检查点完成事件
        /// </summary>
        public event EventHandler<InspectionPointCompletedEventArgs> InspectionPointCompleted;

        /// <summary>
        /// 检查错误事件
        /// </summary>
        public event EventHandler<InspectionErrorEventArgs> InspectionError;

        /// <summary>
        /// 状态变更事件
        /// </summary>
        public event EventHandler<InspectionStatus> StatusChanged;

        /// <summary>
        /// 获取当前检查状态
        /// </summary>
        public InspectionStatus Status => _status;

        /// <summary>
        /// 是否正在检查
        /// </summary>
        public bool IsInspecting =>
            _status == InspectionStatus.Inspecting || _status == InspectionStatus.Paused;

        /// <summary>
        /// 开始检查
        /// </summary>
        /// <param name="model">产品机种</param>
        /// <param name="points">检查点列表</param>
        /// <returns>检查任务</returns>
        public async Task<bool> StartInspectionAsync(
            ProductModel model,
            IEnumerable<InspectionPoint> points
        )
        {
            EnsureInitialized();

            lock (_lockObject)
            {
                if (_status == InspectionStatus.Inspecting)
                {
                    LogMessage("已经有检查任务在进行中", "警告");
                    return false;
                }

                // 检查是否存在报警
                if (CheckForAlarms())
                {
                    return false;
                }

                _currentModel = model ?? throw new ArgumentNullException(nameof(model));
                _inspectionPoints =
                    points?.ToList() ?? throw new ArgumentNullException(nameof(points));

                if (_inspectionPoints.Count == 0)
                {
                    LogMessage("没有可检查的点位", "警告");
                    return false;
                }

                try
                {
                    // 保存当前机种和检查点信息，但不立即开始检查
                    _cancellationTokenSource = new CancellationTokenSource();
                    _isPaused = false;
                    SetStatus(InspectionStatus.Inspecting);

                    LogMessage(
                        $"准备开始检查，机种：{model.ModelName}，检查点数量：{_inspectionPoints.Count}，等待产品到位...",
                        "信息"
                    );

                    return true;
                }
                catch (Exception ex)
                {
                    OnInspectionError($"启动检查失败: {ex.Message}", ex);
                    SetStatus(InspectionStatus.Error);
                    return false;
                }
            }
        }

        /// <summary>
        /// 开始点位检查（在产品到位后调用）
        /// </summary>
        /// <returns>是否成功启动点位检查</returns>
        public async Task<bool> StartInspectPointsAsync()
        {
            EnsureInitialized();

            lock (_lockObject)
            {
                //if (_status == InspectionStatus.Inspecting)
                //{
                //    LogMessage("已经有检查任务在进行中", "警告");
                //    return false;
                //}

                // 检查是否存在报警
                if (CheckForAlarms())
                {
                    return false;
                }

                if (
                    _currentModel == null
                    || _inspectionPoints == null
                    || _inspectionPoints.Count == 0
                )
                {
                    LogMessage("未设置检查模型或点位", "警告");
                    return false;
                }

                try
                {
                    LogMessage(
                        $"产品已到位，开始执行检查，机种：{_currentModel.ModelName}，检查点数量：{_inspectionPoints.Count}",
                        "信息"
                    );

                    // 启动检查任务
                    _inspectionTask = Task.Run(
                        async () =>
                        {
                            try
                            {
                                await PerformInspectionAsync(
                                    _currentModel,
                                    _inspectionPoints,
                                    _cancellationTokenSource.Token
                                );
                            }
                            catch (OperationCanceledException)
                            {
                                LogMessage("检查任务已取消", "信息");
                            }
                            catch (Exception ex)
                            {
                                OnInspectionError($"检查过程中发生错误: {ex.Message}", ex);
                            }
                        },
                        _cancellationTokenSource.Token
                    );

                    return true;
                }
                catch (Exception ex)
                {
                    OnInspectionError($"启动点位检查失败: {ex.Message}", ex);
                    return false;
                }
            }
        }

        /// <summary>
        /// 暂停检查
        /// </summary>
        public async Task PauseInspectionAsync()
        {
            if (_status != InspectionStatus.Inspecting)
            {
                // 如果不是在检查中状态，记录但不抛出异常，以允许UI层继续处理
                LogMessage($"暂停检查失败：当前状态为{_status}，不是检查中状态", "警告");
                return;
            }

            _isPaused = true;
            SetStatus(InspectionStatus.Paused);
            LogMessage("检查已暂停", "信息");

            // 这里确保任何异步操作都能够安全地暂停
            // 实际暂停由检查循环中的_isPaused标志控制
        }

        /// <summary>
        /// 继续检查
        /// </summary>
        public async Task ResumeInspectionAsync()
        {
            if (_status != InspectionStatus.Paused)
            {
                // 如果不是在暂停状态，记录但不抛出异常，以允许UI层继续处理
                LogMessage($"继续检查失败：当前状态为{_status}，不是暂停状态", "警告");
                return;
            }

            _isPaused = false;
            SetStatus(InspectionStatus.Inspecting);
            LogMessage("检查已继续，将从暂停点继续执行", "信息");

            // 从暂停点继续，暂停标志已清除，检查循环将自动继续
        }

        /// <summary>
        /// 停止检查
        /// </summary>
        public async Task StopInspectionAsync()
        {
            if (!IsInspecting)
            {
                return;
            }

            try
            {
                _cancellationTokenSource?.Cancel();

                if (_inspectionTask != null)
                {
                    await _inspectionTask;
                }

                LogMessage("检查已停止", "信息");
            }
            catch (OperationCanceledException)
            {
                // 预期的异常，不做处理
            }
            catch (Exception ex)
            {
                OnInspectionError($"停止检查失败: {ex.Message}", ex);
            }
            finally
            {
                _status = InspectionStatus.Idle;
                _isPaused = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 执行检查
        /// </summary>
        private async Task PerformInspectionAsync(
            ProductModel model,
            List<InspectionPoint> points,
            CancellationToken cancellationToken
        )
        {
            var results = new List<InspectionPointResult>();
            int ngCount = 0;
            int totalCount = points.Count;

            // 按序号排序检查点
            var sortedPoints = points.OrderBy(p => p.SequenceNo).ToList();

            LogMessage($"开始执行检查，总检查点数：{totalCount}", "信息");

            for (int i = 0; i < sortedPoints.Count; i++)
            {
                // 检查是否取消
                cancellationToken.ThrowIfCancellationRequested();

                // 检查是否暂停
                while (_isPaused)
                {
                    await Task.Delay(100, cancellationToken);
                }

                var point = sortedPoints[i];

                if (!_toolBlockList.ContainsKey(point.PointId))
                {
                    LogMessage(
                        $"检查点 {point.PointName} 未找到对应的工具块，请检查工具块配置",
                        "警告"
                    );
                    continue;
                }

                LogMessage($"开始检查点 {point.PointName}（{i + 1}/{totalCount}）", "信息");

                try
                {
                    // 移动到检查点位置
                    await MoveToPointAsync(point, cancellationToken);

                    // 设置光源亮度
                    await _lightControlService.ApplyLightSettingsAsync(point);

                    // 执行检查
                    var result = await InspectPointAsync(point, cancellationToken);
                    results.Add(result);

                    if (!result.IsPass)
                    {
                        ngCount++;
                        LogMessage($"检查点 {point.PointName} 结果：NG", "警告");
                    }
                    else
                    {
                        LogMessage($"检查点 {point.PointName} 结果：OK", "信息");
                    }

                    // 触发检查点完成事件
                    OnInspectionPointCompleted(point, result, i + 1, totalCount);
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    LogMessage($"检查点 {point.PointName} 检查失败: {ex.Message}", "错误");

                    // 创建失败结果
                    var failResult = new InspectionPointResult
                    {
                        Point = point,
                        IsPass = false,
                        ResultDescription = $"检查失败: {ex.Message}",
                        InspectionTime = DateTime.Now,
                    };

                    results.Add(failResult);
                    ngCount++;

                    // 触发检查点完成事件
                    OnInspectionPointCompleted(point, failResult, i + 1, totalCount);
                }
            }

            // 检查是否全部通过
            bool isAllPass = ngCount == 0;
            LogMessage($"检查完成，结果：{(isAllPass ? "OK" : "NG")}，NG数量：{ngCount}", "信息");

            // 触发检查完成事件
            OnInspectionCompleted(isAllPass, ngCount, totalCount, results);

            // 自动下一轮
            _status = InspectionStatus.Inspecting;
        }

        /// <summary>
        /// 移动到检查点
        /// </summary>
        private async Task MoveToPointAsync(
            InspectionPoint point,
            CancellationToken cancellationToken
        )
        {
            LogMessage(
                $"移动到检查点 {point.PointName}，位置：X={point.XPosition}, Y={point.YPosition}, Z={point.ZPosition}",
                "信息"
            );

            // 检查是否处于离线模拟模式
            bool isOfflineSimulation = _machineConfig.IsOfflineSimulationEnabled();

            if (isOfflineSimulation)
            {
                // 离线模拟模式，仅记录日志并延时模拟到位
                int simulationDelayMs = _machineConfig.GetOfflineSimulationDelayMs();
                LogMessage($"当前为离线模拟模式，将等待 {simulationDelayMs} 毫秒模拟到位", "信息");
                await Task.Delay(simulationDelayMs, cancellationToken);
                LogMessage($"已到达检查点 {point.PointName}（离线模拟）", "信息");
                return;
            }

            // 实际硬件控制模式
            // 设置运动速度
            foreach (var axis in _machineConfig.GetAllAxes())
            {
                _motionCard.SetStartSpeed(axis.AxisNo, (int)axis.StartSpeed);
                _motionCard.SetMaxSpeed(axis.AxisNo, (int)axis.RunSpeed);
            }

            // 设置目标位置
            _motionCard.SetTargetPosition(0, (double)point.XPosition);
            _motionCard.SetTargetPosition(1, (double)point.YPosition);
            _motionCard.SetTargetPosition(2, (double)point.ZPosition);

            // 等待运动完成
            const int positionTolerance = 5; // 位置容差（单位：脉冲）
            const int checkIntervalMs = 100; // 检查间隔（毫秒）
            const int timeoutMs = 30000; // 超时时间（毫秒）
            int elapsedMs = 0;
            bool isPositionReached = false;

            while (!isPositionReached && elapsedMs < timeoutMs)
            {
                // 检查是否取消
                cancellationToken.ThrowIfCancellationRequested();

                // 检查是否暂停
                while (_isPaused)
                {
                    await Task.Delay(checkIntervalMs, cancellationToken);
                }

                // 获取当前位置
                double currentX = _motionCard.GetCurrentPosition(0);
                double currentY = _motionCard.GetCurrentPosition(1);
                double currentZ = _motionCard.GetCurrentPosition(2);

                // 判断是否到达目标位置
                bool isXReached = Math.Abs(currentX - (int)point.XPosition) <= positionTolerance;
                bool isYReached = Math.Abs(currentY - (int)point.YPosition) <= positionTolerance;
                bool isZReached = Math.Abs(currentZ - (int)point.ZPosition) <= positionTolerance;

                isPositionReached = isXReached && isYReached && isZReached;

                if (!isPositionReached)
                {
                    await Task.Delay(checkIntervalMs, cancellationToken);
                    elapsedMs += checkIntervalMs;

                    if (elapsedMs % 1000 == 0) // 每秒记录一次位置信息
                    {
                        LogMessage(
                            $"等待移动完成：当前位置 X={currentX}, Y={currentY}, Z={currentZ}，目标位置 X={(int)point.XPosition}, Y={(int)point.YPosition}, Z={(int)point.ZPosition}",
                            "信息"
                        );
                    }
                }
            }

            if (!isPositionReached && elapsedMs >= timeoutMs)
            {
                throw new TimeoutException($"移动到检查点 {point.PointName} 超时，请检查机械系统");
            }

            LogMessage($"已到达检查点 {point.PointName}", "信息");
        }

        /// <summary>
        /// 检查点位
        /// </summary>
        private async Task<InspectionPointResult> InspectPointAsync(
            InspectionPoint point,
            CancellationToken cancellationToken
        )
        {
            LogMessage($"开始执行检查点 {point.PointName} 的检查", "信息");

            // 检查是否处于离线模拟模式
            bool isOfflineSimulation = _machineConfig.IsOfflineSimulationEnabled();

            // 获取相机配置
            var camera = _machineConfig.GetCameraByIndex(0);

            // 触发相机
            bool triggerSuccess = false;

            if (isOfflineSimulation)
            {
                // 离线模拟模式下，仍然触发真实相机，但跳过运动控制部分
                LogMessage("离线模拟模式：触发实际相机", "信息");
                if (camera != null && camera.TriggerIo != null)
                {
                    if (camera.TriggerIo.Type == "Pulse")
                    {
                        LogMessage(
                            $"触发相机拍照，IO地址：{camera.TriggerIo.IoAddress}，位：{camera.TriggerIo.IoBitIndex}",
                            "信息"
                        );
                        _motionCard.SetOutputBit(
                            camera.TriggerIo.IoAddress,
                            camera.TriggerIo.IoBitIndex,
                            true
                        );
                        await Task.Delay(camera.TriggerIo.PulseWidth, cancellationToken);
                        _motionCard.SetOutputBit(
                            camera.TriggerIo.IoAddress,
                            camera.TriggerIo.IoBitIndex,
                            false
                        );
                        triggerSuccess = true;
                    }
                }
                else
                {
                    // 如果没有配置相机，则使用延时模拟
                    LogMessage("离线模拟模式：未配置相机，使用延时模拟", "警告");
                    int simulationDelayMs = _machineConfig.GetOfflineSimulationDelayMs() / 2;
                    await Task.Delay(simulationDelayMs, cancellationToken);
                    triggerSuccess = true;
                }
            }
            else if (camera != null && camera.TriggerIo != null)
            {
                if (camera.TriggerIo.Type == "Pulse")
                {
                    LogMessage(
                        $"触发相机拍照，IO地址：{camera.TriggerIo.IoAddress}，位：{camera.TriggerIo.IoBitIndex}",
                        "信息"
                    );
                    _motionCard.SetOutputBit(
                        camera.TriggerIo.IoAddress,
                        camera.TriggerIo.IoBitIndex,
                        true
                    );
                    await Task.Delay(camera.TriggerIo.PulseWidth, cancellationToken);
                    _motionCard.SetOutputBit(
                        camera.TriggerIo.IoAddress,
                        camera.TriggerIo.IoBitIndex,
                        false
                    );
                    triggerSuccess = true;
                }
            }

            if (!triggerSuccess)
            {
                LogMessage("相机触发失败，无法执行检查", "错误");
                return new InspectionPointResult
                {
                    Point = point,
                    IsPass = false,
                    ResultDescription = "相机触发失败",
                    InspectionTime = DateTime.Now,
                };
            }

            // 等待图像处理完成
            await Task.Delay(500, cancellationToken);

            // 使用VisionPro获取图像并执行处理
            _visionProService.SetWorkingToolBlock(_toolBlockList[point.PointId]);
            var visionResult = await _visionProService.AcquireAndProcessImageAsync();
            bool isPass = visionResult.FinalResult;

            // 确定图像保存路径
            string directory = $"Images/{point.PointName}";
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            string originalImagePath = $"{directory}/{point.PointName}_{timestamp}_original.bmp";
            string processedImagePath = $"{directory}/{point.PointName}_{timestamp}_processed.bmp";

            // 保存原始图像
            if (visionResult.OriginImage != null)
            {
                _visionProService.SaveImage(
                    new Cognex.VisionPro.CogImage8Grey(visionResult.OriginImage),
                    originalImagePath
                );
                LogMessage($"原始图像已保存至: {originalImagePath}", "信息");
            }
            else
            {
                LogMessage("未能获取原始图像，无法保存", "警告");
            }

            // 保存处理后的图像
            if (visionResult.ProcessedImage != null)
            {
                _visionProService.SaveImage(
                    new Cognex.VisionPro.CogImage8Grey(visionResult.ProcessedImage),
                    processedImagePath
                );
                LogMessage($"处理后图像已保存至: {processedImagePath}", "信息");
            }
            else
            {
                LogMessage("未能获取处理后图像，无法保存", "警告");
                processedImagePath = "Images/default.bmp"; // 使用默认图像路径
            }

            // 记录处理时间
            LogMessage($"图像处理耗时: {visionResult.ProcessTimeMs:F2}ms", "信息");

            // 记录缺陷信息
            if (visionResult.Defects != null && visionResult.Defects.Count > 0)
            {
                LogMessage($"检测到{visionResult.Defects.Count}个缺陷:", "警告");
                foreach (var defect in visionResult.Defects)
                {
                    if (defect.Type == "矩形区域")
                    {
                        LogMessage(
                            $"  矩形缺陷: X={defect.X:F2}, Y={defect.Y:F2}, 宽={defect.Width:F2}, 高={defect.Height:F2}, 评分={defect.Score:F2}",
                            "警告"
                        );
                    }
                    else if (defect.Type == "圆形区域")
                    {
                        LogMessage(
                            $"  圆形缺陷: X={defect.X:F2}, Y={defect.Y:F2}, 半径={defect.Radius:F2}, 评分={defect.Score:F2}",
                            "警告"
                        );
                    }
                    else
                    {
                        LogMessage(
                            $"  其他缺陷: 类型={defect.Type}, X={defect.X:F2}, Y={defect.Y:F2}",
                            "警告"
                        );
                    }
                }
            }

            // 记录其他结果数据
            if (visionResult.ResultData != null && visionResult.ResultData.Count > 0)
            {
                LogMessage("检测结果数据:", "信息");
                foreach (var data in visionResult.ResultData)
                {
                    LogMessage($"  {data.Key}: {data.Value}", "信息");
                }
            }

            // 创建检查结果
            var result = new InspectionPointResult
            {
                Point = point,
                IsPass = isPass,
                ResultDescription = visionResult.ResultDescription,
                ImagePath = originalImagePath, // 使用原始图像路径
                InspectionTime = DateTime.Now,
            };

            LogMessage(
                $"检查点 {point.PointName} 检查完成，结果：{result.ResultDescription}",
                "信息"
            );
            return result;
        }

        /// <summary>
        /// 触发检查完成事件
        /// </summary>
        protected virtual void OnInspectionCompleted(
            bool isPass,
            int ngCount,
            int totalCount,
            List<InspectionPointResult> results
        )
        {
            SetStatus(InspectionStatus.Completed);

            LogMessage(
                $"检查全部完成，结果：{(isPass ? "OK" : "NG")}，NG数量：{ngCount}/{totalCount}",
                "信息"
            );
            InspectionCompleted?.Invoke(
                this,
                new InspectionCompletedEventArgs
                {
                    IsPass = isPass,
                    NgCount = ngCount,
                    TotalCount = totalCount,
                    Results = results,
                }
            );
        }

        /// <summary>
        /// 触发检查点完成事件
        /// </summary>
        protected virtual void OnInspectionPointCompleted(
            InspectionPoint point,
            InspectionPointResult result,
            int currentIndex,
            int totalCount
        )
        {
            LogMessage(
                $"检查点 {point.PointName} 完成（{currentIndex}/{totalCount}），结果：{result.ResultDescription}",
                "信息"
            );
            InspectionPointCompleted?.Invoke(
                this,
                new InspectionPointCompletedEventArgs
                {
                    Point = point,
                    IsPass = result.IsPass,
                    Result = result,
                    CurrentIndex = currentIndex,
                    TotalCount = totalCount,
                }
            );
        }

        /// <summary>
        /// 触发检查错误事件
        /// </summary>
        protected virtual void OnInspectionError(string errorMessage, Exception exception)
        {
            SetStatus(InspectionStatus.Error);

            if (exception != null)
            {
                LogMessage(errorMessage, "错误");
            }
            else
            {
                LogMessage(errorMessage, "错误");
            }

            InspectionError?.Invoke(
                this,
                new InspectionErrorEventArgs { ErrorMessage = errorMessage, Exception = exception }
            );
        }

        /// <summary>
        /// 设置状态并触发状态变更事件
        /// </summary>
        private void SetStatus(InspectionStatus status)
        {
            if (_status != status)
            {
                _status = status;
                StatusChanged?.Invoke(this, status);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 停止任何正在进行的检测任务
                StopInspectionAsync().Wait();

                // 取消订阅状态管理器的报警事件
                if (_statusManager != null)
                {
                    _statusManager.AlarmsChanged -= OnAlarmsChanged;
                    _logService.LogInformation("已取消订阅状态管理器的报警事件", "检查服务");
                }

                _logService.LogInformation("检查服务已释放", "检查服务");
            }
            catch (Exception ex)
            {
                _logService.LogError(ex, "释放检查服务时发生错误", "检查服务");
            }
        }
    }

    /// <summary>
    /// 日志消息事件参数
    /// </summary>
    public class LogMessageEventArgs : EventArgs
    {
        public DateTime Timestamp { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
    }
}
