using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nickel_Inspect.Models.LightControl;

namespace Nickel_Inspect.Services.LightControl
{
    /// <summary>
    /// 光源控制器基类，提供通用功能和协议实现
    /// </summary>
    public abstract class LightControllerBase : IDisposable
    {
        protected SerialPort _serialPort;
        protected LightController _config;
        protected bool _isInitialized = false;

        /// <summary>
        /// 获取控制器配置
        /// </summary>
        public LightController Config => _config;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">光源控制器配置</param>
        public LightControllerBase(LightController config)
        {
            _config = config;
        }

        /// <summary>
        /// 初始化光源控制器
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public virtual async Task<bool> InitializeAsync()
        {
            try
            {
                if (_isInitialized && _serialPort != null && _serialPort.IsOpen)
                {
                    return true;
                }

                _serialPort = new SerialPort
                {
                    PortName = _config.Port,
                    BaudRate = _config.BaudRate,
                    DataBits = _config.DataBits,
                    Parity = (System.IO.Ports.Parity)_config.Parity,
                    StopBits = (System.IO.Ports.StopBits)_config.StopBits,
                };

                _serialPort.Open();
                _isInitialized = true;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化光源控制器失败: {ex.Message}");
                _isInitialized = false;
                return false;
            }
        }

        /// <summary>
        /// 关闭光源控制器
        /// </summary>
        public virtual async Task ShutdownAsync()
        {
            try
            {
                if (_serialPort != null && _serialPort.IsOpen)
                {
                    await TurnOffAllChannelsAsync();
                    _serialPort.Close();
                    _serialPort.Dispose();
                    _serialPort = null;
                }
                _isInitialized = false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"关闭光源控制器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <param name="brightness">亮度值</param>
        /// <returns>是否设置成功</returns>
        public abstract Task<bool> SetChannelBrightnessAsync(int channel, int brightness);

        /// <summary>
        /// 获取指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <returns>通道亮度值</returns>
        public abstract Task<int> GetChannelBrightnessAsync(int channel);

        /// <summary>
        /// 设置RGB通道亮度
        /// </summary>
        /// <param name="redBrightness">红色通道亮度</param>
        /// <param name="greenBrightness">绿色通道亮度</param>
        /// <param name="blueBrightness">蓝色通道亮度</param>
        /// <returns>是否设置成功</returns>
        public virtual async Task<bool> SetRgbBrightnessAsync(
            int redBrightness,
            int greenBrightness,
            int blueBrightness
        )
        {
            if (!_config.SupportsRgb)
            {
                Console.WriteLine("此光源控制器不支持RGB控制");
                return false;
            }

            bool result = true;
            result &= await SetChannelBrightnessAsync(1, redBrightness);
            result &= await SetChannelBrightnessAsync(2, greenBrightness);
            result &= await SetChannelBrightnessAsync(3, blueBrightness);

            return result;
        }

        /// <summary>
        /// 关闭所有通道
        /// </summary>
        /// <returns>是否操作成功</returns>
        public virtual async Task<bool> TurnOffAllChannelsAsync()
        {
            bool result = true;
            for (int i = 1; i <= _config.ChannelCount; i++)
            {
                result &= await SetChannelBrightnessAsync(i, 0);
            }
            return result;
        }

        /// <summary>
        /// 发送指令到光源控制器
        /// </summary>
        /// <param name="command">指令字节数组</param>
        /// <returns>是否发送成功</returns>
        protected virtual async Task<bool> SendCommandAsync(byte[] command)
        {
            try
            {
                if (_serialPort == null || !_serialPort.IsOpen)
                {
                    await InitializeAsync();
                    if (_serialPort == null || !_serialPort.IsOpen)
                    {
                        return false;
                    }
                }

                _serialPort.Write(command, 0, command.Length);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送指令失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 接收光源控制器响应
        /// </summary>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>响应字节数组</returns>
        protected virtual async Task<byte[]> ReceiveResponseAsync(int timeout = 1000)
        {
            try
            {
                if (_serialPort == null || !_serialPort.IsOpen)
                {
                    return null;
                }

                _serialPort.ReadTimeout = timeout;

                // 等待数据到达
                await Task.Delay(100);

                int bytesToRead = _serialPort.BytesToRead;
                if (bytesToRead == 0)
                {
                    return new byte[0];
                }

                byte[] buffer = new byte[bytesToRead];
                _serialPort.Read(buffer, 0, bytesToRead);
                return buffer;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"接收响应失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            ShutdownAsync().Wait();
        }
    }
}
