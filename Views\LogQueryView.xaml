<UserControl
    x:Class="Nickel_Inspect.Views.LogQueryView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:Nickel_Inspect.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:Nickel_Inspect.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:LogQueryViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="800"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  日志类型过滤行  -->
        <Grid Grid.Row="0" Margin="10,10,10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="0"
                Margin="0,0,10,0"
                VerticalAlignment="Center"
                Text="日志类型:" />

            <CheckBox
                Grid.Column="1"
                Margin="5,0"
                Content="信息"
                IsChecked="{Binding ShowInfo}" />

            <CheckBox
                Grid.Column="2"
                Margin="5,0"
                Content="警告"
                IsChecked="{Binding ShowWarning}" />

            <CheckBox
                Grid.Column="3"
                Margin="5,0"
                Content="错误"
                IsChecked="{Binding ShowError}" />

            <CheckBox
                Grid.Column="4"
                Margin="5,0"
                Content="调试"
                IsChecked="{Binding ShowDebug}" />

            <Button
                Grid.Column="6"
                Margin="5,0"
                Command="{Binding SelectAllTypesCommand}"
                Content="全选" />

            <Button
                Grid.Column="7"
                Margin="5,0"
                Command="{Binding UnselectAllTypesCommand}"
                Content="全不选" />
        </Grid>

        <!--  日志来源过滤行  -->
        <Grid Grid.Row="1" Margin="10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="0"
                Margin="0,0,10,0"
                VerticalAlignment="Center"
                Text="日志来源:" />

            <ComboBox
                Grid.Column="1"
                Width="200"
                Margin="0,0,10,0"
                DisplayMemberPath="Name"
                ItemsSource="{Binding SourcesFilter}"
                SelectedValue="{Binding SelectedSource}"
                SelectedValuePath="Name" />

            <Button
                Grid.Column="3"
                Width="80"
                Command="{Binding RefreshSourcesCommand}"
                Content="刷新来源" />
        </Grid>

        <!--  时间范围过滤行  -->
        <Grid Grid.Row="2" Margin="10,5,10,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock
                Grid.Column="0"
                Margin="0,0,10,0"
                VerticalAlignment="Center"
                Text="开始时间:" />

            <DatePicker
                Grid.Column="1"
                Width="120"
                Margin="0,0,5,0"
                SelectedDate="{Binding StartTime}" />

            <hc:TimePicker
                Grid.Column="2"
                Width="100"
                Margin="0,0,20,0"
                SelectedTime="{Binding StartTime}" />

            <TextBlock
                Grid.Column="3"
                Margin="0,0,10,0"
                VerticalAlignment="Center"
                Text="结束时间:" />

            <DatePicker
                Grid.Column="4"
                Width="120"
                Margin="0,0,5,0"
                SelectedDate="{Binding EndTime}" />

            <hc:TimePicker
                Grid.Column="5"
                Width="100"
                HorizontalAlignment="Left"
                SelectedTime="{Binding EndTime}" />

            <Button
                Grid.Column="6"
                Width="100"
                Command="{Binding QueryCommand}"
                Content="查询" />
        </Grid>

        <!--  日志数据表格  -->
        <DataGrid
            Grid.Row="3"
            Margin="10,0"
            AutoGenerateColumns="False"
            CanUserAddRows="False"
            CanUserDeleteRows="False"
            CanUserResizeRows="False"
            IsReadOnly="True"
            ItemsSource="{Binding Logs}"
            RowHeaderWidth="0"
            SelectionMode="Extended"
            SelectionUnit="FullRow">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Width="150"
                    Binding="{Binding Timestamp, StringFormat=yyyy-MM-dd HH:mm:ss}"
                    Header="时间戳" />
                <DataGridTextColumn
                    Width="80"
                    Binding="{Binding LogType}"
                    Header="级别" />
                <DataGridTextColumn
                    Width="100"
                    Binding="{Binding Source}"
                    Header="来源" />
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Message}"
                    Header="消息" />
            </DataGrid.Columns>
        </DataGrid>

        <!--  状态和操作栏  -->
        <Grid Grid.Row="4" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <Button
                Grid.Column="0"
                Width="100"
                Margin="0,0,10,0"
                Command="{Binding ExportCsvCommand}"
                Content="导出CSV" />

            <Button
                Grid.Column="1"
                Width="100"
                Command="{Binding ClearCommand}"
                Content="清空结果" />

            <TextBlock
                Grid.Column="3"
                VerticalAlignment="Center"
                Text="{Binding StatusMessage}" />

            <!--  加载指示器  -->
            <hc:LoadingCircle
                Grid.Row="0"
                Grid.Column="0"
                Grid.ColumnSpan="4"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                IsRunning="{Binding IsLoading}" />
        </Grid>
    </Grid>
</UserControl> 