using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using HandyControl.Controls;
using MessageBox = HandyControl.Controls.MessageBox;

namespace Nickel_Inspect.Views.Dialogs
{
    /// <summary>
    /// StartupSplashDialog.xaml 的交互逻辑
    /// </summary>
    public partial class StartupSplashDialog : HandyControl.Controls.Window
    {
        private CancellationTokenSource _cts = new CancellationTokenSource();

        /// <summary>
        /// 标记是否可以关闭启动画面
        /// </summary>
        public bool CanClose { get; set; } = false;

        public StartupSplashDialog()
        {
            InitializeComponent();

            this.Loaded += StartupSplashDialog_Loaded;
            this.Closing += StartupSplashDialog_Closing;
        }

        private void StartupSplashDialog_Loaded(object sender, RoutedEventArgs e)
        {
            // 开始脉动动画
            StartPulseAnimation();

            // 创建启动进度更新任务
            _ = UpdateStartupProgressAsync(_cts.Token);
        }

        private void StartupSplashDialog_Closing(
            object sender,
            System.ComponentModel.CancelEventArgs e
        )
        {
            // 如果尚未准备好关闭，则取消关闭操作
            if (!CanClose)
            {
                e.Cancel = true;
                return;
            }

            // 取消任务
            _cts.Cancel();
        }

        /// <summary>
        /// 创建脉动动画，使Logo看起来更生动
        /// </summary>
        private void StartPulseAnimation()
        {
            // 创建缩放动画
            var scaleAnimation = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0.95,
                To = 1.05,
                Duration = TimeSpan.FromSeconds(1.5),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever,
            };

            // 应用缩放动画
            LogoScaleTransform.BeginAnimation(
                System.Windows.Media.ScaleTransform.ScaleXProperty,
                scaleAnimation
            );
            LogoScaleTransform.BeginAnimation(
                System.Windows.Media.ScaleTransform.ScaleYProperty,
                scaleAnimation
            );
        }

        /// <summary>
        /// 更新启动状态的文本显示
        /// </summary>
        public void UpdateStatus(string statusText)
        {
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(() => StatusTextBlock.Text = statusText);
            }
            else
            {
                StatusTextBlock.Text = statusText;
            }
        }

        /// <summary>
        /// 关闭启动界面
        /// </summary>
        public void CloseSplash()
        {
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(() =>
                {
                    CanClose = true;
                    Close();
                });
            }
            else
            {
                CanClose = true;
                Close();
            }
        }

        /// <summary>
        /// 模拟启动进度的异步方法
        /// </summary>
        private async Task UpdateStartupProgressAsync(CancellationToken cancellationToken)
        {
            string[] startupMessages = new string[]
            {
                "系统初始化中...",
                "正在加载配置...",
                "正在初始化硬件连接...",
                "正在初始化视觉系统...",
                "正在准备运动控制系统...",
                "正在检查系统状态...",
                "准备就绪，即将启动...",
            };

            try
            {
                for (
                    int i = 0;
                    i < startupMessages.Length && !cancellationToken.IsCancellationRequested;
                    i++
                )
                {
                    UpdateStatus(startupMessages[i]);
                    await Task.Delay(800, cancellationToken); // 每个消息显示800毫秒
                }
            }
            catch (OperationCanceledException)
            {
                // 任务被取消，正常退出
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"启动过程中发生错误: {ex.Message}",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }
    }
}
