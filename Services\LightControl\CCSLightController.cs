using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nickel_Inspect.Models.LightControl;

namespace Nickel_Inspect.Services.LightControl
{
    /// <summary>
    /// CCS品牌光源控制器实现
    /// </summary>
    public class CCSLightController : LightControllerBase
    {
        private readonly Dictionary<int, int> _channelBrightness = new Dictionary<int, int>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="config">光源控制器配置</param>
        public CCSLightController(LightController config)
            : base(config)
        {
            // 初始化通道亮度缓存
            for (int i = 1; i <= config.ChannelCount; i++)
            {
                _channelBrightness[i] = 0;
            }
        }

        /// <summary>
        /// 设置指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <param name="brightness">亮度值</param>
        /// <returns>是否设置成功</returns>
        public override async Task<bool> SetChannelBrightnessAsync(int channel, int brightness)
        {
            if (channel < 1 || channel > _config.ChannelCount)
            {
                Console.WriteLine($"通道号无效: {channel}");
                return false;
            }

            // 限制亮度范围
            int brightnessValue = Math.Min(Math.Max(brightness, 0), _config.MaxBrightness);

            // CCS控制器使用命令格式: "c[通道号][亮度值]<CR>"
            // 例如: "c1128" 表示设置通道1的亮度为128
            string command = $"c{channel}{brightnessValue}\r";
            byte[] commandBytes = Encoding.ASCII.GetBytes(command);

            bool result = await SendCommandAsync(commandBytes);
            if (result)
            {
                _channelBrightness[channel] = brightnessValue;
            }
            return result;
        }

        /// <summary>
        /// 获取指定通道亮度
        /// </summary>
        /// <param name="channel">通道号</param>
        /// <returns>通道亮度值</returns>
        public override async Task<int> GetChannelBrightnessAsync(int channel)
        {
            if (channel < 1 || channel > _config.ChannelCount)
            {
                Console.WriteLine($"通道号无效: {channel}");
                return -1;
            }

            // CCS控制器使用命令格式: "r[通道号]<CR>" 读取亮度
            // 例如: "r1" 表示读取通道1的亮度
            string command = $"r{channel}\r";
            byte[] commandBytes = Encoding.ASCII.GetBytes(command);

            bool sendResult = await SendCommandAsync(commandBytes);
            if (!sendResult)
            {
                return _channelBrightness.ContainsKey(channel) ? _channelBrightness[channel] : 0;
            }

            byte[] response = await ReceiveResponseAsync();
            if (response == null || response.Length == 0)
            {
                return _channelBrightness.ContainsKey(channel) ? _channelBrightness[channel] : 0;
            }

            try
            {
                // 响应格式: "[亮度值]<CR>" 例如: "128\r"
                string responseText = Encoding.ASCII.GetString(response).Trim();
                if (int.TryParse(responseText, out int brightness))
                {
                    _channelBrightness[channel] = brightness;
                    return brightness;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析响应失败: {ex.Message}");
            }

            return _channelBrightness.ContainsKey(channel) ? _channelBrightness[channel] : 0;
        }
    }
}
