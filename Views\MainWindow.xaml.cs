using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Nickel_Inspect.ViewModels;

namespace Nickel_Inspect.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : HandyControl.Controls.Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // 添加窗口关闭事件处理
            this.Closing += MainWindow_Closing;
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // 获取视图模型
            if (this.DataContext is MainWindowViewModel viewModel && viewModel.ExitCommand != null)
            {
                // 取消默认关闭行为
                e.Cancel = true;

                // 调用ViewModel中的ConfirmExit方法
                viewModel.ConfirmExit();
            }
        }
    }
}
