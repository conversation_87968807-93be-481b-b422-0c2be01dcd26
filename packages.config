﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Dapper" version="2.1.66" targetFramework="net48" />
  <package id="DryIoc.dll" version="4.7.7" targetFramework="net48" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net48" />
  <package id="FluentModbus" version="5.3.1" targetFramework="net48" />
  <package id="HandyControl" version="3.5.1" targetFramework="net481" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.1" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.Xaml.Behaviors.Wpf" version="1.1.31" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Obfuscar" version="2.2.47" targetFramework="net481" developmentDependency="true" />
  <package id="Prism.Core" version="8.1.97" targetFramework="net48" />
  <package id="Prism.DryIoc" version="8.1.97" targetFramework="net48" />
  <package id="Prism.Wpf" version="8.1.97" targetFramework="net48" />
  <package id="Serilog" version="4.0.0" targetFramework="net48" />
  <package id="Serilog.Sinks.File" version="6.0.0" targetFramework="net48" />
  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="1.0.119.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Data.SQLite" version="1.0.119.0" targetFramework="net48" />
  <package id="System.Data.SQLite.Core" version="1.0.119.0" targetFramework="net48" />
  <package id="System.Data.SQLite.EF6" version="1.0.119.0" targetFramework="net48" />
  <package id="System.Data.SQLite.Linq" version="1.0.119.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net48" />
  <package id="System.IO.Ports" version="5.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Channels" version="8.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
</packages>