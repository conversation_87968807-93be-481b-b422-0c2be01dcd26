using System;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;

namespace Nickel_Inspect.Views
{
    /// <summary>
    /// InspectionView.xaml 的交互逻辑
    /// </summary>
    public partial class InspectionView : UserControl
    {
        public InspectionView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 处理检查结果项目点击事件
        /// </summary>
        private void Border_MouseLeftButtonDown(
            object sender,
            System.Windows.Input.MouseButtonEventArgs e
        )
        {
            if (sender is Border border && border.DataContext != null && DataContext != null)
            {
                // 将点击的项目设置为选中项
                var viewModel = DataContext as ViewModels.InspectionViewModel;
                if (viewModel != null)
                {
                    viewModel.SelectedResult = border.DataContext as ViewModels.InspectionResult;
                }
            }
        }

        /// <summary>
        /// 滚动日志到底部
        /// </summary>
        public void ScrollLogToEnd()
        {
            if (LogRichTextBox != null)
            {
                LogRichTextBox.ScrollToEnd();
            }
        }

        /// <summary>
        /// 添加日志到RichTextBox
        /// </summary>
        public void AppendLogMessage(DateTime timestamp, string type, string message)
        {
            if (LogRichTextBox == null)
                return;

            // 创建新的段落
            Paragraph paragraph = new Paragraph();
            paragraph.LineHeight = 15; // 设置较小的行间距
            paragraph.Margin = new System.Windows.Thickness(0); // 移除段落边距

            // 添加时间戳
            Run timestampRun = new Run(timestamp.ToString("HH:mm:ss.fff") + " ");
            timestampRun.Foreground = System.Windows.Media.Brushes.Gray;
            paragraph.Inlines.Add(timestampRun);

            // 添加类型，根据类型设置不同颜色
            Run typeRun = new Run(type + " ");
            switch (type)
            {
                case "错误":
                    typeRun.Foreground = System.Windows.Media.Brushes.Red;
                    break;
                case "警告":
                    typeRun.Foreground = System.Windows.Media.Brushes.Orange;
                    break;
                case "信息":
                    typeRun.Foreground = System.Windows.Media.Brushes.Green;
                    break;
                default:
                    typeRun.Foreground = System.Windows.Media.Brushes.Black;
                    break;
            }
            paragraph.Inlines.Add(typeRun);

            // 添加消息
            Run messageRun = new Run(message);
            paragraph.Inlines.Add(messageRun);

            // 将段落添加到文档
            LogRichTextBox.Document.Blocks.Add(paragraph);

            // 滚动到底部
            ScrollLogToEnd();
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        public void ClearLog()
        {
            if (LogRichTextBox != null)
            {
                LogRichTextBox.Document.Blocks.Clear();
            }
        }

        /// <summary>
        /// 打开图像所在文件夹
        /// </summary>
        private void OpenImageFolder_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                ViewModels.InspectionResult result = null;

                // 根据不同的触发元素获取InspectionResult对象
                if (sender is MenuItem menuItem && menuItem.Parent is ContextMenu contextMenu)
                {
                    // 来自图像的右键菜单
                    if (
                        contextMenu.PlacementTarget is Image image
                        && image.DataContext is ViewModels.InspectionResult imgResult
                    )
                    {
                        result = imgResult;
                    }
                    // 来自Border的右键菜单
                    else if (
                        contextMenu.PlacementTarget is Border border
                        && border.DataContext is ViewModels.InspectionResult borderResult
                    )
                    {
                        result = borderResult;
                    }
                }

                if (result == null)
                {
                    System.Windows.MessageBox.Show(
                        "无法获取图像信息",
                        "错误",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error
                    );
                    return;
                }

                // 获取图像路径
                string imagePath = result.ImagePath;

                // 如果路径为空或不存在，则不执行操作
                if (string.IsNullOrEmpty(imagePath) || !System.IO.File.Exists(imagePath))
                {
                    System.Windows.MessageBox.Show(
                        "图像文件不存在",
                        "错误",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Error
                    );
                    return;
                }

                // 获取文件所在的文件夹
                string folderPath = System.IO.Path.GetFullPath(imagePath);
                string folderName = System.IO.Path.GetDirectoryName(folderPath);

                // 打开文件资源管理器并选中该文件
                System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{folderName}\"");

                // 记录日志
                var viewModel = DataContext as ViewModels.InspectionViewModel;
                viewModel?.AddLogMessage("信息", $"已打开图像所在文件夹: {folderPath}");
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"无法打开文件夹: {ex.Message}",
                    "错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error
                );
            }
        }

        /// <summary>
        /// 在UI上显示系统状态变更通知
        /// </summary>
        public void ShowStatusNotification(string title, string message, string type = "信息")
        {
            try
            {
                // 使用HandyControl的Growl控件显示通知
                switch (type.ToLower())
                {
                    case "错误":
                        HandyControl.Controls.Growl.Error(message, title);
                        break;
                    case "警告":
                        HandyControl.Controls.Growl.Warning(message, title);
                        break;
                    case "信息":
                    default:
                        HandyControl.Controls.Growl.Info(message, title);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示状态通知失败: {ex.Message}");
            }
        }
    }
}
