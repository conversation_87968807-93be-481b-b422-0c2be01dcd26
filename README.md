# 镍片缺陷检测系统

> 基于工业视觉的PCB镍片缺陷自动检测系统  
> 支持全自动传输、定位、检测、分拣的智能化生产线解决方案

[![许可证](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![.NET Framework](https://img.shields.io/badge/.NET%20Framework-4.7.2-purple.svg)](https://dotnet.microsoft.com/)
[![WPF](https://img.shields.io/badge/UI-WPF-blue.svg)](https://docs.microsoft.com/zh-cn/dotnet/desktop/wpf/)

## 📷 系统界面展示

### 主检测界面
![主界面](DemoImages/mainWindows.jpg)
*实时检测界面，支持相机图像显示和检测结果统计*

### 轨道控制系统
![轨道控制](DemoImages/TrackDebug_Windows.jpg)
*传送带控制、SMEMA通信和系统状态监控*

### 检测点配置
![检测点配置](DemoImages/checkPoint_Cfg.jpg)
*配置检测点坐标、相机参数和光源设置*

### IO状态监控
![IO监控](DemoImages/IO_Windows.jpg)
*实时监控所有输入输出信号状态*

### 系统日志
![系统日志](DemoImages/logWindows.jpg)
*完善的日志记录，支持过滤和搜索功能*

### 视觉测试界面
![视觉测试](DemoImages/testVpp.jpg)
*加载和测试VPP文件，验证视觉算法*

## ✨ 核心功能

### 🔍 **智能检测**
- 基于工业视觉的高精度镍片缺陷检测
- 使用Cognex VisionPro SDK进行实时图像处理
- 可配置检测点，支持独立相机设置
- 统计分析和报表生成

### 🏭 **生产集成**
- 支持SMEMA标准通信，无缝集成生产线
- 全自动PCB处理：进料 → 定位 → 检测 → 出料
- 支持左进右出和右进左出两个方向
- 多种运行模式：正常、直通、手动

### 🎛️ **精密控制**
- XYZ运动平台，实现精确定位
- 传送带系统，支持调宽装置
- 气缸控制（阻挡气缸和夹紧气缸）
- 紧急停止和完善的错误处理

### 🖥️ **用户界面**
- 现代化WPF界面，实时状态显示
- 多标签页导航：检测、轨道控制、配置、IO监控
- 产品型号管理和检测点设置
- 完善的日志记录和数据导出

## 🛠️ 技术架构

| 组件 | 技术 | 用途 |
|-----------|------------|---------|
| **框架** | .NET Framework 4.7.2 | 核心应用程序框架 |
| **界面** | WPF + HandyControl | 现代桌面界面 |
| **架构** | MVVM (Prism + DryIoc) | 清晰的关注点分离 |
| **视觉** | Cognex VisionPro SDK | 工业图像处理 |
| **数据库** | SQLite | 本地数据存储 |
| **运动控制** | 自定义SDK (BkMotionCard) | 硬件运动控制 |
| **序列化** | Newtonsoft.Json | 配置管理 |

## 🚀 快速开始

### 环境要求
- Windows 10 或更高版本
- Visual Studio 2019/2022
- .NET Framework 4.7.2 SDK
- Cognex VisionPro SDK（用于视觉功能）

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/yourusername/Nickel_Inspect.git
cd Nickel_Inspect
```

2. **打开项目**
```
在Visual Studio中打开 Nickel_Inspect.sln
```

3. **恢复NuGet包**
```
右键解决方案 → 还原NuGet程序包
```

4. **配置设置**
- 编辑 `DeviceConfiguration.json` 进行硬件配置
- 编辑 `ModbusConfig.json` 进行通信设置
- 启用离线模式：设置 `"enableOfflineSimulation": true` 用于无硬件测试

5. **编译运行**
```
按F5编译并启动应用程序
```

## 📋 使用说明

### 运行模式
- **正常模式**：全自动检测，包含传送带控制
- **直通模式**：跳过检测，PCB直接通过
- **手动模式**：手动控制，用于调试和设置

### 基本工作流程
1. 配置产品型号和检测点
2. 设置相机位置和光源
3. 启动轨道控制系统
4. PCB自动进料、检测和分拣
5. 实时查看结果和统计数据

## 🏗️ 系统架构

系统采用模块化、服务化的设计：

- **轨道控制模块**：PCB传送和定位
- **视觉模块**：图像采集和缺陷检测
- **运动控制模块**：XYZ轴控制和相机定位
- **状态管理器**：设备监控和状态管理
- **数据库服务**：数据存储和检索
- **报警系统**：全面的错误监控
- **光源控制**：LED照明管理
- **用户界面模块**：用户界面和数据可视化

## 📁 项目结构

```
Nickel_Inspect/
├── Models/                 # 数据模型和实体
│   ├── Track/              # 传送系统模型
│   ├── LightControl/       # 光源系统模型
│   └── ...                 # 配置和检测模型
├── Services/               # 业务逻辑和硬件接口
│   ├── Track/              # 传送控制服务
│   ├── Alarms/             # 报警监控系统
│   ├── LightControl/       # LED光源服务
│   └── ...                 # 视觉、数据库和运动服务
├── ViewModels/             # MVVM视图模型
├── Views/                  # WPF用户界面
│   ├── Dialogs/            # 模态对话框
│   └── ...                 # 主窗口和用户控件
├── Config/                 # 配置文件
├── DemoImages/             # 系统截图
└── Resources/              # 应用程序资源
```

## ⚙️ 配置说明

### 设备配置
编辑 `DeviceConfiguration.json` 配置：
- 运动轴参数（X、Y、Z）
- IO点位映射
- 报警系统设置
- 相机和光源设置

### 配置示例
```json
{
  "deviceConfig": {
    "axes": [
      {
        "axisNo": 0,
        "name": "X",
        "runSpeed": 2000,
        "maxDistance": 500.0
      }
    ],
    "enableOfflineSimulation": true
  }
}
```

## 🔧 开发指南

### 添加新功能
1. 在 `Services/` 中创建服务接口
2. 实现业务逻辑
3. 创建对应的ViewModels
4. 设计WPF视图
5. 在 `App.xaml.cs` 中注册服务

### 调试技巧
- 启用离线模拟模式进行无硬件测试
- 使用IO监控视图检查信号状态
- 查看系统日志获取详细错误信息
- 使用Visual Studio调试器设置断点

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

