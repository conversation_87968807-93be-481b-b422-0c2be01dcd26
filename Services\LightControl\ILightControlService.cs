using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nickel_Inspect.Models;
using Nickel_Inspect.Models.LightControl;

namespace Nickel_Inspect.Services.LightControl
{
    /// <summary>
    /// 光源控制服务接口，用于管理和操作不同品牌的光源控制器
    /// </summary>
    public interface ILightControlService
    {
        /// <summary>
        /// 初始化光源控制服务
        /// </summary>
        /// <returns>初始化是否成功</returns>
        Task<bool> InitializeAsync();

        /// <summary>
        /// 关闭光源控制服务
        /// </summary>
        Task ShutdownAsync();

        /// <summary>
        /// 应用检查点的光源设置
        /// </summary>
        /// <param name="point">检查点</param>
        /// <returns>设置是否成功</returns>
        Task<bool> ApplyLightSettingsAsync(InspectionPoint point);

        /// <summary>
        /// 设置指定控制器的指定通道亮度
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <param name="channel">通道号</param>
        /// <param name="brightness">亮度值 (0-255)</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetChannelBrightnessAsync(int controllerId, int channel, int brightness);

        /// <summary>
        /// 设置RGB三通道亮度
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <param name="redBrightness">红色通道亮度</param>
        /// <param name="greenBrightness">绿色通道亮度</param>
        /// <param name="blueBrightness">蓝色通道亮度</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetRgbBrightnessAsync(
            int controllerId,
            int redBrightness,
            int greenBrightness,
            int blueBrightness
        );

        /// <summary>
        /// 获取指定控制器的指定通道亮度
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <param name="channel">通道号</param>
        /// <returns>通道亮度值</returns>
        Task<int> GetChannelBrightnessAsync(int controllerId, int channel);

        /// <summary>
        /// 关闭指定控制器的所有通道
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> TurnOffAllChannelsAsync(int controllerId);

        /// <summary>
        /// 获取所有可用的光源控制器
        /// </summary>
        /// <returns>控制器列表</returns>
        Task<IList<LightController>> GetAvailableControllersAsync();

        /// <summary>
        /// 添加新的光源控制器
        /// </summary>
        /// <param name="controller">控制器配置</param>
        /// <returns>新控制器的ID</returns>
        Task<int> AddControllerAsync(LightController controller);

        /// <summary>
        /// 移除光源控制器
        /// </summary>
        /// <param name="controllerId">控制器ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> RemoveControllerAsync(int controllerId);
    }
}
