{"DeviceConfig": {"DeviceName": "设备名称", "DeviceModel": "设备型号", "Axes": [{"AxisNo": 0, "Name": "X", "PulseEquivalent": 0.04, "StartSpeed": 200, "RunSpeed": 500, "JogMaxSpeed": 200.0, "MaxDistance": 1200.0, "AccelerationTime": 200, "HomeMaxDistance": 1200.0, "HomeDirection": 1}, {"AxisNo": 1, "Name": "Y", "PulseEquivalent": 0.04, "StartSpeed": 200, "RunSpeed": 500, "JogMaxSpeed": 200.0, "MaxDistance": 800.0, "AccelerationTime": 200, "HomeMaxDistance": 800.0, "HomeDirection": 1}, {"AxisNo": 2, "Name": "Z", "PulseEquivalent": 0.032, "StartSpeed": 200, "RunSpeed": 500, "JogMaxSpeed": 200, "MaxDistance": 200, "AccelerationTime": 200, "HomeMaxDistance": 120.0, "HomeDirection": -1}, {"AxisNo": 3, "Name": "<PERSON><PERSON><PERSON>", "PulseEquivalent": 0.02, "StartSpeed": 200, "RunSpeed": 500.0, "JogMaxSpeed": 200.0, "MaxDistance": 800.0, "AccelerationTime": 200, "HomeMaxDistance": 350.0, "HomeDirection": 1}, {"AxisNo": 4, "Name": "Belt", "PulseEquivalent": 0.02, "StartSpeed": 100, "RunSpeed": 150.0, "JogMaxSpeed": 200.0, "MaxDistance": 1000000.0, "AccelerationTime": 200, "HomeMaxDistance": 1200.0, "HomeDirection": 1}], "Conveyor": {"Sensors": {"IncomingBoard": {"Group": "X", "SubGroup": 0, "BitIndex": 7, "Name": "进料感应", "Type": "Input", "IoAddress": 2305, "Description": "进料感应传感器"}, "BoardInPosition": {"Group": "X", "SubGroup": 1, "BitIndex": 0, "Name": "到位感应", "Type": "Input", "IoAddress": 2306, "Description": "板卡到位感应传感器"}, "OutgoingBoard": {"Group": "X", "SubGroup": 1, "BitIndex": 1, "Name": "出料感应", "Type": "Input", "IoAddress": 2306, "Description": "出料感应传感器"}}, "Actuators": {"StopperCylinder": {"Group": "Y", "SubGroup": 0, "BitIndex": 5, "Name": "阻挡气缸", "Type": "Output", "IoAddress": 2311, "Description": "阻挡气缸控制"}, "ClampCylinder": {"Group": "Y", "SubGroup": 0, "BitIndex": 7, "Name": "夹紧气缸", "Type": "Output", "IoAddress": 2311, "Description": "夹紧气缸控制"}}}}, "Cameras": [{"CameraName": "相机1", "CameraIndex": 0, "TriggerIo": {"IoAddress": 2312, "IoBitIndex": 7, "Name": "相机1触发", "Type": "Pulse", "PulseWidth": 100, "Description": "相机1拍照触发信号"}, "ReadyIo": {"IoAddress": 2312, "IoBitIndex": 1, "Name": "相机1就绪", "Type": "Input", "Description": "相机1就绪信号"}}], "Sensors": {"Safety": {"EmergencyStop": {"Group": "Z", "SubGroup": 0, "BitIndex": 5, "Name": "急停按钮", "Type": "NC", "Description": "设备紧急停止"}, "DoorSensor": {"Group": "X", "SubGroup": 0, "BitIndex": 3, "Name": "安全门", "Type": "NC", "Description": "安全门状态检测"}}, "Position": {"XHomeSwitch": {"Group": "X", "SubGroup": 0, "BitIndex": 4, "Name": "X轴原点", "Type": "NO"}, "YHomeSwitch": {"Group": "X", "SubGroup": 0, "BitIndex": 5, "Name": "Y轴原点", "Type": "NO"}, "ZHomeSwitch": {"Group": "X", "SubGroup": 0, "BitIndex": 6, "Name": "Z轴原点", "Type": "NO"}, "R1HomeSwitch": {"Group": "X", "SubGroup": 0, "BitIndex": 7, "Name": "R1轴原点", "Type": "NO"}, "R2HomeSwitch": {"Group": "X", "SubGroup": 1, "BitIndex": 0, "Name": "R2轴原点", "Type": "NO"}}, "Controls": {"StartButton": {"Group": "X", "SubGroup": 1, "BitIndex": 2, "Name": "开始按钮", "Type": "NO", "IoAddress": 2306, "Description": "开始运行按钮"}, "ResetButton": {"Group": "X", "SubGroup": 1, "BitIndex": 3, "Name": "复位按钮", "Type": "NO", "IoAddress": 2306, "Description": "系统复位按钮"}}}, "Smema": {"Upstream": {"BoardAvailable": {"Group": "Y", "SubGroup": 1, "BitIndex": 6, "Name": "本机有板", "Type": "Output", "IoAddress": 2312, "Description": "本机有板信号"}, "MachineReady": {"Group": "Y", "SubGroup": 1, "BitIndex": 5, "Name": "本机要板", "Type": "Output", "IoAddress": 2312, "Description": "本机要板信号"}}, "Downstream": {"MachineReady": {"Group": "X", "SubGroup": 1, "BitIndex": 7, "Name": "后机要板", "Type": "Input", "IoAddress": 2306, "Description": "后机要板信号"}}}, "Indicators": {"Lights": {"RedLight": {"Group": "Z", "SubGroup": 0, "BitIndex": 2, "Name": "红色指示灯", "Type": "Output", "IoAddress": 2317, "Description": "设备故障或报警状态"}, "GreenLight": {"Group": "Z", "SubGroup": 0, "BitIndex": 1, "Name": "绿色指示灯", "Type": "Output", "IoAddress": 2317, "Description": "设备正常运行状态"}, "YellowLight": {"Group": "Z", "SubGroup": 0, "BitIndex": 0, "Name": "黄色指示灯", "IoAddress": 2317, "Type": "Output", "Description": "设备待机或警告状态"}}, "Buzzer": {"Group": "Z", "SubGroup": 0, "BitIndex": 3, "Name": "蜂鸣器", "Type": "Output", "IoAddress": 2317, "Description": "设备报警提示声音"}}, "EnableOfflineSimulation": false, "OfflineSimulationDelayMs": 2000, "Alarms": {"ServoAlarms": {"XAxisServoAlarm": {"Group": "Z", "SubGroup": 0, "BitIndex": 4, "Name": "X轴伺服报警", "Type": "NC", "Description": "X轴伺服报警信号"}, "YAxisServoAlarm": {"Group": "X", "SubGroup": 0, "BitIndex": 5, "Name": "Y轴伺服报警", "Type": "NC", "Description": "Y轴伺服报警信号"}, "ZAxisServoAlarm": {"Group": "X", "SubGroup": 0, "BitIndex": 6, "Name": "Z轴伺服报警", "Type": "NC", "Description": "Z轴伺服报警信号"}, "EmergencyStopAlarms": {"Group": "Z", "SubGroup": 0, "BitIndex": 5, "Name": "急停按钮", "Type": "NC", "Description": "设备紧急停止"}}, "MotionAlarms": {"CardTotalError": {"BitIndex": 0, "Name": "控制卡总异常", "IoAddress": 17, "Type": "NO", "Description": "控制卡总异常, 1代表有异常"}, "XAxisHomeError": {"BitIndex": 0, "Name": "X轴复位异常", "Type": "NO", "IoAddress": 21, "Description": "X轴复位过程中,检测不到原点信号导致！"}, "XAxisMoveError": {"BitIndex": 1, "Name": "X轴移动异常", "Type": "NO", "IoAddress": 21, "Description": "X轴移动过程中碰触到到极限信号导致"}, "YAxisHomeError": {"BitIndex": 0, "Name": "Y轴复位异常", "Type": "NO", "IoAddress": 37, "Description": "Y轴复位过程中,检测不到原点信号导致！"}, "YAxisMoveError": {"BitIndex": 1, "Name": "Y轴移动异常", "Type": "NO", "IoAddress": 37, "Description": "Y轴移动过程中碰触到到极限信号导致"}, "ZAxisHomeError": {"BitIndex": 0, "Name": "Z轴复位异常", "Type": "NO", "IoAddress": 53, "Description": "Z轴复位过程中,检测不到原点信号导致！"}, "ZAxisMoveError": {"BitIndex": 1, "Name": "Z轴移动异常", "Type": "NO", "IoAddress": 53, "Description": "Z轴移动过程中碰触到到极限信号导致"}}}}